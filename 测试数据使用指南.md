# 测试数据使用指南

## 📊 测试数据概述

我为您创建了一个完整的测试数据集，包含30天的历史数据，用于测试网站统计系统的各种功能。

### 数据时间范围
- **开始日期**: 2024-12-01
- **结束日期**: 2025-01-02
- **总天数**: 33天
- **数据量**: 约3000-4500条访问记录

### 测试账户信息
| 用户名 | 邮箱 | 密码 | 角色 |
|--------|------|------|------|
| admin | <EMAIL> | password123 | 管理员 |
| testuser | <EMAIL> | password123 | 测试用户 |
| demo | <EMAIL> | password123 | 演示用户 |
| blogger | <EMAIL> | password123 | 博主 |
| ecommerce | <EMAIL> | password123 | 电商运营 |

## 🌐 测试网站数据

### 1. 技术博客 (techblog.com)
- **类型**: 个人技术博客
- **特点**: 高质量内容，用户停留时间长
- **主要页面**: 首页、Go教程、Vue3指南、React Hooks、关于页面
- **访客特征**: 技术人员，桌面端访问为主

### 2. 公司官网 (company.com)
- **类型**: 企业官网
- **特点**: 商务访问，转化率较高
- **主要页面**: 首页、产品介绍、解决方案、关于我们、联系我们
- **访客特征**: 商务人士，访问目的明确

### 3. 在线商城 (shop.com)
- **类型**: 电商网站
- **特点**: 高频访问，移动端比例高
- **主要页面**: 首页、电子产品、电脑数码、商品详情、购物车
- **访客特征**: 消费者，移动端访问较多

### 4. 新闻资讯 (news.com)
- **类型**: 新闻网站
- **特点**: 高频次短时间访问
- **主要页面**: 首页、科技新闻、人工智能、区块链
- **访客特征**: 信息获取者，快速浏览

### 5. 学习平台 (learn.com)
- **类型**: 在线教育
- **特点**: 长时间学习，高粘性
- **主要页面**: 首页、课程列表、JavaScript课程、Python课程
- **访客特征**: 学习者，停留时间长

### 6. 社区论坛 (forum.com)
- **类型**: 技术社区
- **特点**: 互动性强，回访率高
- **主要页面**: 首页、前端开发、后端开发、运维部署
- **访客特征**: 技术交流者，活跃度高

## 📈 数据特征和测试场景

### 访问量分布
- **日均访问量**: 50-150次访问
- **设备分布**: 70%桌面端，25%移动端，5%平板
- **时间分布**: 8:00-21:00，工作时间访问量较高
- **搜索来源**: 30%来自搜索引擎，70%直接访问

### 用户行为模式
- **新访客比例**: 30%
- **平均停留时间**: 1-10分钟
- **平均页面浏览数**: 1-8页
- **跳出率**: 约20-40%（根据网站类型）

### 热门关键词
1. Go语言教程 (156次)
2. JavaScript高级 (145次)
3. Vue3开发指南 (134次)
4. React入门 (128次)
5. TypeScript教程 (98次)
6. 网站性能优化 (92次)
7. Node.js开发 (87次)
8. 数据库设计 (83次)

## 🚀 执行测试数据

### 1. 导入测试数据
```bash
# 在命令行中执行
mysql -u root -p web_stats < comprehensive_test_data.sql

# 或者在phpMyAdmin的SQL标签页中执行
```

### 2. 验证数据导入
```sql
-- 查看数据统计
SELECT 
    '用户数' as item, COUNT(*) as count FROM users
UNION ALL
SELECT 
    '网站数' as item, COUNT(*) as count FROM sites
UNION ALL
SELECT 
    '访问记录数' as item, COUNT(*) as count FROM visits
UNION ALL
SELECT 
    '页面浏览数' as item, COUNT(*) as count FROM page_views;

-- 查看网站访问趋势
SELECT 
    s.site_name,
    COUNT(v.id) as total_visits,
    COUNT(DISTINCT v.visitor_id) as unique_visitors,
    AVG(v.visit_duration) as avg_duration
FROM sites s
LEFT JOIN visits v ON s.id = v.site_id
GROUP BY s.id, s.site_name
ORDER BY total_visits DESC;
```

## 🔍 测试功能场景

### 1. 用户登录测试
- 使用任意测试账户登录系统
- 验证权限控制和数据访问

### 2. 网站管理测试
- 查看网站列表
- 添加/编辑/删除网站
- 生成统计代码

### 3. 实时统计测试
- 查看当前在线访客
- 实时PV/UV统计
- 最近访问记录

### 4. 历史数据分析
- 30天访问趋势图
- 不同时间段对比
- 周环比、月环比分析

### 5. 搜索引擎分析
- 搜索引擎来源分布
- 热门关键词排行
- 搜索词云图

### 6. 设备和地区分析
- 设备类型分布
- 浏览器统计
- 地区访客分布

### 7. 页面分析
- 热门页面排行
- 页面停留时间
- 入口页面分析
- 退出页面分析

### 8. 用户行为分析
- 新老访客比例
- 访问深度分析
- 跳出率统计
- 回访频率

## 🧹 清理测试数据

### 完全清理（推荐）
```bash
# 删除所有测试数据，保留基础字典数据
mysql -u root -p web_stats < cleanup_test_data.sql
```

### 手动清理特定数据
```sql
-- 只清理访问记录，保留用户和网站
DELETE FROM visits WHERE visit_date >= '2024-12-01';
DELETE FROM page_views WHERE view_date >= '2024-12-01';
DELETE FROM stats_daily WHERE stat_date >= '2024-12-01';

-- 清理统计数据
TRUNCATE TABLE search_engine_stats;
TRUNCATE TABLE keyword_stats;
TRUNCATE TABLE region_stats;
TRUNCATE TABLE page_stats;
TRUNCATE TABLE device_stats;
TRUNCATE TABLE realtime_stats;
```

## 📋 测试检查清单

### 基础功能测试
- [ ] 用户注册/登录
- [ ] 网站添加/管理
- [ ] 统计代码生成
- [ ] 数据收集接口

### 统计功能测试
- [ ] 实时统计显示
- [ ] 历史数据查询
- [ ] 趋势图表展示
- [ ] 数据导出功能

### 分析功能测试
- [ ] 搜索引擎分析
- [ ] 关键词分析
- [ ] 地区分析
- [ ] 设备分析
- [ ] 页面分析

### 性能测试
- [ ] 大数据量查询性能
- [ ] 分区表查询效率
- [ ] 索引使用情况
- [ ] 并发访问测试

### 数据完整性测试
- [ ] 外键关系验证
- [ ] 数据一致性检查
- [ ] 统计数据准确性
- [ ] 实时数据同步

## 🎯 开发建议

### 1. 分阶段开发
1. **第一阶段**: 基础用户管理和网站管理
2. **第二阶段**: 数据收集API和基础统计
3. **第三阶段**: 高级分析和图表展示
4. **第四阶段**: 性能优化和扩展功能

### 2. 测试驱动开发
- 使用测试数据验证每个功能
- 编写单元测试和集成测试
- 性能基准测试

### 3. 数据验证
- 定期检查数据完整性
- 验证统计数据准确性
- 监控系统性能指标

现在您有了完整的测试数据集，可以开始全面测试网站统计系统的各项功能了！
