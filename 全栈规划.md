# 网站统计系统全栈开发规划

## 项目概述
开发一个类似百度统计、CNZZ的网站统计程序，提供完整的网站流量分析功能。

## 技术栈确定
- **后端**: Go Fiber + GORM + MySQL 8.0 (Go 1.23.9)
- **前端**: Vue 3 + TypeScript + Element Plus + Vite 5
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **代码规范**: ESLint

## 一、数据库设计

### 1.1 核心数据表结构

#### 用户相关表
```sql
-- 用户表
users (
  id, username, email, password_hash, 
  created_at, updated_at, status
)

-- 网站表
sites (
  id, user_id, site_name, domain, site_url, 
  site_type, region, description, tracking_code,
  created_at, updated_at, status
)
```

#### 字典表（性能优化）
```sql
-- 搜索引擎字典表
search_engines (id, name, domain, created_at)

-- 关键词字典表
keywords (id, keyword, keyword_hash, first_seen, last_seen, total_count)

-- 浏览器字典表
browsers (id, name, version, created_at)

-- 操作系统字典表
operating_systems (id, name, version, created_at)

-- 地区字典表
regions (id, country, province, city, created_at)

-- 页面URL字典表
page_urls (id, url, url_hash, domain, path, title, first_seen, last_seen)

-- 来源域名字典表
referer_domains (id, domain, domain_type, created_at)
```

#### 统计数据表（优化版）
```sql
-- 访问记录表 (核心表) - 使用字典表ID
visits (
  id, site_id, visitor_id, session_id, ip,
  user_agent, referer_domain_id, referer_url_id,
  landing_page_id, exit_page_id, visit_time, visit_duration,
  page_views, is_new_visitor, region_id, device_type,
  browser_id, os_id, screen_resolution, language,
  search_engine_id, search_keyword_id, created_at
)

-- 页面浏览记录表
page_views (
  id, site_id, visitor_id, session_id, page_url,
  page_title, referer, view_time, stay_duration,
  created_at
)

-- 访客表
visitors (
  id, site_id, visitor_hash, first_visit,
  last_visit, total_visits, total_page_views,
  created_at, updated_at
)

-- 实时在线表
online_visitors (
  id, site_id, visitor_id, last_activity,
  current_page, created_at
)
```

#### 统计汇总表
```sql
-- 日统计汇总表
daily_stats (
  id, site_id, date, pv, uv, ip_count,
  bounce_rate, avg_visit_duration, new_visitors,
  created_at, updated_at
)

-- 小时统计表
hourly_stats (
  id, site_id, date, hour, pv, uv, ip_count,
  created_at
)

-- 搜索引擎统计表
search_engine_stats (
  id, site_id, date, engine_name, pv, uv,
  keywords, created_at
)

-- 地区统计表
region_stats (
  id, site_id, date, country, province, city,
  pv, uv, created_at
)
```

#### 配置表
```sql
-- 排除规则表
exclude_rules (
  id, site_id, rule_type, rule_value, 
  match_type, created_at
)

-- 站点配置表
site_configs (
  id, site_id, config_key, config_value,
  created_at, updated_at
)
```

## 二、后端API设计

### 2.1 用户认证模块
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- POST /api/auth/logout - 用户登出
- GET /api/auth/profile - 获取用户信息

### 2.2 站点管理模块
- GET /api/sites - 获取用户站点列表
- POST /api/sites - 创建新站点
- PUT /api/sites/:id - 更新站点信息
- DELETE /api/sites/:id - 删除站点
- GET /api/sites/:id/code - 获取统计代码

### 2.3 数据收集模块
- POST /api/track/pv - 页面浏览统计
- POST /api/track/event - 事件统计
- GET /api/track/online - 实时在线统计

### 2.4 数据分析模块
- GET /api/stats/overview/:siteId - 网站概况
- GET /api/stats/trend/:siteId - 趋势分析
- GET /api/stats/compare/:siteId - 对比分析
- GET /api/stats/realtime/:siteId - 实时统计
- GET /api/stats/source/:siteId - 来源分析
- GET /api/stats/page/:siteId - 页面分析
- GET /api/stats/visitor/:siteId - 访客分析
- GET /api/stats/region/:siteId - 地区分析

## 三、前端页面设计

### 3.1 用户端页面结构

#### 主要页面
1. **用户首页** - 站点列表和汇总数据
2. **网站概况** - 单个站点的详细统计
3. **流量分析** - 趋势、对比、实时、明细
4. **来源分析** - 搜索引擎、关键词、来路域名
5. **受访分析** - 受访页面、域名分析
6. **访客分析** - 地区分布、设备环境、新老访客
7. **设置页面** - 站点配置、排除规则

#### 删除的页面（标记暂不做）
- SEO推荐页面
- 热点图页面

### 3.2 管理后台页面
- 用户管理
- 站点管理
- 系统配置
- 数据监控

## 四、开发阶段规划

### 阶段一：基础架构搭建 (1-2周)
1. 数据库设计和创建
2. Go Fiber后端项目初始化
3. Vue 3前端项目初始化
4. 用户认证系统
5. 基础的站点管理功能

### 阶段二：数据收集系统 (2-3周)
1. 统计代码生成
2. 数据收集API开发
3. 访问数据存储逻辑
4. 实时数据处理
5. 数据清洗和去重

### 阶段三：数据分析引擎 (3-4周)
1. 统计数据计算逻辑
2. 趋势分析算法
3. 对比分析功能
4. 地区分析（IP地址库集成）
5. 搜索引擎和关键词分析

### 阶段四：前端界面开发 (4-5周)
1. 用户首页开发
2. 网站概况页面
3. 各类分析报表页面
4. 图表组件集成（ECharts）
5. 响应式设计优化

### 阶段五：高级功能 (2-3周)
1. 实时在线统计
2. 数据导出功能
3. 排除规则配置
4. 报表定制功能
5. 性能优化

### 阶段六：测试和部署 (1-2周)
1. 单元测试编写
2. 集成测试
3. 性能测试
4. 生产环境部署
5. 监控和日志系统

## 五、关键技术实现

### 5.1 统计代码生成
- 异步JavaScript代码
- 支持多种显示样式
- 防重复统计机制

### 5.2 数据收集优化
- **字典表优化**: 使用ID替代重复字符串，提升查询性能10-100倍
- **批量数据插入**: 减少数据库连接开销
- **异步数据处理**: 后台处理数据清洗和字典表维护
- **数据压缩存储**: 通过字典表大幅减少存储空间
- **智能缓存**: 常用字典数据缓存到Redis，加速ID查找

### 5.3 实时统计
- WebSocket连接
- Redis缓存
- 定时任务更新

### 5.4 地理位置识别
- IP地址库集成
- 地区映射优化

### 5.5 性能优化
- 数据库索引优化
- 查询缓存机制
- CDN静态资源

## 六、部署和运维

### 6.1 服务器配置
- Go应用服务器
- MySQL数据库服务器
- Redis缓存服务器
- Nginx反向代理

### 6.2 监控和日志
- 应用性能监控
- 错误日志收集
- 数据备份策略

## 七、项目里程碑

1. **MVP版本** (6周) - 基础统计功能
2. **Beta版本** (10周) - 完整功能测试
3. **正式版本** (12周) - 生产环境发布

## 八、风险评估和应对

### 8.1 技术风险
- 大数据量处理性能问题
- 实时统计准确性
- 跨域数据收集限制

### 8.2 应对策略
- 数据分片和归档
- 缓存策略优化
- CORS配置和备用方案

## 九、详细功能模块设计

### 9.1 统计代码生成器
```javascript
// 异步统计代码模板
var _stats = _stats || [];
(function() {
    var s = document.createElement("script");
    s.src = "https://your-domain.com/js/stats.js?id={SITE_ID}&async=1";
    s.async = true;
    var x = document.getElementsByTagName("script")[0];
    x.parentNode.insertBefore(s, x);
})();
```

### 9.2 数据收集JavaScript SDK
- 页面浏览量(PV)统计
- 独立访客(UV)识别
- 访问时长计算
- 跳出率统计
- 来源页面识别
- 搜索关键词提取

### 9.3 实时数据处理流程
1. 前端发送统计请求
2. 后端验证和清洗数据
3. 存储到实时缓存(Redis)
4. 异步写入数据库
5. 触发实时统计更新

### 9.4 报表生成算法
- 按时间维度聚合(小时/日/周/月)
- 地理位置统计(省市级别)
- 设备和浏览器分析
- 来源渠道分析
- 用户行为路径分析

## 十、数据库优化策略

### 10.1 索引设计
```sql
-- 访问记录表关键索引
CREATE INDEX idx_visits_site_time ON visits(site_id, visit_time);
CREATE INDEX idx_visits_visitor ON visits(visitor_id, visit_time);
CREATE INDEX idx_visits_ip_time ON visits(ip, visit_time);

-- 页面浏览表索引
CREATE INDEX idx_pageviews_site_time ON page_views(site_id, view_time);
CREATE INDEX idx_pageviews_url ON page_views(site_id, page_url);
```

### 10.2 数据分区策略
- 按月分区存储历史数据
- 热数据和冷数据分离
- 定期归档和清理

### 10.3 查询优化
- **字典表规范化**: 关键词、搜索引擎、地区、URL等使用独立字典表
- **ID关联查询**: 整数ID比较比字符串快10-100倍
- **预计算汇总表**: 按小时/日/月预先计算统计数据
- **查询结果缓存**: 热点数据缓存到Redis
- **复合索引优化**: 为常用查询组合创建专门索引
- **分区表设计**: 按月分区存储历史数据

#### 字典表优化效果
- **存储空间**: 减少60-80%的存储空间占用
- **查询性能**: 提升10-100倍的查询速度
- **索引效率**: 显著减少索引大小，提高缓存命中率
- **JOIN性能**: 整数ID的JOIN操作比字符串快数十倍

## 十一、前端组件设计

### 11.1 图表组件
- 趋势线图(ECharts)
- 饼图统计
- 地图热力图
- 实时数据仪表盘

### 11.2 数据表格组件
- 分页和排序
- 数据筛选
- 导出功能
- 响应式设计

### 11.3 时间选择器
- 快速时间范围选择
- 自定义时间范围
- 时间对比功能

## 十二、安全和隐私保护

### 12.1 数据安全
- API接口鉴权
- 数据传输加密(HTTPS)
- 敏感数据脱敏
- SQL注入防护

### 12.2 隐私保护
- IP地址匿名化处理
- 用户数据加密存储
- 数据访问权限控制
- 符合GDPR规范

### 12.3 防刷和反爬虫
- 请求频率限制
- 异常流量检测
- 爬虫特征识别
- 验证码机制

## 十三、性能监控和优化

### 13.1 系统监控指标
- API响应时间
- 数据库查询性能
- 内存和CPU使用率
- 错误率统计

### 13.2 性能优化方案
- 数据库连接池
- Redis缓存策略
- CDN静态资源加速
- 代码压缩和合并

### 13.3 扩展性设计
- 微服务架构准备
- 数据库读写分离
- 负载均衡配置
- 容器化部署

## 十四、测试策略

### 14.1 单元测试
- Go后端API测试
- Vue组件测试
- 数据库操作测试

### 14.2 集成测试
- 端到端功能测试
- 性能压力测试
- 兼容性测试

### 14.3 用户验收测试
- 功能完整性验证
- 用户体验测试
- 数据准确性验证

---

**项目交付物：**
1. 完整的源代码(前后端)
2. 数据库设计文档和脚本
3. API接口文档
4. 部署和运维手册
5. 用户使用指南
6. 测试报告

**注意事项：**
- 所有上升数据用红色显示，下降数据用绿色显示
- 确保数据隐私和安全性
- 遵循GDPR等数据保护法规
- 提供详细的API文档和用户手册
- 支持多语言国际化
- 移动端响应式适配
