#!/bin/bash

# 网站统计系统后端启动脚本

echo "=== 网站统计系统后端启动 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: Go环境未安装"
    exit 1
fi

echo "Go版本: $(go version)"

# 进入后端目录
cd backend

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "警告: .env文件不存在，使用默认配置"
    echo "建议复制.env.example为.env并配置数据库连接"
fi

# 安装依赖
echo "安装Go依赖..."
go mod tidy

# 编译项目
echo "编译项目..."
go build -o ../build/web-stats-backend ./cmd/server

if [ $? -eq 0 ]; then
    echo "编译成功!"
    
    # 启动服务
    echo "启动后端服务..."
    echo "服务将在端口9002启动"
    echo "API文档: http://localhost:9002/api/v1/health"
    echo "按Ctrl+C停止服务"
    echo ""
    
    ../build/web-stats-backend
else
    echo "编译失败!"
    exit 1
fi
