-- 网站统计系统数据库初始化脚本（简化版）
-- 避免权限问题，适用于受限的MySQL环境
-- 创建时间: 2025-01-02

-- 使用数据库
USE web_stats;

-- ============================================================================
-- 字典表设计（性能优化核心）
-- ============================================================================

-- 搜索引擎字典表
CREATE TABLE search_engines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '搜索引擎名称',
    domain VARCHAR(100) COMMENT '搜索引擎域名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎字典表';

-- 关键词字典表
CREATE TABLE keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(200) UNIQUE NOT NULL COMMENT '关键词',
    keyword_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '关键词哈希值',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_count INT DEFAULT 1 COMMENT '总出现次数',
    INDEX idx_keyword (keyword),
    INDEX idx_keyword_hash (keyword_hash),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词字典表';

-- 浏览器字典表
CREATE TABLE browsers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '浏览器名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_browser_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览器字典表';

-- 操作系统字典表
CREATE TABLE operating_systems (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '操作系统名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_os_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作系统字典表';

-- 地区字典表
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country VARCHAR(50) NOT NULL COMMENT '国家',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_region (country, province, city),
    INDEX idx_country (country),
    INDEX idx_province (province),
    INDEX idx_city (city)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区字典表';

-- 页面URL字典表
CREATE TABLE page_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(500) UNIQUE NOT NULL COMMENT '页面URL',
    url_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'URL哈希值',
    domain VARCHAR(100) COMMENT '域名',
    path VARCHAR(400) COMMENT '路径',
    title VARCHAR(200) COMMENT '页面标题',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    INDEX idx_url_hash (url_hash),
    INDEX idx_domain (domain),
    INDEX idx_path (path(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面URL字典表';

-- 来源域名字典表
CREATE TABLE referer_domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(100) UNIQUE NOT NULL COMMENT '来源域名',
    domain_type ENUM('search_engine', 'social_media', 'direct', 'external') DEFAULT 'external' COMMENT '域名类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_domain_type (domain_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名字典表';

-- 会话字典表
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '会话哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_hash (session_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话字典表';

-- IP地址字典表
CREATE TABLE ip_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_int INT UNSIGNED COMMENT 'IPv4地址的整数表示',
    ip_string VARCHAR(45) COMMENT '原始IP字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_int (ip_int),
    INDEX idx_ip_string (ip_string)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP地址字典表';

-- User Agent字典表
CREATE TABLE user_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ua_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'User Agent哈希值',
    user_agent TEXT COMMENT '完整User Agent字符串',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ua_hash (ua_hash),
    INDEX idx_browser_os (browser_id, os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User Agent字典表';

-- ============================================================================
-- 用户管理相关表
-- ============================================================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 网站表
CREATE TABLE sites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    site_name VARCHAR(100) NOT NULL COMMENT '网站名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '网站URL',
    site_type VARCHAR(50) DEFAULT 'other' COMMENT '网站类型',
    region VARCHAR(50) COMMENT '网站地区',
    description TEXT COMMENT '网站描述',
    tracking_code VARCHAR(32) UNIQUE NOT NULL COMMENT '统计代码ID',
    view_password VARCHAR(50) COMMENT '查看密码',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_tracking_code (tracking_code),
    INDEX idx_domain (domain),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';

-- 访客表
CREATE TABLE visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_hash VARCHAR(64) NOT NULL COMMENT '访客唯一标识',
    first_visit TIMESTAMP NOT NULL COMMENT '首次访问时间',
    last_visit TIMESTAMP NOT NULL COMMENT '最后访问时间',
    total_visits INT DEFAULT 1 COMMENT '总访问次数',
    total_page_views INT DEFAULT 1 COMMENT '总页面浏览数',
    total_duration INT DEFAULT 0 COMMENT '总访问时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_site_visitor (site_id, visitor_hash),
    INDEX idx_site_id (site_id),
    INDEX idx_last_visit (last_visit)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访客表';

-- ============================================================================
-- 核心访问记录表（分区表）
-- ============================================================================

-- 访问记录表（修复版 - 复合主键包含分区键）
CREATE TABLE visits (
    id BIGINT AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    ip_id INT NOT NULL COMMENT 'IP地址ID',
    user_agent_id INT COMMENT '用户代理ID',
    referer_domain_id INT COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    landing_page_id BIGINT NOT NULL COMMENT '入口页面ID',
    exit_page_id BIGINT COMMENT '退出页面ID',

    -- 时间字段优化
    visit_time TIMESTAMP NOT NULL COMMENT '访问时间',
    visit_date DATE NOT NULL COMMENT '访问日期',
    visit_hour TINYINT NOT NULL COMMENT '访问小时（0-23）',
    visit_year SMALLINT NOT NULL COMMENT '访问年份',
    visit_month TINYINT NOT NULL COMMENT '访问月份（1-12）',
    visit_week TINYINT NOT NULL COMMENT '访问周数（1-53）',
    visit_weekday TINYINT NOT NULL COMMENT '星期几（1-7）',

    visit_duration INT DEFAULT 0 COMMENT '访问时长(秒)',
    page_views INT DEFAULT 1 COMMENT '页面浏览数',
    is_new_visitor TINYINT DEFAULT 0 COMMENT '是否新访客',
    region_id INT COMMENT '地区ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    language VARCHAR(10) COMMENT '语言',
    search_engine_id INT COMMENT '搜索引擎ID',
    search_keyword_id BIGINT COMMENT '搜索关键词ID',
    utm_source VARCHAR(100) COMMENT 'UTM来源',
    utm_medium VARCHAR(100) COMMENT 'UTM媒介',
    utm_campaign VARCHAR(100) COMMENT 'UTM活动',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 复合主键（包含分区键）
    PRIMARY KEY (id, visit_year, visit_month),

    -- 优化索引
    INDEX idx_site_date (site_id, visit_date),
    INDEX idx_site_date_hour (site_id, visit_date, visit_hour),
    INDEX idx_visitor_date (visitor_id, visit_date),
    INDEX idx_session_date (session_id, visit_date),
    INDEX idx_analytics_basic (site_id, visit_date, device_type, is_new_visitor),
    INDEX idx_analytics_source (site_id, visit_date, search_engine_id),
    INDEX idx_covering_basic (site_id, visit_date, visitor_id, page_views, visit_duration)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问记录表'
PARTITION BY RANGE (visit_year * 100 + visit_month) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 页面浏览记录表
CREATE TABLE page_views (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    view_time TIMESTAMP NOT NULL COMMENT '浏览时间',
    view_date DATE NOT NULL COMMENT '浏览日期',
    view_hour TINYINT NOT NULL COMMENT '浏览小时',
    stay_duration INT DEFAULT 0 COMMENT '停留时长(秒)',
    scroll_depth INT DEFAULT 0 COMMENT '滚动深度百分比',
    is_bounce TINYINT DEFAULT 0 COMMENT '是否跳出',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_site_date (site_id, view_date),
    INDEX idx_visitor_date (visitor_id, view_date),
    INDEX idx_page_url (site_id, page_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面浏览记录表';

-- ============================================================================
-- 预聚合统计表
-- ============================================================================

-- 日级预聚合表
CREATE TABLE stats_daily (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    pv INT DEFAULT 0,
    uv INT DEFAULT 0,
    ip_count INT DEFAULT 0,
    session_count INT DEFAULT 0,
    new_visitors INT DEFAULT 0,
    returning_visitors INT DEFAULT 0,
    bounce_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,
    avg_duration DECIMAL(8,2) DEFAULT 0,
    avg_page_views DECIMAL(5,2) DEFAULT 0,
    desktop_pv INT DEFAULT 0,
    mobile_pv INT DEFAULT 0,
    tablet_pv INT DEFAULT 0,
    search_pv INT DEFAULT 0,
    direct_pv INT DEFAULT 0,
    external_pv INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_site_date (site_id, stat_date),
    INDEX idx_site_date (site_id, stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日级预聚合表';

-- 实时统计表
CREATE TABLE realtime_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    time_slot TIMESTAMP NOT NULL COMMENT '时间槽（每5分钟）',
    pv_5min INT DEFAULT 0,
    uv_5min INT DEFAULT 0,
    online_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_site_time (site_id, time_slot),
    INDEX idx_time_slot (time_slot)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时统计表';

-- ============================================================================
-- 初始化数据
-- ============================================================================

-- 插入默认搜索引擎数据
INSERT INTO search_engines (name, domain) VALUES
('百度', 'baidu.com'),
('Google', 'google.com'),
('360搜索', '360.cn'),
('搜狗', 'sogou.com'),
('神马搜索', 'sm.cn'),
('夸克搜索', 'quark.cn'),
('头条搜索', 'toutiao.com'),
('Bing', 'bing.com'),
('Yahoo', 'yahoo.com'),
('DuckDuckGo', 'duckduckgo.com');

-- 插入默认浏览器数据
INSERT INTO browsers (name, version) VALUES
('Chrome', '120.0'),
('Firefox', '119.0'),
('Safari', '17.0'),
('Edge', '119.0'),
('Opera', '105.0'),
('IE', '11.0');

-- 插入默认操作系统数据
INSERT INTO operating_systems (name, version) VALUES
('Windows', '11'),
('Windows', '10'),
('macOS', '14.0'),
('macOS', '13.0'),
('Linux', 'Ubuntu 22.04'),
('iOS', '17.0'),
('Android', '14.0'),
('Android', '13.0');

-- 插入默认地区数据
INSERT INTO regions (country, province, city) VALUES
('中国', '北京市', '北京市'),
('中国', '上海市', '上海市'),
('中国', '广东省', '广州市'),
('中国', '广东省', '深圳市'),
('中国', '浙江省', '杭州市'),
('中国', '江苏省', '南京市'),
('中国', '四川省', '成都市'),
('中国', '湖北省', '武汉市'),
('中国', '陕西省', '西安市'),
('中国', '山东省', '青岛市');

-- 插入默认来源域名数据
INSERT INTO referer_domains (domain, domain_type) VALUES
('baidu.com', 'search_engine'),
('google.com', 'search_engine'),
('360.cn', 'search_engine'),
('sogou.com', 'search_engine'),
('weibo.com', 'social_media'),
('qq.com', 'social_media'),
('wechat.com', 'social_media'),
('zhihu.com', 'social_media'),
('douyin.com', 'social_media'),
('bilibili.com', 'social_media');

-- ============================================================================
-- 完成提示
-- ============================================================================

-- 显示创建结果
SELECT 'Database tables created successfully!' as status;
SELECT 'visits table created with partition support' as note;
SELECT 'Data integrity maintained at application level' as important;
