package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"web-stats-backend/internal/config"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()
	log.Printf("Starting %s v%s in %s mode", cfg.App.Name, cfg.App.Version, cfg.App.Env)

	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		AppName:      cfg.App.Name,
		ServerHeader: cfg.App.Name,
	})

	// 基本中间件
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// 基本路由
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Web Statistics Backend API",
			"version": cfg.App.Version,
			"status":  "running",
		})
	})

	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "healthy",
			"version": cfg.App.Version,
		})
	})

	// API路由组
	api := app.Group("/api")

	// 测试路由
	api.Get("/test", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "API is working!",
			"time":    fmt.Sprintf("%v", os.Getenv("TZ")),
		})
	})

	// 认证测试路由
	auth := api.Group("/auth")
	auth.Post("/register", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Register endpoint - TODO: implement with database",
		})
	})

	auth.Post("/login", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Login endpoint - TODO: implement with database",
		})
	})

	// 网站管理测试路由
	sites := api.Group("/sites")
	sites.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Sites list endpoint - TODO: implement with database",
			"sites":   []interface{}{},
		})
	})

	sites.Post("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Create site endpoint - TODO: implement with database",
		})
	})

	// 统计数据测试路由
	analytics := api.Group("/analytics")
	analytics.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Analytics endpoint - TODO: implement with database",
			"data":    []interface{}{},
		})
	})

	// 数据收集测试路由
	collect := api.Group("/collect")
	collect.Post("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Data collection endpoint - TODO: implement with database",
			"status":  "received",
		})
	})

	// 404处理
	app.Use(func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error":   "Not Found",
			"message": "The requested resource was not found",
		})
	})

	// 优雅关闭
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		<-c

		log.Println("Shutting down server...")
		if err := app.Shutdown(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}

		log.Println("Server stopped")
		os.Exit(0)
	}()

	// 启动服务器
	log.Printf("Server starting on %s", cfg.GetServerAddress())
	if err := app.Listen(fmt.Sprintf(":%d", cfg.Server.Port)); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
