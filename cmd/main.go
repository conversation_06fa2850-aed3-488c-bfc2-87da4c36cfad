package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"web-stats-backend/internal/config"
	"web-stats-backend/internal/database"
	"web-stats-backend/internal/handlers"
	"web-stats-backend/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()
	log.Printf("Starting %s v%s in %s mode", cfg.App.Name, cfg.App.Version, cfg.App.Env)

	// 初始化数据库
	if err := database.InitDatabase(cfg); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建索引
	if err := database.CreateIndexes(); err != nil {
		log.Printf("Warning: Failed to create indexes: %v", err)
	}

	// 初始化种子数据
	if err := database.SeedData(); err != nil {
		log.Printf("Warning: Failed to seed data: %v", err)
	}

	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		ErrorHandler: middleware.ErrorHandler(),
		AppName:      cfg.App.Name,
		ServerHeader: cfg.App.Name,
	})

	// 设置中间件
	middleware.SetupMiddleware(app, cfg)

	// 设置路由
	setupRoutes(app)

	// 优雅关闭
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		<-c

		log.Println("Shutting down server...")
		if err := app.Shutdown(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}

		// 关闭数据库连接
		if err := database.CloseDatabase(); err != nil {
			log.Printf("Error closing database: %v", err)
		}

		log.Println("Server stopped")
		os.Exit(0)
	}()

	// 启动服务器
	log.Printf("Server starting on %s", cfg.GetServerAddress())
	if err := app.Listen(fmt.Sprintf(":%d", cfg.Server.Port)); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// setupRoutes 设置路由
func setupRoutes(app *fiber.App) {
	// 健康检查
	app.Get("/health", func(c *fiber.Ctx) error {
		// 检查数据库连接
		if err := database.HealthCheck(); err != nil {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"status":   "unhealthy",
				"database": "disconnected",
				"error":    err.Error(),
			})
		}

		return c.JSON(fiber.Map{
			"status":   "healthy",
			"database": "connected",
			"version":  config.GlobalConfig.App.Version,
		})
	})

	// API路由组
	api := app.Group("/api")

	// 认证路由
	authHandler := handlers.NewAuthHandler()
	auth := api.Group("/auth")
	{
		auth.Post("/register", authHandler.Register)
		auth.Post("/login", authHandler.Login)
		auth.Post("/logout", middleware.AuthRequired(), authHandler.Logout)
		auth.Get("/me", middleware.AuthRequired(), authHandler.Me)
		auth.Put("/profile", middleware.AuthRequired(), authHandler.UpdateProfile)
	}

	// 网站管理路由
	sitesHandler := handlers.NewSitesHandler()
	sites := api.Group("/sites", middleware.AuthRequired())
	{
		sites.Get("/", sitesHandler.GetSites)
		sites.Post("/", sitesHandler.CreateSite)
		sites.Get("/:id", sitesHandler.GetSite)
		sites.Put("/:id", sitesHandler.UpdateSite)
		sites.Delete("/:id", sitesHandler.DeleteSite)
		sites.Post("/:id/regenerate-code", sitesHandler.RegenerateTrackingCode)
		sites.Get("/:id/tracking-code", sitesHandler.GetTrackingCode)
	}

	// 统计数据路由
	analyticsHandler := handlers.NewAnalyticsHandler()
	analytics := api.Group("/analytics", middleware.AuthRequired())
	{
		analytics.Get("/sites/:id/stats", analyticsHandler.GetSiteStats)
		analytics.Get("/sites/:id/daily", analyticsHandler.GetDailyStats)
	}

	// 公开的统计数据收集路由
	collectHandler := handlers.NewCollectHandler()
	collect := api.Group("/collect")
	{
		collect.Post("/", collectHandler.CollectData)
		collect.Get("/visitor-id", collectHandler.GenerateVisitorID)
		collect.Get("/session-id", collectHandler.GenerateSessionID)
	}

	// 静态文件服务（用于前端）
	app.Static("/", "./web", fiber.Static{
		Index: "index.html",
	})

	// 404处理
	app.Use(func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error":   "Not Found",
			"message": "The requested resource was not found",
		})
	})
}
