-- 网站统计系统优化测试数据
-- 基于优化后的数据库结构创建测试数据
-- 创建时间: 2025-01-02

USE web_stats;

-- ============================================================================
-- 测试用户和网站数据
-- ============================================================================

-- 插入测试用户
INSERT INTO users (username, email, password_hash, nickname, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', 'admin', 1, 1),
('testuser1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户1', 'user', 1, 1),
('testuser2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户2', 'user', 1, 1),
('demo', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '演示用户', 'user', 1, 1);

-- 插入测试网站
INSERT INTO sites (user_id, site_name, domain, site_url, site_type, category, region, description, tracking_code, status) VALUES
(2, '测试博客', 'blog.example.com', 'https://blog.example.com', 'blog', 'blog', '北京市', '个人技术博客', 'TRACK001', 1),
(2, '电商网站', 'shop.example.com', 'https://shop.example.com', 'ecommerce', 'ecommerce', '上海市', '在线购物网站', 'TRACK002', 1),
(3, '新闻门户', 'news.example.com', 'https://news.example.com', 'news', 'news', '广州市', '科技新闻网站', 'TRACK003', 1),
(3, '企业官网', 'company.example.com', 'https://company.example.com', 'corporate', 'corporate', '深圳市', '公司官方网站', 'TRACK004', 1),
(4, '演示网站', 'demo.example.com', 'https://demo.example.com', 'demo', 'other', '杭州市', '功能演示网站', 'TRACK005', 1);

-- ============================================================================
-- 测试页面URL数据
-- ============================================================================

-- 插入测试页面URL
INSERT INTO page_urls (url, url_hash, domain, path, title, page_type) VALUES
('https://blog.example.com/', MD5('https://blog.example.com/'), 'blog.example.com', '/', '首页 - 测试博客', 'page'),
('https://blog.example.com/about', MD5('https://blog.example.com/about'), 'blog.example.com', '/about', '关于我们 - 测试博客', 'page'),
('https://blog.example.com/posts/tech-1', MD5('https://blog.example.com/posts/tech-1'), 'blog.example.com', '/posts/tech-1', 'Go语言入门教程', 'page'),
('https://blog.example.com/posts/tech-2', MD5('https://blog.example.com/posts/tech-2'), 'blog.example.com', '/posts/tech-2', 'Vue3开发指南', 'page'),
('https://blog.example.com/contact', MD5('https://blog.example.com/contact'), 'blog.example.com', '/contact', '联系我们', 'page'),

('https://shop.example.com/', MD5('https://shop.example.com/'), 'shop.example.com', '/', '首页 - 在线商城', 'page'),
('https://shop.example.com/products', MD5('https://shop.example.com/products'), 'shop.example.com', '/products', '商品列表', 'page'),
('https://shop.example.com/products/phone-1', MD5('https://shop.example.com/products/phone-1'), 'shop.example.com', '/products/phone-1', 'iPhone 15 Pro', 'page'),
('https://shop.example.com/cart', MD5('https://shop.example.com/cart'), 'shop.example.com', '/cart', '购物车', 'page'),
('https://shop.example.com/checkout', MD5('https://shop.example.com/checkout'), 'shop.example.com', '/checkout', '结算页面', 'page'),

('https://news.example.com/', MD5('https://news.example.com/'), 'news.example.com', '/', '科技新闻首页', 'page'),
('https://news.example.com/tech', MD5('https://news.example.com/tech'), 'news.example.com', '/tech', '科技频道', 'page'),
('https://news.example.com/ai', MD5('https://news.example.com/ai'), 'news.example.com', '/ai', 'AI资讯', 'page'),
('https://news.example.com/mobile', MD5('https://news.example.com/mobile'), 'news.example.com', '/mobile', '移动互联网', 'page');

-- ============================================================================
-- 测试关键词数据
-- ============================================================================

-- 插入测试关键词
INSERT INTO keywords (keyword, keyword_hash, keyword_length, total_count) VALUES
('Go语言教程', MD5('Go语言教程'), 6, 150),
('Vue3开发', MD5('Vue3开发'), 5, 120),
('网站统计', MD5('网站统计'), 4, 200),
('数据分析', MD5('数据分析'), 4, 180),
('前端开发', MD5('前端开发'), 4, 300),
('后端开发', MD5('后端开发'), 4, 250),
('MySQL优化', MD5('MySQL优化'), 6, 80),
('性能监控', MD5('性能监控'), 4, 90),
('用户体验', MD5('用户体验'), 4, 160),
('移动开发', MD5('移动开发'), 4, 140);

-- ============================================================================
-- 测试访客数据
-- ============================================================================

-- 插入测试访客（使用存储过程批量生成）
DELIMITER //
CREATE PROCEDURE GenerateTestVisitors()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE site_id_val BIGINT;
    DECLARE visitor_hash_val VARCHAR(64);
    DECLARE first_visit_val TIMESTAMP;
    DECLARE last_visit_val TIMESTAMP;
    
    WHILE i <= 1000 DO
        SET site_id_val = (i % 5) + 1;
        SET visitor_hash_val = MD5(CONCAT('visitor_', i, '_', site_id_val));
        SET first_visit_val = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY);
        SET last_visit_val = DATE_ADD(first_visit_val, INTERVAL FLOOR(RAND() * 24) HOUR);
        
        INSERT INTO visitors (
            site_id, visitor_hash, first_visit, last_visit, 
            total_visits, total_page_views, total_duration,
            last_region_id, last_device_type
        ) VALUES (
            site_id_val, visitor_hash_val, first_visit_val, last_visit_val,
            FLOOR(RAND() * 10) + 1,
            FLOOR(RAND() * 50) + 1,
            FLOOR(RAND() * 3600) + 60,
            FLOOR(RAND() * 16) + 1,
            CASE FLOOR(RAND() * 3)
                WHEN 0 THEN 'desktop'
                WHEN 1 THEN 'mobile'
                ELSE 'tablet'
            END
        );
        
        SET i = i + 1;
    END WHILE;
END //
DELIMITER ;

-- 执行存储过程生成测试访客
CALL GenerateTestVisitors();

-- 删除存储过程
DROP PROCEDURE GenerateTestVisitors;

-- ============================================================================
-- 测试会话数据
-- ============================================================================

-- 插入测试会话
INSERT INTO sessions (session_hash, expires_at) 
SELECT 
    MD5(CONCAT('session_', id, '_', UNIX_TIMESTAMP())),
    DATE_ADD(NOW(), INTERVAL 30 MINUTE)
FROM visitors 
LIMIT 500;

-- ============================================================================
-- 测试IP地址数据
-- ============================================================================

-- 插入测试IP地址
INSERT INTO ip_addresses (ip_int, ip_string, ip_version, is_internal, is_bot) VALUES
(INET_ATON('*************'), '*************', 4, 1, 0),
(INET_ATON('*********'), '*********', 4, 1, 0),
(INET_ATON('***********'), '***********', 4, 1, 0),
(INET_ATON('*******'), '*******', 4, 0, 0),
(INET_ATON('***************'), '***************', 4, 0, 0),
(INET_ATON('*********'), '*********', 4, 0, 0),
(INET_ATON('************'), '************', 4, 0, 1),
(INET_ATON('**************'), '**************', 4, 0, 1);

-- 批量生成更多IP地址
INSERT INTO ip_addresses (ip_int, ip_string, ip_version, is_internal, is_bot)
SELECT 
    INET_ATON(CONCAT(
        FLOOR(RAND() * 223) + 1, '.', 
        FLOOR(RAND() * 255), '.', 
        FLOOR(RAND() * 255), '.', 
        FLOOR(RAND() * 254) + 1
    )) as ip_int,
    CONCAT(
        FLOOR(RAND() * 223) + 1, '.', 
        FLOOR(RAND() * 255), '.', 
        FLOOR(RAND() * 255), '.', 
        FLOOR(RAND() * 254) + 1
    ) as ip_string,
    4 as ip_version,
    0 as is_internal,
    CASE WHEN RAND() < 0.05 THEN 1 ELSE 0 END as is_bot
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t3
LIMIT 100;

-- ============================================================================
-- 测试User Agent数据
-- ============================================================================

-- 插入测试User Agent
INSERT INTO user_agents (ua_hash, user_agent, browser_id, os_id, device_type, is_bot) VALUES
(MD5('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'), 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 1, 2, 'desktop', 0),
(MD5('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'), 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 1, 3, 'desktop', 0),
(MD5('Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15'), 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 6, 7, 'mobile', 0),
(MD5('Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36'), 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 2, 9, 'mobile', 0),
(MD5('Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'), 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)', NULL, NULL, 'bot', 1),
(MD5('Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)'), 'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)', NULL, NULL, 'bot', 1);

-- ============================================================================
-- 完成提示
-- ============================================================================

SELECT 'Test data generation completed!' as status;
SELECT 'Generated users, sites, pages, keywords, visitors, sessions, IPs, and user agents' as note;
SELECT 'Ready for visit data generation' as next_step;
