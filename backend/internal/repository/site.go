package repository

import (
	"gorm.io/gorm"

	"web-stats-backend/internal/model"
)

// SiteRepository 网站仓储接口
type SiteRepository interface {
	GetByTrackingCode(trackingCode string) (*model.Site, error)
	GetByID(id uint64) (*model.Site, error)
	GetByUserID(userID uint64, page, pageSize int, search, siteType string, status *int8) ([]*model.Site, int64, error)
	Create(site *model.Site) error
	Update(id uint64, updates map[string]interface{}) error
	Delete(id uint64) error
	GetUserSiteCount(userID uint64) (int64, error)
	CheckDomainExists(domain string, excludeID uint64) (bool, error)
	GetSiteStats(siteID uint64) (*model.SiteResponse, error)
}

// siteRepository 网站仓储实现
type siteRepository struct {
	db *gorm.DB
}

// NewSiteRepository 创建网站仓储
func NewSiteRepository(db *gorm.DB) SiteRepository {
	return &siteRepository{db: db}
}

// GetByTrackingCode 根据tracking_code获取网站
func (r *siteRepository) GetByTrackingCode(trackingCode string) (*model.Site, error) {
	var site model.Site
	err := r.db.Where("tracking_code = ? AND status = 1", trackingCode).First(&site).Error
	if err != nil {
		return nil, err
	}
	return &site, nil
}

// GetByID 根据ID获取网站
func (r *siteRepository) GetByID(id uint64) (*model.Site, error) {
	var site model.Site
	err := r.db.First(&site, id).Error
	if err != nil {
		return nil, err
	}
	return &site, nil
}

// GetByUserID 根据用户ID获取网站列表
func (r *siteRepository) GetByUserID(userID uint64, page, pageSize int, search, siteType string, status *int8) ([]*model.Site, int64, error) {
	var sites []*model.Site
	var total int64

	query := r.db.Model(&model.Site{}).Where("user_id = ?", userID)

	// 搜索条件
	if search != "" {
		query = query.Where("site_name LIKE ? OR domain LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 网站类型过滤
	if siteType != "" {
		query = query.Where("site_type = ?", siteType)
	}

	// 状态过滤
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&sites).Error
	if err != nil {
		return nil, 0, err
	}

	return sites, total, nil
}

// Create 创建网站
func (r *siteRepository) Create(site *model.Site) error {
	return r.db.Create(site).Error
}

// Update 更新网站
func (r *siteRepository) Update(id uint64, updates map[string]interface{}) error {
	return r.db.Model(&model.Site{}).Where("id = ?", id).Updates(updates).Error
}

// Delete 删除网站
func (r *siteRepository) Delete(id uint64) error {
	return r.db.Delete(&model.Site{}, id).Error
}

// GetUserSiteCount 获取用户网站数量
func (r *siteRepository) GetUserSiteCount(userID uint64) (int64, error) {
	var count int64
	err := r.db.Model(&model.Site{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// CheckDomainExists 检查域名是否存在
func (r *siteRepository) CheckDomainExists(domain string, excludeID uint64) (bool, error) {
	var count int64
	query := r.db.Model(&model.Site{}).Where("domain = ?", domain)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	err := query.Count(&count).Error
	return count > 0, err
}

// GetSiteStats 获取网站统计信息
func (r *siteRepository) GetSiteStats(siteID uint64) (*model.SiteResponse, error) {
	var site model.Site
	err := r.db.First(&site, siteID).Error
	if err != nil {
		return nil, err
	}

	response := &model.SiteResponse{
		ID:           site.ID,
		SiteName:     site.SiteName,
		Domain:       site.Domain,
		SiteURL:      site.SiteURL,
		SiteType:     site.SiteType,
		Region:       site.Region,
		Description:  site.Description,
		TrackingCode: site.TrackingCode,
		IsPublic:     site.IsPublic,
		Status:       site.Status,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
	}

	// 获取统计数据
	var totalVisits int64
	r.db.Model(&model.Visit{}).Where("site_id = ?", siteID).Count(&totalVisits)
	response.TotalVisits = totalVisits

	var totalPageViews int64
	r.db.Model(&model.PageView{}).Where("site_id = ?", siteID).Count(&totalPageViews)
	response.TotalPageViews = totalPageViews

	// 获取最后访问时间
	var lastVisit model.Visit
	err = r.db.Where("site_id = ?", siteID).Order("visit_date DESC").First(&lastVisit).Error
	if err == nil {
		response.LastVisit = &lastVisit.VisitDate
	}

	return response, nil
}
