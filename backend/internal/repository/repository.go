package repository

import (
	"gorm.io/gorm"
)

// Repository 仓储层结构体
type Repository struct {
	db *gorm.DB

	// 各个模块的仓储
	User       UserRepository
	Site       SiteRepository
	Track      TrackRepository
	Stats      StatsRepository
	Permission PermissionRepository
}

// New 创建新的仓储实例
func New(db *gorm.DB) *Repository {
	return &Repository{
		db:         db,
		User:       NewUserRepository(db),
		Site:       NewSiteRepository(db),
		Track:      NewTrackRepository(db),
		Stats:      NewStatsRepository(db),
		Permission: NewPermissionRepository(db),
	}
}
