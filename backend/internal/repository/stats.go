package repository

import (
	"time"

	"gorm.io/gorm"

	"web-stats-backend/internal/model"
)

// StatsRepository 统计仓储接口
type StatsRepository interface {
	GetOverview(siteID uint64, startDate, endDate time.Time) (*model.OverviewStats, error)
	GetTrend(siteID uint64, startDate, endDate time.Time, granularity string) ([]*model.TrendData, error)
	GetRealtime(siteID uint64) (*model.RealtimeStats, error)
	GetSourceStats(siteID uint64, startDate, endDate time.Time) ([]*model.SourceStats, error)
	GetPageStats(siteID uint64, startDate, endDate time.Time) ([]*model.PageStats, error)
	GetVisitorStats(siteID uint64, startDate, endDate time.Time) (*model.VisitorStats, error)
}

// statsRepository 统计仓储实现
type statsRepository struct {
	db *gorm.DB
}

// NewStatsRepository 创建统计仓储
func NewStatsRepository(db *gorm.DB) StatsRepository {
	return &statsRepository{db: db}
}

// GetOverview 获取概况统计数据
func (r *statsRepository) GetOverview(siteID uint64, startDate, endDate time.Time) (*model.OverviewStats, error) {
	var stats model.OverviewStats

	// 设置基础信息
	stats.SiteID = siteID
	stats.StartDate = startDate
	stats.EndDate = endDate

	// 查询基础统计数据
	err := r.db.Raw(`
		SELECT
			COALESCE(SUM(pv), 0) as pv,
			COALESCE(SUM(uv), 0) as uv,
			COALESCE(SUM(ip_count), 0) as ip_count,
			COALESCE(SUM(session_count), 0) as session_count,
			COALESCE(SUM(new_visitors), 0) as new_visitors,
			COALESCE(SUM(returning_visitors), 0) as returning_visitors,
			COALESCE(SUM(bounce_count), 0) as bounce_count,
			CASE
				WHEN SUM(session_count) > 0 THEN ROUND(SUM(bounce_count) * 100.0 / SUM(session_count), 2)
				ELSE 0
			END as bounce_rate,
			CASE
				WHEN SUM(session_count) > 0 THEN ROUND(SUM(total_duration) / SUM(session_count), 2)
				ELSE 0
			END as avg_duration,
			COALESCE(AVG(avg_page_views), 0) as avg_page_views,
			COALESCE(SUM(desktop_pv), 0) as desktop_pv,
			COALESCE(SUM(mobile_pv), 0) as mobile_pv,
			COALESCE(SUM(tablet_pv), 0) as tablet_pv,
			COALESCE(SUM(search_pv), 0) as search_pv,
			COALESCE(SUM(direct_pv), 0) as direct_pv,
			COALESCE(SUM(external_pv), 0) as external_pv
		FROM stats_daily
		WHERE site_id = ? AND stat_date BETWEEN ? AND ?
	`, siteID, startDate, endDate).Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	// TODO: 计算增长率（与上一周期对比）

	return &stats, nil
}

// GetTrend 获取趋势数据
func (r *statsRepository) GetTrend(siteID uint64, startDate, endDate time.Time, granularity string) ([]*model.TrendData, error) {
	var trends []*model.TrendData

	if granularity == "hour" {
		// 按小时查询（仅支持单日）
		err := r.db.Raw(`
			SELECT
				stat_date as date,
				stat_hour as hour,
				COALESCE(pv, 0) as pv,
				COALESCE(uv, 0) as uv,
				COALESCE(ip_count, 0) as ip_count,
				COALESCE(session_count, 0) as session_count,
				COALESCE(new_visitors, 0) as new_visitors,
				COALESCE(bounce_count, 0) as bounce_count,
				CASE
					WHEN session_count > 0 THEN ROUND(bounce_count * 100.0 / session_count, 2)
					ELSE 0
				END as bounce_rate,
				COALESCE(avg_duration, 0) as avg_duration
			FROM stats_hourly
			WHERE site_id = ? AND stat_date = ?
			ORDER BY stat_hour
		`, siteID, startDate).Scan(&trends).Error

		return trends, err
	} else {
		// 按天查询
		err := r.db.Raw(`
			SELECT
				stat_date as date,
				COALESCE(pv, 0) as pv,
				COALESCE(uv, 0) as uv,
				COALESCE(ip_count, 0) as ip_count,
				COALESCE(session_count, 0) as session_count,
				COALESCE(new_visitors, 0) as new_visitors,
				COALESCE(bounce_count, 0) as bounce_count,
				CASE
					WHEN session_count > 0 THEN ROUND(bounce_count * 100.0 / session_count, 2)
					ELSE 0
				END as bounce_rate,
				COALESCE(avg_duration, 0) as avg_duration
			FROM stats_daily
			WHERE site_id = ? AND stat_date BETWEEN ? AND ?
			ORDER BY stat_date
		`, siteID, startDate, endDate).Scan(&trends).Error

		return trends, err
	}
}

// GetRealtime 获取实时统计数据
func (r *statsRepository) GetRealtime(siteID uint64) (*model.RealtimeStats, error) {
	var stats model.RealtimeStats

	// 获取最新的实时统计数据
	err := r.db.Raw(`
		SELECT
			site_id,
			COALESCE(online_count, 0) as online_count,
			COALESCE(pv_5min, 0) as pv_5min,
			COALESCE(uv_5min, 0) as uv_5min,
			updated_at as last_update
		FROM realtime_stats
		WHERE site_id = ?
		ORDER BY time_slot DESC
		LIMIT 1
	`, siteID).Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	// TODO: 获取最近访客和热门页面数据

	return &stats, nil
}

// GetSourceStats 获取来源统计数据
func (r *statsRepository) GetSourceStats(siteID uint64, startDate, endDate time.Time) ([]*model.SourceStats, error) {
	var sources []*model.SourceStats

	// 简化版本：从visits表聚合数据
	err := r.db.Raw(`
		SELECT
			CASE
				WHEN se.name IS NOT NULL THEN 'search_engine'
				WHEN rd.domain_type = 'social_media' THEN 'social_media'
				WHEN v.referer_domain_id IS NULL THEN 'direct'
				ELSE 'external'
			END as source_type,
			COALESCE(se.name, rd.domain, 'Direct') as source_name,
			COUNT(*) as pv,
			COUNT(DISTINCT v.visitor_id) as uv,
			COUNT(DISTINCT v.session_id) as session_count,
			ROUND(AVG(v.visit_duration), 2) as avg_duration,
			ROUND(AVG(v.page_views), 2) as avg_page_views,
			ROUND(SUM(CASE WHEN v.page_views = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as bounce_rate
		FROM visits v
		LEFT JOIN referer_domains rd ON v.referer_domain_id = rd.id
		LEFT JOIN search_engines se ON v.search_engine_id = se.id
		WHERE v.site_id = ? AND v.visit_date BETWEEN ? AND ?
		GROUP BY source_type, source_name
		ORDER BY pv DESC
		LIMIT 20
	`, siteID, startDate, endDate).Scan(&sources).Error

	return sources, err
}

// GetPageStats 获取页面统计数据
func (r *statsRepository) GetPageStats(siteID uint64, startDate, endDate time.Time) ([]*model.PageStats, error) {
	var pages []*model.PageStats

	err := r.db.Raw(`
		SELECT
			pu.url as page_url,
			pu.title as page_title,
			COUNT(pv.id) as pv,
			COUNT(DISTINCT pv.visitor_id) as uv,
			ROUND(AVG(pv.stay_duration), 2) as avg_duration,
			ROUND(AVG(pv.scroll_depth), 2) as avg_scroll_depth,
			SUM(CASE WHEN pv.is_bounce = 1 THEN 1 ELSE 0 END) as bounce_count,
			ROUND(SUM(CASE WHEN pv.is_bounce = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as bounce_rate
		FROM page_views pv
		JOIN page_urls pu ON pv.page_url_id = pu.id
		WHERE pv.site_id = ? AND pv.view_date BETWEEN ? AND ?
		GROUP BY pu.url, pu.title
		ORDER BY pv DESC
		LIMIT 50
	`, siteID, startDate, endDate).Scan(&pages).Error

	return pages, err
}

// GetVisitorStats 获取访客统计数据
func (r *statsRepository) GetVisitorStats(siteID uint64, startDate, endDate time.Time) (*model.VisitorStats, error) {
	var stats model.VisitorStats

	// 获取地区分布
	var regions []*model.RegionStatsItem
	err := r.db.Raw(`
		SELECT
			r.country,
			r.province,
			r.city,
			COUNT(*) as pv,
			COUNT(DISTINCT v.visitor_id) as uv,
			COUNT(DISTINCT v.session_id) as session_count
		FROM visits v
		LEFT JOIN regions r ON v.region_id = r.id
		WHERE v.site_id = ? AND v.visit_date BETWEEN ? AND ?
		GROUP BY r.country, r.province, r.city
		ORDER BY pv DESC
		LIMIT 20
	`, siteID, startDate, endDate).Scan(&regions).Error

	if err != nil {
		return nil, err
	}
	stats.RegionStats = regions

	// 获取设备分布
	var devices []*model.DeviceStatsItem
	err = r.db.Raw(`
		SELECT
			device_type,
			COUNT(*) as pv,
			COUNT(DISTINCT visitor_id) as uv,
			COUNT(DISTINCT session_id) as session_count,
			ROUND(AVG(visit_duration), 2) as avg_duration,
			ROUND(SUM(CASE WHEN page_views = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as bounce_rate
		FROM visits
		WHERE site_id = ? AND visit_date BETWEEN ? AND ?
		GROUP BY device_type
		ORDER BY pv DESC
	`, siteID, startDate, endDate).Scan(&devices).Error

	if err != nil {
		return nil, err
	}
	stats.DeviceStats = devices

	// TODO: 实现浏览器、操作系统、新老访客、忠诚度统计

	return &stats, nil
}
