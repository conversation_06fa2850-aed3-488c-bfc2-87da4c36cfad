package repository

import (
	"time"

	"gorm.io/gorm"

	"web-stats-backend/internal/model"
)

// PermissionRepository 权限仓储接口
type PermissionRepository interface {
	// 权限检查
	CheckUserPermission(userID uint64, resource, action string) (bool, error)
	GetUserPermissions(userID uint64) ([]*model.Permission, error)
	GetUserRoles(userID uint64) ([]*model.Role, error)
	
	// 角色管理
	CreateRole(role *model.Role) error
	UpdateRole(id uint, updates map[string]interface{}) error
	DeleteRole(id uint) error
	GetRoleByID(id uint) (*model.Role, error)
	GetRoleByName(name string) (*model.Role, error)
	GetRoles(page, pageSize int, search string, status *int8) ([]*model.Role, int64, error)
	
	// 权限管理
	CreatePermission(permission *model.Permission) error
	UpdatePermission(id uint, updates map[string]interface{}) error
	DeletePermission(id uint) error
	GetPermissionByID(id uint) (*model.Permission, error)
	GetPermissions() ([]*model.Permission, error)
	
	// 角色权限关联
	AssignPermissionsToRole(roleID uint, permissionIDs []uint) error
	RemovePermissionsFromRole(roleID uint, permissionIDs []uint) error
	GetRolePermissions(roleID uint) ([]*model.Permission, error)
	
	// 用户角色关联
	AssignRolesToUser(userID uint64, roleIDs []uint) error
	RemoveRolesFromUser(userID uint64, roleIDs []uint) error
	
	// 权限缓存
	RefreshUserPermissionCache(userID uint64) error
	ClearUserPermissionCache(userID uint64) error
	
	// 操作日志
	CreateOperationLog(log *model.OperationLog) error
	GetOperationLogs(req *model.OperationLogRequest) ([]*model.OperationLog, int64, error)
}

// permissionRepository 权限仓储实现
type permissionRepository struct {
	db *gorm.DB
}

// NewPermissionRepository 创建权限仓储
func NewPermissionRepository(db *gorm.DB) PermissionRepository {
	return &permissionRepository{db: db}
}

// CheckUserPermission 检查用户权限
func (r *permissionRepository) CheckUserPermission(userID uint64, resource, action string) (bool, error) {
	var count int64
	err := r.db.Model(&model.UserPermission{}).
		Where("user_id = ? AND resource = ? AND action = ?", userID, resource, action).
		Count(&count).Error
	
	return count > 0, err
}

// GetUserPermissions 获取用户权限列表
func (r *permissionRepository) GetUserPermissions(userID uint64) ([]*model.Permission, error) {
	var permissions []*model.Permission
	
	err := r.db.Table("permissions p").
		Select("p.*").
		Joins("JOIN user_permissions up ON p.id = up.permission_id").
		Where("up.user_id = ?", userID).
		Find(&permissions).Error
	
	return permissions, err
}

// GetUserRoles 获取用户角色列表
func (r *permissionRepository) GetUserRoles(userID uint64) ([]*model.Role, error) {
	var roles []*model.Role
	
	err := r.db.Table("roles r").
		Select("r.*").
		Joins("JOIN user_roles ur ON r.id = ur.role_id").
		Where("ur.user_id = ? AND r.status = 1", userID).
		Find(&roles).Error
	
	return roles, err
}

// CreateRole 创建角色
func (r *permissionRepository) CreateRole(role *model.Role) error {
	return r.db.Create(role).Error
}

// UpdateRole 更新角色
func (r *permissionRepository) UpdateRole(id uint, updates map[string]interface{}) error {
	return r.db.Model(&model.Role{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteRole 删除角色
func (r *permissionRepository) DeleteRole(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id = ?", id).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}
		
		// 删除用户角色关联
		if err := tx.Where("role_id = ?", id).Delete(&model.UserRole{}).Error; err != nil {
			return err
		}
		
		// 删除角色
		return tx.Delete(&model.Role{}, id).Error
	})
}

// GetRoleByID 根据ID获取角色
func (r *permissionRepository) GetRoleByID(id uint) (*model.Role, error) {
	var role model.Role
	err := r.db.Preload("Permissions").First(&role, id).Error
	return &role, err
}

// GetRoleByName 根据名称获取角色
func (r *permissionRepository) GetRoleByName(name string) (*model.Role, error) {
	var role model.Role
	err := r.db.Where("name = ?", name).First(&role).Error
	return &role, err
}

// GetRoles 获取角色列表
func (r *permissionRepository) GetRoles(page, pageSize int, search string, status *int8) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64
	
	query := r.db.Model(&model.Role{})
	
	// 搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR display_name LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	
	// 状态过滤
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Preload("Permissions").Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&roles).Error
	
	return roles, total, err
}

// CreatePermission 创建权限
func (r *permissionRepository) CreatePermission(permission *model.Permission) error {
	return r.db.Create(permission).Error
}

// UpdatePermission 更新权限
func (r *permissionRepository) UpdatePermission(id uint, updates map[string]interface{}) error {
	return r.db.Model(&model.Permission{}).Where("id = ?", id).Updates(updates).Error
}

// DeletePermission 删除权限
func (r *permissionRepository) DeletePermission(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("permission_id = ?", id).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}
		
		// 删除用户权限缓存
		if err := tx.Where("permission_id = ?", id).Delete(&model.UserPermission{}).Error; err != nil {
			return err
		}
		
		// 删除权限
		return tx.Delete(&model.Permission{}, id).Error
	})
}

// GetPermissionByID 根据ID获取权限
func (r *permissionRepository) GetPermissionByID(id uint) (*model.Permission, error) {
	var permission model.Permission
	err := r.db.First(&permission, id).Error
	return &permission, err
}

// GetPermissions 获取所有权限
func (r *permissionRepository) GetPermissions() ([]*model.Permission, error) {
	var permissions []*model.Permission
	err := r.db.Order("resource, action").Find(&permissions).Error
	return permissions, err
}

// AssignPermissionsToRole 为角色分配权限
func (r *permissionRepository) AssignPermissionsToRole(roleID uint, permissionIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除现有权限
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}
		
		// 添加新权限
		for _, permissionID := range permissionIDs {
			rolePermission := &model.RolePermission{
				RoleID:       roleID,
				PermissionID: permissionID,
				CreatedAt:    time.Now(),
			}
			if err := tx.Create(rolePermission).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

// RemovePermissionsFromRole 从角色移除权限
func (r *permissionRepository) RemovePermissionsFromRole(roleID uint, permissionIDs []uint) error {
	return r.db.Where("role_id = ? AND permission_id IN ?", roleID, permissionIDs).
		Delete(&model.RolePermission{}).Error
}

// GetRolePermissions 获取角色权限
func (r *permissionRepository) GetRolePermissions(roleID uint) ([]*model.Permission, error) {
	var permissions []*model.Permission
	
	err := r.db.Table("permissions p").
		Select("p.*").
		Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id = ?", roleID).
		Find(&permissions).Error
	
	return permissions, err
}

// AssignRolesToUser 为用户分配角色
func (r *permissionRepository) AssignRolesToUser(userID uint64, roleIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除现有角色
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserRole{}).Error; err != nil {
			return err
		}
		
		// 添加新角色
		for _, roleID := range roleIDs {
			userRole := &model.UserRole{
				UserID:    userID,
				RoleID:    roleID,
				CreatedAt: time.Now(),
			}
			if err := tx.Create(userRole).Error; err != nil {
				return err
			}
		}
		
		// 刷新用户权限缓存
		return r.RefreshUserPermissionCache(userID)
	})
}

// RemoveRolesFromUser 从用户移除角色
func (r *permissionRepository) RemoveRolesFromUser(userID uint64, roleIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("user_id = ? AND role_id IN ?", userID, roleIDs).
			Delete(&model.UserRole{}).Error
		if err != nil {
			return err
		}
		
		// 刷新用户权限缓存
		return r.RefreshUserPermissionCache(userID)
	})
}

// RefreshUserPermissionCache 刷新用户权限缓存
func (r *permissionRepository) RefreshUserPermissionCache(userID uint64) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 清除现有缓存
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserPermission{}).Error; err != nil {
			return err
		}
		
		// 获取用户所有权限
		var permissions []*model.Permission
		err := tx.Table("permissions p").
			Select("DISTINCT p.*").
			Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
			Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
			Joins("JOIN roles r ON ur.role_id = r.id").
			Where("ur.user_id = ? AND r.status = 1", userID).
			Find(&permissions).Error
		
		if err != nil {
			return err
		}
		
		// 创建权限缓存
		for _, permission := range permissions {
			userPermission := &model.UserPermission{
				UserID:       userID,
				PermissionID: permission.ID,
				Resource:     permission.Resource,
				Action:       permission.Action,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}
			if err := tx.Create(userPermission).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

// ClearUserPermissionCache 清除用户权限缓存
func (r *permissionRepository) ClearUserPermissionCache(userID uint64) error {
	return r.db.Where("user_id = ?", userID).Delete(&model.UserPermission{}).Error
}

// CreateOperationLog 创建操作日志
func (r *permissionRepository) CreateOperationLog(log *model.OperationLog) error {
	return r.db.Create(log).Error
}

// GetOperationLogs 获取操作日志
func (r *permissionRepository) GetOperationLogs(req *model.OperationLogRequest) ([]*model.OperationLog, int64, error) {
	var logs []*model.OperationLog
	var total int64
	
	query := r.db.Model(&model.OperationLog{})
	
	// 过滤条件
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.Action != "" {
		query = query.Where("action LIKE ?", "%"+req.Action+"%")
	}
	if req.Resource != "" {
		query = query.Where("resource LIKE ?", "%"+req.Resource+"%")
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}
	
	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	
	offset := (req.Page - 1) * req.PageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&logs).Error
	
	return logs, total, err
}
