package config

import (
	"os"
	"strconv"
	"strings"
)

// Config 应用配置
type Config struct {
	Database DatabaseConfig
	JWT      JWTConfig
	CORS     CORSConfig
	LogLevel string
	Port     string
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	DBName   string
	Charset  string
	TimeZone string
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string
	ExpireTime int // 小时
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins string
}

// New 创建新的配置实例
func New() *Config {
	return &Config{
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 3306),
			User:     getEnv("DB_USER", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			DBName:   getEnv("DB_NAME", "web_stats"),
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
			TimeZone: getEnv("DB_TIMEZONE", "Asia/Shanghai"),
		},
		JWT: JWTConfig{
			Secret:     getEnv("JWT_SECRET", "your-secret-key"),
			ExpireTime: getEnvAsInt("JWT_EXPIRE_TIME", 24),
		},
		CORS: CORSConfig{
			AllowOrigins: getEnv("CORS_ALLOW_ORIGINS", "http://localhost:2002,http://localhost:4002"),
		},
		LogLevel: getEnv("LOG_LEVEL", "info"),
		Port:     getEnv("PORT", "9002"),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return c.User + ":" + c.Password + "@tcp(" + c.Host + ":" + strconv.Itoa(c.Port) + ")/" + c.DBName + "?charset=" + c.Charset + "&parseTime=True&loc=" + strings.ReplaceAll(c.TimeZone, "/", "%2F")
}
