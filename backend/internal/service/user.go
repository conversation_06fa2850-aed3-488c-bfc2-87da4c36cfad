package service

import (
	"web-stats-backend/internal/model"
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// UserService 用户服务接口
type UserService interface {
	Create(user *model.User) error
	GetByID(id uint) (*model.User, error)
	GetByUsername(username string) (*model.User, error)
	GetByEmail(email string) (*model.User, error)
	Update(user *model.User) error
	Delete(id uint) error
	List(offset, limit int) ([]*model.User, int64, error)
}

// userService 用户服务实现
type userService struct {
	repos  *repository.Repository
	logger logger.Logger
}

// NewUserService 创建用户服务
func NewUserService(repos *repository.Repository, logger logger.Logger) UserService {
	return &userService{
		repos:  repos,
		logger: logger,
	}
}

// Create 创建用户
func (s *userService) Create(user *model.User) error {
	return s.repos.User.Create(user)
}

// GetByID 根据ID获取用户
func (s *userService) GetByID(id uint) (*model.User, error) {
	return s.repos.User.GetByID(id)
}

// GetByUsername 根据用户名获取用户
func (s *userService) GetByUsername(username string) (*model.User, error) {
	return s.repos.User.GetByUsername(username)
}

// GetByEmail 根据邮箱获取用户
func (s *userService) GetByEmail(email string) (*model.User, error) {
	return s.repos.User.GetByEmail(email)
}

// Update 更新用户
func (s *userService) Update(user *model.User) error {
	return s.repos.User.Update(user)
}

// Delete 删除用户
func (s *userService) Delete(id uint) error {
	return s.repos.User.Delete(id)
}

// List 获取用户列表
func (s *userService) List(offset, limit int) ([]*model.User, int64, error) {
	return s.repos.User.List(offset, limit)
}
