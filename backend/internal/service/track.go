package service

import (
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// TrackService 数据收集服务接口
type TrackService interface {
	// TODO: 定义数据收集服务接口方法
}

// trackService 数据收集服务实现
type trackService struct {
	repos  *repository.Repository
	logger logger.Logger
}

// NewTrackService 创建数据收集服务
func NewTrackService(repos *repository.Repository, logger logger.Logger) TrackService {
	return &trackService{
		repos:  repos,
		logger: logger,
	}
}
