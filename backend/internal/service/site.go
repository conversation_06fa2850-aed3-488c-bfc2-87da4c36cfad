package service

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"

	"web-stats-backend/internal/model"
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// SiteService 网站服务接口
type SiteService interface {
	GetByTrackingCode(trackingCode string) (*model.Site, error)
	GetByID(id uint64) (*model.Site, error)
	GetUserSites(userID uint64, req *model.SiteListRequest) ([]*model.SiteResponse, int64, error)
	Create(userID uint64, req *model.SiteCreateRequest) (*model.SiteResponse, error)
	Update(id, userID uint64, req *model.SiteUpdateRequest) (*model.SiteResponse, error)
	Delete(id, userID uint64) error
	GetSiteStats(id, userID uint64) (*model.SiteResponse, error)
	GenerateTrackingCode() string
	ValidateSiteOwnership(siteID, userID uint64) error
	CheckDomainExists(domain string, excludeID uint64) (bool, error)
}

// siteService 网站服务实现
type siteService struct {
	repos  *repository.Repository
	logger logger.Logger
}

// NewSiteService 创建网站服务
func NewSiteService(repos *repository.Repository, logger logger.Logger) SiteService {
	return &siteService{
		repos:  repos,
		logger: logger,
	}
}

// GetByTrackingCode 根据tracking_code获取网站
func (s *siteService) GetByTrackingCode(trackingCode string) (*model.Site, error) {
	return s.repos.Site.GetByTrackingCode(trackingCode)
}

// GetByID 根据ID获取网站
func (s *siteService) GetByID(id uint64) (*model.Site, error) {
	return s.repos.Site.GetByID(id)
}

// GetUserSites 获取用户网站列表
func (s *siteService) GetUserSites(userID uint64, req *model.SiteListRequest) ([]*model.SiteResponse, int64, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	var status *int8
	if req.Status >= 0 {
		status = &req.Status
	}

	sites, total, err := s.repos.Site.GetByUserID(userID, req.Page, req.PageSize, req.Search, req.SiteType, status)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*model.SiteResponse, len(sites))
	for i, site := range sites {
		responses[i] = &model.SiteResponse{
			ID:           site.ID,
			SiteName:     site.SiteName,
			Domain:       site.Domain,
			SiteURL:      site.SiteURL,
			SiteType:     site.SiteType,
			Region:       site.Region,
			Description:  site.Description,
			TrackingCode: site.TrackingCode,
			IsPublic:     site.IsPublic,
			Status:       site.Status,
			CreatedAt:    site.CreatedAt,
			UpdatedAt:    site.UpdatedAt,
		}
	}

	return responses, total, nil
}

// Create 创建网站
func (s *siteService) Create(userID uint64, req *model.SiteCreateRequest) (*model.SiteResponse, error) {
	// 验证域名是否已存在
	exists, err := s.repos.Site.CheckDomainExists(req.Domain, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("域名已存在")
	}

	// 生成跟踪代码
	trackingCode := s.GenerateTrackingCode()

	// 创建网站
	site := &model.Site{
		UserID:       userID,
		SiteName:     req.SiteName,
		Domain:       req.Domain,
		SiteURL:      req.SiteURL,
		SiteType:     req.SiteType,
		Region:       req.Region,
		Description:  req.Description,
		TrackingCode: trackingCode,
		ViewPassword: req.ViewPassword,
		IsPublic:     req.IsPublic,
		Status:       1, // 默认启用
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if req.SiteType == "" {
		site.SiteType = "other"
	}

	err = s.repos.Site.Create(site)
	if err != nil {
		return nil, err
	}

	// 返回响应
	return &model.SiteResponse{
		ID:           site.ID,
		SiteName:     site.SiteName,
		Domain:       site.Domain,
		SiteURL:      site.SiteURL,
		SiteType:     site.SiteType,
		Region:       site.Region,
		Description:  site.Description,
		TrackingCode: site.TrackingCode,
		IsPublic:     site.IsPublic,
		Status:       site.Status,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
	}, nil
}

// Update 更新网站
func (s *siteService) Update(id, userID uint64, req *model.SiteUpdateRequest) (*model.SiteResponse, error) {
	// 验证网站所有权
	err := s.ValidateSiteOwnership(id, userID)
	if err != nil {
		return nil, err
	}

	// 如果更新域名，检查是否已存在
	if req.Domain != "" {
		exists, err := s.repos.Site.CheckDomainExists(req.Domain, id)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, errors.New("域名已存在")
		}
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.SiteName != "" {
		updates["site_name"] = req.SiteName
	}
	if req.Domain != "" {
		updates["domain"] = req.Domain
	}
	if req.SiteURL != "" {
		updates["site_url"] = req.SiteURL
	}
	if req.SiteType != "" {
		updates["site_type"] = req.SiteType
	}
	if req.Region != "" {
		updates["region"] = req.Region
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.ViewPassword != "" {
		updates["view_password"] = req.ViewPassword
	}
	if req.IsPublic >= 0 {
		updates["is_public"] = req.IsPublic
	}
	if req.Status >= 0 {
		updates["status"] = req.Status
	}
	updates["updated_at"] = time.Now()

	// 执行更新
	err = s.repos.Site.Update(id, updates)
	if err != nil {
		return nil, err
	}

	// 获取更新后的网站信息
	site, err := s.repos.Site.GetByID(id)
	if err != nil {
		return nil, err
	}

	return &model.SiteResponse{
		ID:           site.ID,
		SiteName:     site.SiteName,
		Domain:       site.Domain,
		SiteURL:      site.SiteURL,
		SiteType:     site.SiteType,
		Region:       site.Region,
		Description:  site.Description,
		TrackingCode: site.TrackingCode,
		IsPublic:     site.IsPublic,
		Status:       site.Status,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
	}, nil
}

// Delete 删除网站
func (s *siteService) Delete(id, userID uint64) error {
	// 验证网站所有权
	err := s.ValidateSiteOwnership(id, userID)
	if err != nil {
		return err
	}

	// TODO: 在实际删除前，可能需要清理相关的统计数据
	// 或者采用软删除的方式

	return s.repos.Site.Delete(id)
}

// GetSiteStats 获取网站统计信息
func (s *siteService) GetSiteStats(id, userID uint64) (*model.SiteResponse, error) {
	// 验证网站所有权
	err := s.ValidateSiteOwnership(id, userID)
	if err != nil {
		return nil, err
	}

	return s.repos.Site.GetSiteStats(id)
}

// GenerateTrackingCode 生成跟踪代码
func (s *siteService) GenerateTrackingCode() string {
	// 生成8字节随机数
	bytes := make([]byte, 8)
	rand.Read(bytes)

	// 转换为16进制字符串并转大写
	code := strings.ToUpper(hex.EncodeToString(bytes))

	// 格式化为 TRACK + 8位字符
	return fmt.Sprintf("TRACK%s", code[:8])
}

// ValidateSiteOwnership 验证网站所有权
func (s *siteService) ValidateSiteOwnership(siteID, userID uint64) error {
	site, err := s.repos.Site.GetByID(siteID)
	if err != nil {
		return errors.New("网站不存在")
	}

	if site.UserID != userID {
		return errors.New("无权限访问此网站")
	}

	return nil
}

// CheckDomainExists 检查域名是否已存在
func (s *siteService) CheckDomainExists(domain string, excludeID uint64) (bool, error) {
	return s.repos.Site.CheckDomainExists(domain, excludeID)
}
