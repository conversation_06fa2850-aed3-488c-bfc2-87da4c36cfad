package service

import (
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// Service 服务层结构体
type Service struct {
	repos  *repository.Repository
	logger logger.Logger

	// 各个模块的服务
	User       UserService
	Site       SiteService
	Track      TrackService
	Stats      StatsService
	Permission PermissionService
}

// New 创建新的服务实例
func New(repos *repository.Repository, logger logger.Logger) *Service {
	return &Service{
		repos:      repos,
		logger:     logger,
		User:       NewUserService(repos, logger),
		Site:       NewSiteService(repos, logger),
		Track:      NewTrackService(repos, logger),
		Stats:      NewStatsService(repos, logger),
		Permission: NewPermissionService(repos, logger),
	}
}
