package service

import (
	"time"

	"web-stats-backend/internal/model"
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// StatsService 统计服务接口
type StatsService interface {
	GetOverview(siteID uint64, startDate, endDate time.Time) (*model.OverviewStats, error)
	GetTrend(siteID uint64, startDate, endDate time.Time, granularity string) ([]*model.TrendData, error)
	GetRealtime(siteID uint64) (*model.RealtimeStats, error)
	GetSourceStats(siteID uint64, startDate, endDate time.Time) ([]*model.SourceStats, error)
	GetPageStats(siteID uint64, startDate, endDate time.Time) ([]*model.PageStats, error)
	GetVisitorStats(siteID uint64, startDate, endDate time.Time) (*model.VisitorStats, error)
}

// statsService 统计服务实现
type statsService struct {
	repos  *repository.Repository
	logger logger.Logger
}

// NewStatsService 创建统计服务
func NewStatsService(repos *repository.Repository, logger logger.Logger) StatsService {
	return &statsService{
		repos:  repos,
		logger: logger,
	}
}

// GetOverview 获取概况统计数据
func (s *statsService) GetOverview(siteID uint64, startDate, endDate time.Time) (*model.OverviewStats, error) {
	return s.repos.Stats.GetOverview(siteID, startDate, endDate)
}

// GetTrend 获取趋势数据
func (s *statsService) GetTrend(siteID uint64, startDate, endDate time.Time, granularity string) ([]*model.TrendData, error) {
	return s.repos.Stats.GetTrend(siteID, startDate, endDate, granularity)
}

// GetRealtime 获取实时统计数据
func (s *statsService) GetRealtime(siteID uint64) (*model.RealtimeStats, error) {
	return s.repos.Stats.GetRealtime(siteID)
}

// GetSourceStats 获取来源统计数据
func (s *statsService) GetSourceStats(siteID uint64, startDate, endDate time.Time) ([]*model.SourceStats, error) {
	return s.repos.Stats.GetSourceStats(siteID, startDate, endDate)
}

// GetPageStats 获取页面统计数据
func (s *statsService) GetPageStats(siteID uint64, startDate, endDate time.Time) ([]*model.PageStats, error) {
	return s.repos.Stats.GetPageStats(siteID, startDate, endDate)
}

// GetVisitorStats 获取访客统计数据
func (s *statsService) GetVisitorStats(siteID uint64, startDate, endDate time.Time) (*model.VisitorStats, error) {
	return s.repos.Stats.GetVisitorStats(siteID, startDate, endDate)
}
