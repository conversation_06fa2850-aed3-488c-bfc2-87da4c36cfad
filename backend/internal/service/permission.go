package service

import (
	"errors"
	"time"

	"web-stats-backend/internal/model"
	"web-stats-backend/internal/repository"
	"web-stats-backend/pkg/logger"
)

// PermissionService 权限服务接口
type PermissionService interface {
	// 权限检查
	CheckPermission(userID uint64, resource, action string) (bool, error)
	CheckSitePermission(userID, siteID uint64, action string) (bool, error)
	GetUserPermissions(userID uint64) (*model.UserPermissionResponse, error)
	
	// 角色管理
	CreateRole(req *model.RoleCreateRequest) (*model.RoleResponse, error)
	UpdateRole(id uint, req *model.RoleUpdateRequest) (*model.RoleResponse, error)
	DeleteRole(id uint) error
	GetRole(id uint) (*model.RoleResponse, error)
	GetRoles(req *model.RoleListRequest) ([]*model.RoleResponse, int64, error)
	
	// 权限管理
	CreatePermission(req *model.PermissionCreateRequest) (*model.PermissionResponse, error)
	UpdatePermission(id uint, req *model.PermissionUpdateRequest) (*model.PermissionResponse, error)
	DeletePermission(id uint) error
	GetPermission(id uint) (*model.PermissionResponse, error)
	GetPermissions() ([]*model.PermissionResponse, error)
	
	// 用户角色分配
	AssignUserRoles(req *model.UserRoleAssignRequest) error
	GetUserRoles(userID uint64) ([]*model.RoleResponse, error)
	
	// 操作日志
	LogOperation(userID uint64, username, action, resource string, resourceID uint64, 
		method, path, ip, userAgent, request, response string, status int, duration int64) error
	GetOperationLogs(req *model.OperationLogRequest) ([]*model.OperationLog, int64, error)
	
	// 初始化系统权限
	InitializeSystemPermissions() error
	InitializeSystemRoles() error
}

// permissionService 权限服务实现
type permissionService struct {
	repos  *repository.Repository
	logger logger.Logger
}

// NewPermissionService 创建权限服务
func NewPermissionService(repos *repository.Repository, logger logger.Logger) PermissionService {
	return &permissionService{
		repos:  repos,
		logger: logger,
	}
}

// CheckPermission 检查用户权限
func (s *permissionService) CheckPermission(userID uint64, resource, action string) (bool, error) {
	// 超级管理员拥有所有权限
	if s.isAdmin(userID) {
		return true, nil
	}
	
	return s.repos.Permission.CheckUserPermission(userID, resource, action)
}

// CheckSitePermission 检查网站权限（包含数据隔离）
func (s *permissionService) CheckSitePermission(userID, siteID uint64, action string) (bool, error) {
	// 超级管理员拥有所有权限
	if s.isAdmin(userID) {
		return true, nil
	}
	
	// 检查基础权限
	hasPermission, err := s.repos.Permission.CheckUserPermission(userID, model.ResourceSite, action)
	if err != nil || !hasPermission {
		return false, err
	}
	
	// 检查网站所有权（数据隔离）
	site, err := s.repos.Site.GetByID(siteID)
	if err != nil {
		return false, err
	}
	
	return site.UserID == userID, nil
}

// GetUserPermissions 获取用户权限信息
func (s *permissionService) GetUserPermissions(userID uint64) (*model.UserPermissionResponse, error) {
	// 获取用户信息
	user, err := s.repos.User.GetByID(uint(userID))
	if err != nil {
		return nil, err
	}
	
	// 获取用户角色
	roles, err := s.repos.Permission.GetUserRoles(userID)
	if err != nil {
		return nil, err
	}
	
	// 获取用户权限
	permissions, err := s.repos.Permission.GetUserPermissions(userID)
	if err != nil {
		return nil, err
	}
	
	// 转换为响应格式
	roleResponses := make([]model.RoleResponse, len(roles))
	for i, role := range roles {
		roleResponses[i] = model.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			IsSystem:    role.IsSystem,
			Status:      role.Status,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		}
	}
	
	permissionResponses := make([]model.PermissionResponse, len(permissions))
	for i, permission := range permissions {
		permissionResponses[i] = model.PermissionResponse{
			ID:          permission.ID,
			Name:        permission.Name,
			DisplayName: permission.DisplayName,
			Description: permission.Description,
			Resource:    permission.Resource,
			Action:      permission.Action,
			IsSystem:    permission.IsSystem,
			CreatedAt:   permission.CreatedAt,
			UpdatedAt:   permission.UpdatedAt,
		}
	}
	
	return &model.UserPermissionResponse{
		UserID:      userID,
		Username:    user.Username,
		Roles:       roleResponses,
		Permissions: permissionResponses,
	}, nil
}

// CreateRole 创建角色
func (s *permissionService) CreateRole(req *model.RoleCreateRequest) (*model.RoleResponse, error) {
	// 检查角色名是否已存在
	_, err := s.repos.Permission.GetRoleByName(req.Name)
	if err == nil {
		return nil, errors.New("角色名已存在")
	}
	
	// 创建角色
	role := &model.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsSystem:    false,
		Status:      1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	err = s.repos.Permission.CreateRole(role)
	if err != nil {
		return nil, err
	}
	
	// 分配权限
	if len(req.Permissions) > 0 {
		err = s.repos.Permission.AssignPermissionsToRole(role.ID, req.Permissions)
		if err != nil {
			return nil, err
		}
	}
	
	// 获取完整的角色信息
	return s.GetRole(role.ID)
}

// UpdateRole 更新角色
func (s *permissionService) UpdateRole(id uint, req *model.RoleUpdateRequest) (*model.RoleResponse, error) {
	// 检查角色是否存在
	role, err := s.repos.Permission.GetRoleByID(id)
	if err != nil {
		return nil, errors.New("角色不存在")
	}
	
	// 系统角色不允许修改
	if role.IsSystem {
		return nil, errors.New("系统角色不允许修改")
	}
	
	// 构建更新数据
	updates := make(map[string]interface{})
	if req.DisplayName != "" {
		updates["display_name"] = req.DisplayName
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status >= 0 {
		updates["status"] = req.Status
	}
	updates["updated_at"] = time.Now()
	
	// 更新角色
	err = s.repos.Permission.UpdateRole(id, updates)
	if err != nil {
		return nil, err
	}
	
	// 更新权限
	if req.Permissions != nil {
		err = s.repos.Permission.AssignPermissionsToRole(id, req.Permissions)
		if err != nil {
			return nil, err
		}
	}
	
	// 获取更新后的角色信息
	return s.GetRole(id)
}

// DeleteRole 删除角色
func (s *permissionService) DeleteRole(id uint) error {
	// 检查角色是否存在
	role, err := s.repos.Permission.GetRoleByID(id)
	if err != nil {
		return errors.New("角色不存在")
	}
	
	// 系统角色不允许删除
	if role.IsSystem {
		return errors.New("系统角色不允许删除")
	}
	
	return s.repos.Permission.DeleteRole(id)
}

// GetRole 获取角色详情
func (s *permissionService) GetRole(id uint) (*model.RoleResponse, error) {
	role, err := s.repos.Permission.GetRoleByID(id)
	if err != nil {
		return nil, err
	}
	
	// 转换权限列表
	permissions := make([]model.PermissionResponse, len(role.Permissions))
	for i, permission := range role.Permissions {
		permissions[i] = model.PermissionResponse{
			ID:          permission.ID,
			Name:        permission.Name,
			DisplayName: permission.DisplayName,
			Description: permission.Description,
			Resource:    permission.Resource,
			Action:      permission.Action,
			IsSystem:    permission.IsSystem,
			CreatedAt:   permission.CreatedAt,
			UpdatedAt:   permission.UpdatedAt,
		}
	}
	
	return &model.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		IsSystem:    role.IsSystem,
		Status:      role.Status,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
		Permissions: permissions,
	}, nil
}

// GetRoles 获取角色列表
func (s *permissionService) GetRoles(req *model.RoleListRequest) ([]*model.RoleResponse, int64, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	
	var status *int8
	if req.Status >= 0 {
		status = &req.Status
	}
	
	roles, total, err := s.repos.Permission.GetRoles(req.Page, req.PageSize, req.Search, status)
	if err != nil {
		return nil, 0, err
	}
	
	// 转换为响应格式
	responses := make([]*model.RoleResponse, len(roles))
	for i, role := range roles {
		permissions := make([]model.PermissionResponse, len(role.Permissions))
		for j, permission := range role.Permissions {
			permissions[j] = model.PermissionResponse{
				ID:          permission.ID,
				Name:        permission.Name,
				DisplayName: permission.DisplayName,
				Description: permission.Description,
				Resource:    permission.Resource,
				Action:      permission.Action,
				IsSystem:    permission.IsSystem,
				CreatedAt:   permission.CreatedAt,
				UpdatedAt:   permission.UpdatedAt,
			}
		}
		
		responses[i] = &model.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			IsSystem:    role.IsSystem,
			Status:      role.Status,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
			Permissions: permissions,
		}
	}
	
	return responses, total, nil
}

// isAdmin 检查是否为管理员
func (s *permissionService) isAdmin(userID uint64) bool {
	roles, err := s.repos.Permission.GetUserRoles(userID)
	if err != nil {
		return false
	}
	
	for _, role := range roles {
		if role.Name == model.RoleAdmin {
			return true
		}
	}
	
	return false
}

// LogOperation 记录操作日志
func (s *permissionService) LogOperation(userID uint64, username, action, resource string, resourceID uint64, 
	method, path, ip, userAgent, request, response string, status int, duration int64) error {
	
	log := &model.OperationLog{
		UserID:     userID,
		Username:   username,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		Method:     method,
		Path:       path,
		IP:         ip,
		UserAgent:  userAgent,
		Request:    request,
		Response:   response,
		Status:     status,
		Duration:   duration,
		CreatedAt:  time.Now(),
	}
	
	return s.repos.Permission.CreateOperationLog(log)
}

// GetOperationLogs 获取操作日志
func (s *permissionService) GetOperationLogs(req *model.OperationLogRequest) ([]*model.OperationLog, int64, error) {
	return s.repos.Permission.GetOperationLogs(req)
}

// AssignUserRoles 为用户分配角色
func (s *permissionService) AssignUserRoles(req *model.UserRoleAssignRequest) error {
	return s.repos.Permission.AssignRolesToUser(req.UserID, req.RoleIDs)
}

// GetUserRoles 获取用户角色
func (s *permissionService) GetUserRoles(userID uint64) ([]*model.RoleResponse, error) {
	roles, err := s.repos.Permission.GetUserRoles(userID)
	if err != nil {
		return nil, err
	}

	responses := make([]*model.RoleResponse, len(roles))
	for i, role := range roles {
		responses[i] = &model.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			IsSystem:    role.IsSystem,
			Status:      role.Status,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		}
	}

	return responses, nil
}

// CreatePermission 创建权限
func (s *permissionService) CreatePermission(req *model.PermissionCreateRequest) (*model.PermissionResponse, error) {
	permission := &model.Permission{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		IsSystem:    false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := s.repos.Permission.CreatePermission(permission)
	if err != nil {
		return nil, err
	}

	return &model.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		IsSystem:    permission.IsSystem,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}, nil
}

// UpdatePermission 更新权限
func (s *permissionService) UpdatePermission(id uint, req *model.PermissionUpdateRequest) (*model.PermissionResponse, error) {
	updates := make(map[string]interface{})
	if req.DisplayName != "" {
		updates["display_name"] = req.DisplayName
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Resource != "" {
		updates["resource"] = req.Resource
	}
	if req.Action != "" {
		updates["action"] = req.Action
	}
	updates["updated_at"] = time.Now()

	err := s.repos.Permission.UpdatePermission(id, updates)
	if err != nil {
		return nil, err
	}

	permission, err := s.repos.Permission.GetPermissionByID(id)
	if err != nil {
		return nil, err
	}

	return &model.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		IsSystem:    permission.IsSystem,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}, nil
}

// DeletePermission 删除权限
func (s *permissionService) DeletePermission(id uint) error {
	permission, err := s.repos.Permission.GetPermissionByID(id)
	if err != nil {
		return errors.New("权限不存在")
	}

	if permission.IsSystem {
		return errors.New("系统权限不允许删除")
	}

	return s.repos.Permission.DeletePermission(id)
}

// GetPermission 获取权限详情
func (s *permissionService) GetPermission(id uint) (*model.PermissionResponse, error) {
	permission, err := s.repos.Permission.GetPermissionByID(id)
	if err != nil {
		return nil, err
	}

	return &model.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		IsSystem:    permission.IsSystem,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}, nil
}

// GetPermissions 获取所有权限
func (s *permissionService) GetPermissions() ([]*model.PermissionResponse, error) {
	permissions, err := s.repos.Permission.GetPermissions()
	if err != nil {
		return nil, err
	}

	responses := make([]*model.PermissionResponse, len(permissions))
	for i, permission := range permissions {
		responses[i] = &model.PermissionResponse{
			ID:          permission.ID,
			Name:        permission.Name,
			DisplayName: permission.DisplayName,
			Description: permission.Description,
			Resource:    permission.Resource,
			Action:      permission.Action,
			IsSystem:    permission.IsSystem,
			CreatedAt:   permission.CreatedAt,
			UpdatedAt:   permission.UpdatedAt,
		}
	}

	return responses, nil
}

// InitializeSystemPermissions 初始化系统权限
func (s *permissionService) InitializeSystemPermissions() error {
	permissions := []*model.Permission{
		// 网站管理权限
		{Name: "site.view", DisplayName: "查看网站", Description: "查看网站信息", Resource: model.ResourceSite, Action: model.ActionView, IsSystem: true},
		{Name: "site.create", DisplayName: "创建网站", Description: "创建新网站", Resource: model.ResourceSite, Action: model.ActionCreate, IsSystem: true},
		{Name: "site.update", DisplayName: "更新网站", Description: "更新网站信息", Resource: model.ResourceSite, Action: model.ActionUpdate, IsSystem: true},
		{Name: "site.delete", DisplayName: "删除网站", Description: "删除网站", Resource: model.ResourceSite, Action: model.ActionDelete, IsSystem: true},
		{Name: "site.manage", DisplayName: "管理网站", Description: "完全管理网站", Resource: model.ResourceSite, Action: model.ActionManage, IsSystem: true},

		// 统计数据权限
		{Name: "stats.view", DisplayName: "查看统计", Description: "查看统计数据", Resource: model.ResourceStats, Action: model.ActionView, IsSystem: true},
		{Name: "stats.export", DisplayName: "导出统计", Description: "导出统计数据", Resource: model.ResourceStats, Action: model.ActionExport, IsSystem: true},

		// 用户管理权限
		{Name: "user.view", DisplayName: "查看用户", Description: "查看用户信息", Resource: model.ResourceUser, Action: model.ActionView, IsSystem: true},
		{Name: "user.create", DisplayName: "创建用户", Description: "创建新用户", Resource: model.ResourceUser, Action: model.ActionCreate, IsSystem: true},
		{Name: "user.update", DisplayName: "更新用户", Description: "更新用户信息", Resource: model.ResourceUser, Action: model.ActionUpdate, IsSystem: true},
		{Name: "user.delete", DisplayName: "删除用户", Description: "删除用户", Resource: model.ResourceUser, Action: model.ActionDelete, IsSystem: true},
		{Name: "user.manage", DisplayName: "管理用户", Description: "完全管理用户", Resource: model.ResourceUser, Action: model.ActionManage, IsSystem: true},

		// 角色管理权限
		{Name: "role.view", DisplayName: "查看角色", Description: "查看角色信息", Resource: model.ResourceRole, Action: model.ActionView, IsSystem: true},
		{Name: "role.create", DisplayName: "创建角色", Description: "创建新角色", Resource: model.ResourceRole, Action: model.ActionCreate, IsSystem: true},
		{Name: "role.update", DisplayName: "更新角色", Description: "更新角色信息", Resource: model.ResourceRole, Action: model.ActionUpdate, IsSystem: true},
		{Name: "role.delete", DisplayName: "删除角色", Description: "删除角色", Resource: model.ResourceRole, Action: model.ActionDelete, IsSystem: true},

		// 权限管理权限
		{Name: "permission.view", DisplayName: "查看权限", Description: "查看权限信息", Resource: model.ResourcePermission, Action: model.ActionView, IsSystem: true},
		{Name: "permission.create", DisplayName: "创建权限", Description: "创建新权限", Resource: model.ResourcePermission, Action: model.ActionCreate, IsSystem: true},
		{Name: "permission.update", DisplayName: "更新权限", Description: "更新权限信息", Resource: model.ResourcePermission, Action: model.ActionUpdate, IsSystem: true},
		{Name: "permission.delete", DisplayName: "删除权限", Description: "删除权限", Resource: model.ResourcePermission, Action: model.ActionDelete, IsSystem: true},

		// 系统管理权限
		{Name: "system.manage", DisplayName: "系统管理", Description: "系统管理权限", Resource: model.ResourceSystem, Action: model.ActionManage, IsSystem: true},
	}

	for _, permission := range permissions {
		// 检查权限是否已存在
		existing, err := s.repos.Permission.GetPermissionByID(0) // 这里需要改为按名称查找
		if err != nil || existing == nil {
			permission.CreatedAt = time.Now()
			permission.UpdatedAt = time.Now()
			if err := s.repos.Permission.CreatePermission(permission); err != nil {
				s.logger.Error("Failed to create permission", err, "permission", permission.Name)
			}
		}
	}

	return nil
}

// InitializeSystemRoles 初始化系统角色
func (s *permissionService) InitializeSystemRoles() error {
	// 获取所有权限
	allPermissions, err := s.repos.Permission.GetPermissions()
	if err != nil {
		return err
	}

	// 创建权限ID映射
	permissionMap := make(map[string]uint)
	for _, permission := range allPermissions {
		permissionMap[permission.Name] = permission.ID
	}

	// 定义系统角色
	roles := []struct {
		Name        string
		DisplayName string
		Description string
		Permissions []string
	}{
		{
			Name:        model.RoleAdmin,
			DisplayName: "超级管理员",
			Description: "拥有所有权限的超级管理员",
			Permissions: []string{
				"site.manage", "stats.view", "stats.export", "user.manage",
				"role.view", "role.create", "role.update", "role.delete",
				"permission.view", "permission.create", "permission.update", "permission.delete",
				"system.manage",
			},
		},
		{
			Name:        model.RoleUser,
			DisplayName: "普通用户",
			Description: "普通用户，可以管理自己的网站",
			Permissions: []string{
				"site.view", "site.create", "site.update", "site.delete",
				"stats.view", "stats.export",
			},
		},
		{
			Name:        model.RoleViewer,
			DisplayName: "访客",
			Description: "只能查看统计数据的访客",
			Permissions: []string{
				"site.view", "stats.view",
			},
		},
	}

	for _, roleData := range roles {
		// 检查角色是否已存在
		existing, err := s.repos.Permission.GetRoleByName(roleData.Name)
		if err != nil || existing == nil {
			// 创建角色
			role := &model.Role{
				Name:        roleData.Name,
				DisplayName: roleData.DisplayName,
				Description: roleData.Description,
				IsSystem:    true,
				Status:      1,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}

			err = s.repos.Permission.CreateRole(role)
			if err != nil {
				s.logger.Error("Failed to create role", err, "role", roleData.Name)
				continue
			}

			// 分配权限
			var permissionIDs []uint
			for _, permissionName := range roleData.Permissions {
				if permissionID, exists := permissionMap[permissionName]; exists {
					permissionIDs = append(permissionIDs, permissionID)
				}
			}

			if len(permissionIDs) > 0 {
				err = s.repos.Permission.AssignPermissionsToRole(role.ID, permissionIDs)
				if err != nil {
					s.logger.Error("Failed to assign permissions to role", err, "role", roleData.Name)
				}
			}
		}
	}

	return nil
}
