package middleware

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/limiter"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"web-stats-backend/internal/service"
)

// ErrorHandler 全局错误处理器
func ErrorHandler(c *fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError
	message := "Internal Server Error"

	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
		message = e.Message
	}

	return c.Status(code).JSON(fiber.Map{
		"error":   true,
		"message": message,
		"code":    code,
	})
}

// RequestID 请求ID中间件
func RequestID() fiber.Handler {
	return func(c *fiber.Ctx) error {
		requestID := c.Get("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Set("X-Request-ID", requestID)
		c.Locals("requestID", requestID)
		return c.Next()
	}
}

// RateLimit 限流中间件
func RateLimit() fiber.Handler {
	return limiter.New(limiter.Config{
		Max:        100,
		Expiration: 1 * time.Minute,
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.IP()
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(fiber.StatusTooManyRequests).JSON(fiber.Map{
				"error":   true,
				"message": "Too many requests",
				"code":    fiber.StatusTooManyRequests,
			})
		},
	})
}

// AuthRequired JWT认证中间件
func AuthRequired() fiber.Handler {
	return func(c *fiber.Ctx) error {
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "Authorization header required",
				"code":    fiber.StatusUnauthorized,
			})
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "Invalid authorization format",
				"code":    fiber.StatusUnauthorized,
			})
		}

		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, errors.New("unexpected signing method")
			}
			return []byte("your-super-secret-jwt-key-for-development"), nil // 使用与.env中相同的密钥
		})

		if err != nil || !token.Valid {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "Invalid token",
				"code":    fiber.StatusUnauthorized,
			})
		}

		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			c.Locals("user_id", uint64(claims["user_id"].(float64)))
			c.Locals("username", claims["username"].(string))
		}

		return c.Next()
	}
}

// AdminRequired 管理员权限中间件
func AdminRequired() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 这里可以添加管理员权限检查逻辑
		// 暂时简单实现，后续可以扩展
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"error":   true,
				"message": "Admin access required",
				"code":    fiber.StatusForbidden,
			})
		}

		// 检查用户是否为管理员
		// 这里可以查询数据库或缓存来验证管理员权限
		
		return c.Next()
	}
}

// CORS 跨域中间件配置
func CORSConfig() fiber.Handler {
	return func(c *fiber.Ctx) error {
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		c.Set("Access-Control-Allow-Headers", "Origin,Content-Type,Accept,Authorization")
		c.Set("Access-Control-Allow-Credentials", "true")

		if c.Method() == "OPTIONS" {
			return c.SendStatus(fiber.StatusOK)
		}

		return c.Next()
	}
}

// RequirePermission 权限验证中间件
func RequirePermission(permissionService service.PermissionService, resource, action string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取用户ID
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "未授权访问",
				"code":    fiber.StatusUnauthorized,
			})
		}

		var uid uint64
		switch v := userID.(type) {
		case uint64:
			uid = v
		case float64:
			uid = uint64(v)
		default:
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "无效的用户ID",
				"code":    fiber.StatusUnauthorized,
			})
		}

		// 检查权限
		hasPermission, err := permissionService.CheckPermission(uid, resource, action)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error":   true,
				"message": "权限检查失败",
				"code":    fiber.StatusInternalServerError,
			})
		}

		if !hasPermission {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"error":   true,
				"message": "权限不足",
				"code":    fiber.StatusForbidden,
			})
		}

		return c.Next()
	}
}

// RequireSitePermission 网站权限验证中间件（包含数据隔离）
func RequireSitePermission(permissionService service.PermissionService, action string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取用户ID
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "未授权访问",
				"code":    fiber.StatusUnauthorized,
			})
		}

		var uid uint64
		switch v := userID.(type) {
		case uint64:
			uid = v
		case float64:
			uid = uint64(v)
		default:
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error":   true,
				"message": "无效的用户ID",
				"code":    fiber.StatusUnauthorized,
			})
		}

		// 获取网站ID
		siteIDParam := c.Params("id")
		if siteIDParam == "" {
			siteIDParam = c.Params("siteId")
		}

		if siteIDParam != "" {
			// 解析网站ID
			var siteID uint64
			if _, err := fmt.Sscanf(siteIDParam, "%d", &siteID); err != nil {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"error":   true,
					"message": "无效的网站ID",
					"code":    fiber.StatusBadRequest,
				})
			}

			// 检查网站权限（包含数据隔离）
			hasPermission, err := permissionService.CheckSitePermission(uid, siteID, action)
			if err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
					"error":   true,
					"message": "权限检查失败",
					"code":    fiber.StatusInternalServerError,
				})
			}

			if !hasPermission {
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
					"error":   true,
					"message": "权限不足或网站不存在",
					"code":    fiber.StatusForbidden,
				})
			}
		} else {
			// 没有网站ID，检查基础权限
			hasPermission, err := permissionService.CheckPermission(uid, "site", action)
			if err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
					"error":   true,
					"message": "权限检查失败",
					"code":    fiber.StatusInternalServerError,
				})
			}

			if !hasPermission {
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
					"error":   true,
					"message": "权限不足",
					"code":    fiber.StatusForbidden,
				})
			}
		}

		return c.Next()
	}
}
