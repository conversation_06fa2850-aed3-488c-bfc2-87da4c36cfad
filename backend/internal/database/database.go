package database

import (
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"web-stats-backend/internal/config"
	"web-stats-backend/internal/model"
)

// New 创建数据库连接
func New(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := cfg.GetDSN()

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 获取底层的sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&model.User{},
		&model.Site{},
		&model.Visitor{},
		&model.SearchEngine{},
		&model.Keyword{},
		&model.Browser{},
		&model.OperatingSystem{},
		&model.Region{},
		&model.PageURL{},
		&model.RefererDomain{},
		&model.Session{},
		&model.IPAddress{},
		&model.UserAgent{},
		&model.Visit{},
		&model.PageView{},
		&model.StatsDaily{},
		&model.RealtimeStats{},
	)
}
