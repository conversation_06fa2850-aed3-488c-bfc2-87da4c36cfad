package handler

import (
	"strconv"

	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/model"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
	"web-stats-backend/pkg/validator"
)

// SiteHandler 网站处理器
type SiteHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewSiteHandler 创建网站处理器
func NewSiteHandler(services *service.Service, logger logger.Logger) *SiteHandler {
	return &SiteHandler{
		services: services,
		logger:   logger,
	}
}

// NewSiteHandlerWithoutDB 创建无数据库网站处理器（用于测试）
func NewSiteHandlerWithoutDB(logger logger.Logger) *SiteHandler {
	return &SiteHandler{
		services: nil,
		logger:   logger,
	}
}

// List 获取网站列表
func (h *SiteHandler) List(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 解析查询参数
	var req model.SiteListRequest
	if err := c.QueryParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("参数解析失败", fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Site list request in no-database mode", "user_id", userID)

		mockSites := []*model.SiteResponse{
			{
				ID:           1,
				SiteName:     "技术博客",
				Domain:       "blog.test.com",
				SiteURL:      "https://blog.test.com",
				SiteType:     "blog",
				Region:       "北京",
				Description:  "个人技术博客",
				TrackingCode: "TRACK001",
				IsPublic:     1,
				Status:       1,
				TotalVisits:  1250,
				TotalPageViews: 3680,
			},
			{
				ID:           2,
				SiteName:     "公司官网",
				Domain:       "company.test.com",
				SiteURL:      "https://company.test.com",
				SiteType:     "corporate",
				Region:       "上海",
				Description:  "公司官网",
				TrackingCode: "TRACK002",
				IsPublic:     1,
				Status:       1,
				TotalVisits:  890,
				TotalPageViews: 2340,
			},
		}

		return c.JSON(SuccessResponseWithPagination(mockSites, 2, req.Page, req.PageSize, "网站列表获取成功"))
	}

	// 真实数据库查询
	sites, total, err := h.services.Site.GetUserSites(userID, &req)
	if err != nil {
		h.logger.Error("Failed to get user sites", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("获取网站列表失败", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponseWithPagination(sites, total, req.Page, req.PageSize, "网站列表获取成功"))
}

// Create 创建网站
func (h *SiteHandler) Create(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 解析请求体
	var req model.SiteCreateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("请求体解析失败", fiber.StatusBadRequest))
	}

	// 验证请求数据
	if err := validateStruct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse(err.Error(), fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Site create request in no-database mode", "user_id", userID, "site_name", req.SiteName)

		mockSite := &model.SiteResponse{
			ID:           3,
			SiteName:     req.SiteName,
			Domain:       req.Domain,
			SiteURL:      req.SiteURL,
			SiteType:     req.SiteType,
			Region:       req.Region,
			Description:  req.Description,
			TrackingCode: "TRACK003",
			IsPublic:     req.IsPublic,
			Status:       1,
		}

		return c.Status(fiber.StatusCreated).JSON(SuccessResponse(mockSite, "网站创建成功"))
	}

	// 真实数据库操作
	site, err := h.services.Site.Create(userID, &req)
	if err != nil {
		h.logger.Error("Failed to create site", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse(err.Error(), fiber.StatusInternalServerError))
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse(site, "网站创建成功"))
}

// GetByID 根据ID获取网站
func (h *SiteHandler) GetByID(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 获取网站ID
	siteIDStr := c.Params("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("无效的网站ID", fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Site get request in no-database mode", "user_id", userID, "site_id", siteID)

		mockSite := &model.SiteResponse{
			ID:           siteID,
			SiteName:     "技术博客",
			Domain:       "blog.test.com",
			SiteURL:      "https://blog.test.com",
			SiteType:     "blog",
			Region:       "北京",
			Description:  "个人技术博客",
			TrackingCode: "TRACK001",
			IsPublic:     1,
			Status:       1,
			TotalVisits:  1250,
			TotalPageViews: 3680,
		}

		return c.JSON(SuccessResponse(mockSite, "网站信息获取成功"))
	}

	// 真实数据库查询
	site, err := h.services.Site.GetSiteStats(siteID, userID)
	if err != nil {
		h.logger.Error("Failed to get site", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse(err.Error(), fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(site, "网站信息获取成功"))
}

// Update 更新网站
func (h *SiteHandler) Update(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 获取网站ID
	siteIDStr := c.Params("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("无效的网站ID", fiber.StatusBadRequest))
	}

	// 解析请求体
	var req model.SiteUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("请求体解析失败", fiber.StatusBadRequest))
	}

	// 验证请求数据
	if err := validateStruct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse(err.Error(), fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Site update request in no-database mode", "user_id", userID, "site_id", siteID)

		mockSite := &model.SiteResponse{
			ID:           siteID,
			SiteName:     req.SiteName,
			Domain:       req.Domain,
			SiteURL:      req.SiteURL,
			SiteType:     req.SiteType,
			Region:       req.Region,
			Description:  req.Description,
			TrackingCode: "TRACK001",
			IsPublic:     req.IsPublic,
			Status:       req.Status,
		}

		return c.JSON(SuccessResponse(mockSite, "网站更新成功"))
	}

	// 真实数据库操作
	site, err := h.services.Site.Update(siteID, userID, &req)
	if err != nil {
		h.logger.Error("Failed to update site", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse(err.Error(), fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(site, "网站更新成功"))
}

// Delete 删除网站
func (h *SiteHandler) Delete(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 获取网站ID
	siteIDStr := c.Params("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("无效的网站ID", fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Site delete request in no-database mode", "user_id", userID, "site_id", siteID)
		return c.JSON(SuccessResponse(nil, "网站删除成功"))
	}

	// 真实数据库操作
	err = h.services.Site.Delete(siteID, userID)
	if err != nil {
		h.logger.Error("Failed to delete site", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse(err.Error(), fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(nil, "网站删除成功"))
}

// GenerateTrackingCode 生成跟踪代码
func (h *SiteHandler) GenerateTrackingCode(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Generate tracking code request in no-database mode", "user_id", userID)
		return c.JSON(SuccessResponse(map[string]string{
			"tracking_code": "TRACK" + strconv.FormatInt(int64(userID), 10) + "001",
		}, "跟踪代码生成成功"))
	}

	// 真实生成
	trackingCode := h.services.Site.GenerateTrackingCode()
	return c.JSON(SuccessResponse(map[string]string{
		"tracking_code": trackingCode,
	}, "跟踪代码生成成功"))
}

// GetTrackingCode 获取网站跟踪代码
func (h *SiteHandler) GetTrackingCode(c *fiber.Ctx) error {
	// 获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("未授权", fiber.StatusUnauthorized))
	}

	// 获取网站ID
	siteIDStr := c.Params("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("无效的网站ID", fiber.StatusBadRequest))
	}

	// 无数据库模式
	if h.services == nil {
		h.logger.Info("Get tracking code request in no-database mode", "user_id", userID, "site_id", siteID)

		trackingCode := "TRACK001"
		jsCode := generateJSTrackingCode(trackingCode, "http://localhost:9002")

		return c.JSON(SuccessResponse(map[string]interface{}{
			"tracking_code": trackingCode,
			"js_code":       jsCode,
			"install_guide": getInstallGuide(trackingCode),
		}, "跟踪代码获取成功"))
	}

	// 真实数据库查询
	site, err := h.services.Site.GetByID(siteID)
	if err != nil {
		h.logger.Error("Failed to get site", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("获取网站信息失败", fiber.StatusInternalServerError))
	}

	// 验证所有权
	if err := h.services.Site.ValidateSiteOwnership(siteID, userID); err != nil {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse(err.Error(), fiber.StatusForbidden))
	}

	// 生成JS代码
	jsCode := generateJSTrackingCode(site.TrackingCode, "http://localhost:9002")

	return c.JSON(SuccessResponse(map[string]interface{}{
		"tracking_code": site.TrackingCode,
		"js_code":       jsCode,
		"install_guide": getInstallGuide(site.TrackingCode),
	}, "跟踪代码获取成功"))
}

// generateJSTrackingCode 生成JS跟踪代码
func generateJSTrackingCode(trackingCode, apiURL string) string {
	return `<!-- WebStats 统计代码开始 -->
<script>
  window._wsConfig = {
    siteId: '` + trackingCode + `',
    apiUrl: '` + apiURL + `/api/v1',
    debug: false
  };
</script>
<script async src="` + apiURL + `/js/track.min.js"></script>
<!-- WebStats 统计代码结束 -->`
}

// getInstallGuide 获取安装指南
func getInstallGuide(trackingCode string) map[string]interface{} {
	return map[string]interface{}{
		"step1": "复制上面的统计代码",
		"step2": "将代码粘贴到网站每个页面的 </head> 标签前",
		"step3": "保存并上传文件到服务器",
		"step4": "访问网站页面，等待几分钟后查看统计数据",
		"notes": []string{
			"统计代码必须放在每个需要统计的页面中",
			"建议放在网站模板的头部，这样所有页面都会自动包含",
			"代码是异步加载的，不会影响网站加载速度",
			"首次安装后，数据可能需要几分钟才能显示",
		},
		"examples": map[string]string{
			"html": `<!DOCTYPE html>
<html>
<head>
    <title>我的网站</title>
    <!-- 其他头部内容 -->

    <!-- WebStats 统计代码开始 -->
    <script>
      window._wsConfig = {
        siteId: '` + trackingCode + `',
        apiUrl: 'http://localhost:9002/api/v1',
        debug: false
      };
    </script>
    <script async src="http://localhost:9002/js/track.min.js"></script>
    <!-- WebStats 统计代码结束 -->
</head>
<body>
    <!-- 页面内容 -->
</body>
</html>`,
			"wordpress": `// 在WordPress主题的functions.php文件中添加：
function add_webstats_tracking() {
    ?>
    <!-- WebStats 统计代码开始 -->
    <script>
      window._wsConfig = {
        siteId: '` + trackingCode + `',
        apiUrl: 'http://localhost:9002/api/v1',
        debug: false
      };
    </script>
    <script async src="http://localhost:9002/js/track.min.js"></script>
    <!-- WebStats 统计代码结束 -->
    <?php
}
add_action('wp_head', 'add_webstats_tracking');`,
		},
	}
}

// validateStruct 验证结构体
func validateStruct(s interface{}) error {
	return validator.ValidateStruct(s)
}

// getUserIDFromContext 从上下文获取用户ID
func getUserIDFromContext(c *fiber.Ctx) uint64 {
	userID := c.Locals("user_id")
	if userID == nil {
		return 0
	}

	switch v := userID.(type) {
	case uint64:
		return v
	case float64:
		return uint64(v)
	case int:
		return uint64(v)
	case string:
		id, _ := strconv.ParseUint(v, 10, 64)
		return id
	default:
		return 0
	}
}


