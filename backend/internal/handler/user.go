package handler

import (
	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// UserHandler 用户处理器
type UserHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewUserHandler 创建用户处理器
func NewUserHandler(services *service.Service, logger logger.Logger) *UserHandler {
	return &UserHandler{
		services: services,
		logger:   logger,
	}
}

// NewUserHandlerWithoutDB 创建无数据库用户处理器（用于测试）
func NewUserHandlerWithoutDB(logger logger.Logger) *UserHandler {
	return &UserHandler{
		services: nil,
		logger:   logger,
	}
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *fiber.Ctx) error {
	userID := c.Locals("userID").(uint)
	
	user, err := h.services.User.GetByID(userID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse("User not found", fiber.StatusNotFound))
	}

	// 隐藏密码
	user.PasswordHash = ""

	return c.JSON(SuccessResponse(user, "User profile retrieved successfully"))
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *fiber.Ctx) error {
	// TODO: 实现用户资料更新逻辑
	return c.JSON(SuccessResponse(nil, "Profile updated successfully"))
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *fiber.Ctx) error {
	// TODO: 实现密码修改逻辑
	return c.JSON(SuccessResponse(nil, "Password changed successfully"))
}

// GetUserPermissions 获取用户权限
func (h *UserHandler) GetUserPermissions(c *fiber.Ctx) error {
	_ = c.Params("id") // 暂时不使用，避免编译错误

	// 无数据库模式：返回模拟权限
	if h.services == nil {
		h.logger.Info("GetUserPermissions request in no-database mode")

		// 模拟权限数据
		permissions := map[string]interface{}{
			"permissions": []string{
				"dashboard.view",
				"site.view",
				"site.create",
				"site.update",
				"site.delete",
				"stats.view",
				"user.view",
				"user.update",
			},
			"roles": []string{"admin"},
		}

		return c.JSON(SuccessResponse(permissions, "User permissions retrieved successfully (mock mode)"))
	}

	// TODO: 实现真实的权限查询逻辑
	// 这里应该从数据库查询用户的角色和权限
	permissions := map[string]interface{}{
		"permissions": []string{
			"dashboard.view",
			"site.view",
			"site.create",
			"site.update",
			"site.delete",
			"stats.view",
		},
		"roles": []string{"user"},
	}

	return c.JSON(SuccessResponse(permissions, "User permissions retrieved successfully"))
}
