package handler

import (
	"math/rand"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/model"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// StatsHandler 统计处理器
type StatsHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewStatsHandler 创建统计处理器
func NewStatsHandler(services *service.Service, logger logger.Logger) *StatsHandler {
	return &StatsHandler{
		services: services,
		logger:   logger,
	}
}

// NewStatsHandlerWithoutDB 创建无数据库统计处理器（用于测试）
func NewStatsHandlerWithoutDB(logger logger.Logger) *StatsHandler {
	return &StatsHandler{
		services: nil,
		logger:   logger,
	}
}

// Overview 获取概况数据
func (h *StatsHandler) Overview(c *fiber.Ctx) error {
	siteIDStr := c.<PERSON>("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	startDateStr := c.Query("start_date", time.Now().AddDate(0, 0, -7).Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Overview request in no-database mode", "site_id", siteID)

		// 使用网站ID作为随机种子，确保数据一致性
		rand.Seed(int64(siteID) + startDate.Unix())

		mockData := map[string]interface{}{
			"pv":                rand.Int63n(50000) + 10000,
			"pv_change":         (rand.Float64() - 0.5) * 40,
			"uv":                rand.Int63n(20000) + 5000,
			"uv_change":         (rand.Float64() - 0.5) * 30,
			"ip":                rand.Int63n(15000) + 3000,
			"ip_change":         (rand.Float64() - 0.5) * 25,
			"online":            rand.Int63n(100) + 20,
			"bounce_rate":       rand.Float64()*30 + 30,
			"avg_duration":      rand.Int63n(300) + 60,
			"new_visitors":      rand.Int63n(8000) + 2000,
			"returning_visitors": rand.Int63n(5000) + 1000,
		}

		return c.JSON(SuccessResponse(mockData, "Overview data retrieved successfully"))
	}

	// 真实数据库查询
	stats, err := h.services.Stats.GetOverview(siteID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to get overview stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get overview data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(stats, "Overview data retrieved successfully"))
}

// Trend 获取趋势数据
func (h *StatsHandler) Trend(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	startDateStr := c.Query("start_date", time.Now().AddDate(0, 0, -7).Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))
	granularity := c.Query("granularity", "day") // day 或 hour

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Trend request in no-database mode", "site_id", siteID, "granularity", granularity)

		var mockData []*model.TrendData
		if granularity == "hour" {
			// 生成24小时的模拟数据
			for i := 0; i < 24; i++ {
				mockData = append(mockData, &model.TrendData{
					Date:         startDate,
					Hour:         i,
					PV:           int64(100 + i*20 + (i%3)*50),
					UV:           int64(30 + i*5 + (i%2)*10),
					IPCount:      int64(25 + i*4),
					SessionCount: int64(35 + i*6),
					NewVisitors:  int64(15 + i*2),
					BounceCount:  int64(12 + i*2),
					BounceRate:   float64(30 + i%10),
					AvgDuration:  float64(120 + i*10),
				})
			}
		} else {
			// 生成7天的模拟数据
			for i := 0; i < 7; i++ {
				date := startDate.AddDate(0, 0, i)
				mockData = append(mockData, &model.TrendData{
					Date:         date,
					PV:           int64(1500 + i*200 + (i%3)*300),
					UV:           int64(450 + i*50 + (i%2)*80),
					IPCount:      int64(400 + i*45),
					SessionCount: int64(520 + i*60),
					NewVisitors:  int64(250 + i*30),
					BounceCount:  int64(180 + i*25),
					BounceRate:   float64(32 + i%8),
					AvgDuration:  float64(165 + i*15),
				})
			}
		}

		return c.JSON(SuccessResponse(mockData, "Trend data retrieved successfully"))
	}

	// 真实数据库查询
	trends, err := h.services.Stats.GetTrend(siteID, startDate, endDate, granularity)
	if err != nil {
		h.logger.Error("Failed to get trend stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get trend data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(trends, "Trend data retrieved successfully"))
}

// Source 获取来源数据
func (h *StatsHandler) Source(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	limit := c.QueryInt("limit", 10)
	startDateStr := c.Query("start_date", time.Now().Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	_, err = time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Source request in no-database mode", "site_id", siteID, "limit", limit)

		rand.Seed(int64(siteID) + startDate.Unix() + 1)

		sources := []map[string]interface{}{
			{
				"source":      "百度",
				"source_type": "search",
				"pv":          rand.Int63n(5000) + 1000,
				"uv":          rand.Int63n(2000) + 500,
				"percentage":  rand.Float64()*20 + 25,
			},
			{
				"source":      "谷歌",
				"source_type": "search",
				"pv":          rand.Int63n(3000) + 800,
				"uv":          rand.Int63n(1500) + 400,
				"percentage":  rand.Float64()*15 + 15,
			},
			{
				"source":      "直接访问",
				"source_type": "direct",
				"pv":          rand.Int63n(2000) + 500,
				"uv":          rand.Int63n(1000) + 300,
				"percentage":  rand.Float64()*10 + 10,
			},
			{
				"source":      "微信",
				"source_type": "social",
				"pv":          rand.Int63n(1500) + 300,
				"uv":          rand.Int63n(800) + 200,
				"percentage":  rand.Float64()*8 + 5,
			},
			{
				"source":      "其他",
				"source_type": "referral",
				"pv":          rand.Int63n(1000) + 200,
				"uv":          rand.Int63n(500) + 100,
				"percentage":  rand.Float64()*5 + 3,
			},
		}

		if limit > 0 && limit < len(sources) {
			sources = sources[:limit]
		}

		result := map[string]interface{}{
			"data":  sources,
			"total": len(sources),
		}

		return c.JSON(SuccessResponse(result, "Source data retrieved successfully"))
	}

	// 真实数据库查询
	// TODO: 实现真实的数据库查询
	return c.JSON(SuccessResponse(nil, "Source data retrieved successfully"))
}

// Pages 获取页面数据
func (h *StatsHandler) Pages(c *fiber.Ctx) error {
	// TODO: 实现页面数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Pages data retrieved successfully"))
}

// Visitors 获取访客数据
func (h *StatsHandler) Visitors(c *fiber.Ctx) error {
	// TODO: 实现访客数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Visitors data retrieved successfully"))
}

// VisitorType 获取访客类型数据
func (h *StatsHandler) VisitorType(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	startDateStr := c.Query("start_date", time.Now().Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	_, err = time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("VisitorType request in no-database mode", "site_id", siteID)

		rand.Seed(int64(siteID) + startDate.Unix() + 3)

		newVisitors := rand.Int63n(8000) + 2000
		returningVisitors := rand.Int63n(5000) + 1000
		total := newVisitors + returningVisitors

		result := map[string]interface{}{
			"new_visitors": map[string]interface{}{
				"count":        newVisitors,
				"percentage":   float64(newVisitors) / float64(total) * 100,
				"avg_duration": rand.Int63n(200) + 60,
				"bounce_rate":  rand.Float64()*20 + 40,
			},
			"returning_visitors": map[string]interface{}{
				"count":        returningVisitors,
				"percentage":   float64(returningVisitors) / float64(total) * 100,
				"avg_duration": rand.Int63n(300) + 120,
				"bounce_rate":  rand.Float64()*15 + 25,
			},
		}

		return c.JSON(SuccessResponse(result, "Visitor type data retrieved successfully"))
	}

	// 真实数据库查询
	// TODO: 实现真实的数据库查询
	return c.JSON(SuccessResponse(nil, "Visitor type data retrieved successfully"))
}

// Realtime 获取实时数据
func (h *StatsHandler) Realtime(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Realtime request in no-database mode", "site_id", siteID)

		mockData := &model.RealtimeStats{
			SiteID:      siteID,
			OnlineCount: 23,
			PV5Min:      45,
			UV5Min:      18,
			LastUpdate:  time.Now(),
			RecentVisitors: []*model.RecentVisitorInfo{
				{
					VisitorID:  "visitor_001",
					PageURL:    "https://blog.example.com/posts/go-tutorial",
					PageTitle:  "Go语言入门教程",
					Referrer:   "https://google.com",
					Location:   "北京市",
					DeviceType: "desktop",
					Browser:    "Chrome",
					VisitTime:  time.Now().Add(-2 * time.Minute),
				},
				{
					VisitorID:  "visitor_002",
					PageURL:    "https://blog.example.com/",
					PageTitle:  "首页",
					Referrer:   "direct",
					Location:   "上海市",
					DeviceType: "mobile",
					Browser:    "Safari",
					VisitTime:  time.Now().Add(-5 * time.Minute),
				},
			},
			TopPages: []*model.RealtimePageInfo{
				{
					PageURL:   "https://blog.example.com/",
					PageTitle: "首页",
					PV5Min:    12,
					UV5Min:    8,
				},
				{
					PageURL:   "https://blog.example.com/posts/go-tutorial",
					PageTitle: "Go语言入门教程",
					PV5Min:    8,
					UV5Min:    6,
				},
			},
		}

		return c.JSON(SuccessResponse(mockData, "Realtime data retrieved successfully"))
	}

	// 真实数据库查询
	stats, err := h.services.Stats.GetRealtime(siteID)
	if err != nil {
		h.logger.Error("Failed to get realtime stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get realtime data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(stats, "Realtime data retrieved successfully"))
}

// Regions 获取地区数据
func (h *StatsHandler) Regions(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	limit := c.QueryInt("limit", 10)
	startDateStr := c.Query("start_date", time.Now().Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	_, err = time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Regions request in no-database mode", "site_id", siteID, "limit", limit)

		rand.Seed(int64(siteID) + startDate.Unix() + 2)

		regionNames := []string{"北京", "上海", "广东", "浙江", "江苏", "山东", "四川", "湖北", "河南", "福建"}
		var regions []map[string]interface{}

		for i, region := range regionNames {
			if limit > 0 && i >= limit {
				break
			}

			pv := rand.Int63n(3000) + 500
			uv := rand.Int63n(1500) + 250

			regions = append(regions, map[string]interface{}{
				"region":     region,
				"pv":         pv,
				"uv":         uv,
				"percentage": rand.Float64()*15 + 5,
			})
		}

		result := map[string]interface{}{
			"data":  regions,
			"total": len(regions),
		}

		return c.JSON(SuccessResponse(result, "Regions data retrieved successfully"))
	}

	// 真实数据库查询
	// TODO: 实现真实的数据库查询
	return c.JSON(SuccessResponse(nil, "Regions data retrieved successfully"))
}

// Devices 获取设备数据
func (h *StatsHandler) Devices(c *fiber.Ctx) error {
	// TODO: 实现设备数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Devices data retrieved successfully"))
}
