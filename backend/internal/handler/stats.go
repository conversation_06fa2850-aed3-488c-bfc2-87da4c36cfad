package handler

import (
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/model"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// StatsHandler 统计处理器
type StatsHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewStatsHandler 创建统计处理器
func NewStatsHandler(services *service.Service, logger logger.Logger) *StatsHandler {
	return &StatsHandler{
		services: services,
		logger:   logger,
	}
}

// NewStatsHandlerWithoutDB 创建无数据库统计处理器（用于测试）
func NewStatsHandlerWithoutDB(logger logger.Logger) *StatsHandler {
	return &StatsHandler{
		services: nil,
		logger:   logger,
	}
}

// Overview 获取概况数据
func (h *StatsHandler) Overview(c *fiber.Ctx) error {
	siteIDStr := c.<PERSON>("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	startDateStr := c.Query("start_date", time.Now().AddDate(0, 0, -7).Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Overview request in no-database mode", "site_id", siteID)

		mockData := &model.OverviewStats{
			SiteID:            siteID,
			StartDate:         startDate,
			EndDate:           endDate,
			PV:                12580,
			UV:                3420,
			IPCount:           3180,
			SessionCount:      4250,
			NewVisitors:       2100,
			ReturningVisitors: 1320,
			BounceCount:       1530,
			BounceRate:        36.0,
			AvgDuration:       185.5,
			AvgPageViews:      2.96,
			DesktopPV:         7548,
			MobilePV:          4632,
			TabletPV:          400,
			SearchPV:          6290,
			DirectPV:          3145,
			ExternalPV:        3145,
			PVGrowth:          12.5,
			UVGrowth:          8.3,
			BounceRateChange:  -2.1,
			AvgDurationChange: 15.2,
		}

		return c.JSON(SuccessResponse(mockData, "Overview data retrieved successfully"))
	}

	// 真实数据库查询
	stats, err := h.services.Stats.GetOverview(siteID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to get overview stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get overview data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(stats, "Overview data retrieved successfully"))
}

// Trend 获取趋势数据
func (h *StatsHandler) Trend(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 获取查询参数
	startDateStr := c.Query("start_date", time.Now().AddDate(0, 0, -7).Format("2006-01-02"))
	endDateStr := c.Query("end_date", time.Now().Format("2006-01-02"))
	granularity := c.Query("granularity", "day") // day 或 hour

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid start_date format", fiber.StatusBadRequest))
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid end_date format", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Trend request in no-database mode", "site_id", siteID, "granularity", granularity)

		var mockData []*model.TrendData
		if granularity == "hour" {
			// 生成24小时的模拟数据
			for i := 0; i < 24; i++ {
				mockData = append(mockData, &model.TrendData{
					Date:         startDate,
					Hour:         i,
					PV:           int64(100 + i*20 + (i%3)*50),
					UV:           int64(30 + i*5 + (i%2)*10),
					IPCount:      int64(25 + i*4),
					SessionCount: int64(35 + i*6),
					NewVisitors:  int64(15 + i*2),
					BounceCount:  int64(12 + i*2),
					BounceRate:   float64(30 + i%10),
					AvgDuration:  float64(120 + i*10),
				})
			}
		} else {
			// 生成7天的模拟数据
			for i := 0; i < 7; i++ {
				date := startDate.AddDate(0, 0, i)
				mockData = append(mockData, &model.TrendData{
					Date:         date,
					PV:           int64(1500 + i*200 + (i%3)*300),
					UV:           int64(450 + i*50 + (i%2)*80),
					IPCount:      int64(400 + i*45),
					SessionCount: int64(520 + i*60),
					NewVisitors:  int64(250 + i*30),
					BounceCount:  int64(180 + i*25),
					BounceRate:   float64(32 + i%8),
					AvgDuration:  float64(165 + i*15),
				})
			}
		}

		return c.JSON(SuccessResponse(mockData, "Trend data retrieved successfully"))
	}

	// 真实数据库查询
	trends, err := h.services.Stats.GetTrend(siteID, startDate, endDate, granularity)
	if err != nil {
		h.logger.Error("Failed to get trend stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get trend data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(trends, "Trend data retrieved successfully"))
}

// Source 获取来源数据
func (h *StatsHandler) Source(c *fiber.Ctx) error {
	// TODO: 实现来源数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Source data retrieved successfully"))
}

// Pages 获取页面数据
func (h *StatsHandler) Pages(c *fiber.Ctx) error {
	// TODO: 实现页面数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Pages data retrieved successfully"))
}

// Visitors 获取访客数据
func (h *StatsHandler) Visitors(c *fiber.Ctx) error {
	// TODO: 实现访客数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Visitors data retrieved successfully"))
}

// Realtime 获取实时数据
func (h *StatsHandler) Realtime(c *fiber.Ctx) error {
	siteIDStr := c.Params("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 64)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Realtime request in no-database mode", "site_id", siteID)

		mockData := &model.RealtimeStats{
			SiteID:      siteID,
			OnlineCount: 23,
			PV5Min:      45,
			UV5Min:      18,
			LastUpdate:  time.Now(),
			RecentVisitors: []*model.RecentVisitorInfo{
				{
					VisitorID:  "visitor_001",
					PageURL:    "https://blog.example.com/posts/go-tutorial",
					PageTitle:  "Go语言入门教程",
					Referrer:   "https://google.com",
					Location:   "北京市",
					DeviceType: "desktop",
					Browser:    "Chrome",
					VisitTime:  time.Now().Add(-2 * time.Minute),
				},
				{
					VisitorID:  "visitor_002",
					PageURL:    "https://blog.example.com/",
					PageTitle:  "首页",
					Referrer:   "direct",
					Location:   "上海市",
					DeviceType: "mobile",
					Browser:    "Safari",
					VisitTime:  time.Now().Add(-5 * time.Minute),
				},
			},
			TopPages: []*model.RealtimePageInfo{
				{
					PageURL:   "https://blog.example.com/",
					PageTitle: "首页",
					PV5Min:    12,
					UV5Min:    8,
				},
				{
					PageURL:   "https://blog.example.com/posts/go-tutorial",
					PageTitle: "Go语言入门教程",
					PV5Min:    8,
					UV5Min:    6,
				},
			},
		}

		return c.JSON(SuccessResponse(mockData, "Realtime data retrieved successfully"))
	}

	// 真实数据库查询
	stats, err := h.services.Stats.GetRealtime(siteID)
	if err != nil {
		h.logger.Error("Failed to get realtime stats", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to get realtime data", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(stats, "Realtime data retrieved successfully"))
}

// Regions 获取地区数据
func (h *StatsHandler) Regions(c *fiber.Ctx) error {
	// TODO: 实现地区数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Regions data retrieved successfully"))
}

// Devices 获取设备数据
func (h *StatsHandler) Devices(c *fiber.Ctx) error {
	// TODO: 实现设备数据获取逻辑
	return c.JSON(SuccessResponse(nil, "Devices data retrieved successfully"))
}
