package handler

import (
	"crypto/md5"
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/model"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// TrackHandler 数据收集处理器
type TrackHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewTrackHandler 创建数据收集处理器
func NewTrackHandler(services *service.Service, logger logger.Logger) *TrackHandler {
	return &TrackHandler{
		services: services,
		logger:   logger,
	}
}

// NewTrackHandlerWithoutDB 创建无数据库数据收集处理器（用于测试）
func NewTrackHandlerWithoutDB(logger logger.Logger) *TrackHandler {
	return &TrackHandler{
		services: nil,
		logger:   logger,
	}
}

// Collect 收集访问数据
func (h *TrackHandler) Collect(c *fiber.Ctx) error {
	// 解析请求数据
	var req model.TrackRequest
	if err := c.BodyParser(&req); err != nil {
		h.logger.Error("Failed to parse track request", err)
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid request body", fiber.StatusBadRequest))
	}

	// 获取客户端IP
	req.ClientIP = c.IP()

	// 设置时间戳（如果客户端没有提供）
	if req.Timestamp == 0 {
		req.Timestamp = time.Now().Unix()
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Track request in no-database mode", "site_id", req.SiteID, "page_url", req.PageURL)

		// 生成模拟的访客ID和会话ID
		visitorID := req.VisitorID
		if visitorID == "" {
			visitorID = generateMockVisitorID(req.ClientIP, req.UserAgent)
		}

		sessionID := req.SessionID
		if sessionID == "" {
			sessionID = generateMockSessionID(visitorID)
		}

		response := model.TrackResponse{
			Success:   true,
			Message:   "Data collected successfully (mock mode)",
			VisitorID: visitorID,
			SessionID: sessionID,
		}

		// 记录收集的数据（用于调试）
		h.logger.Info("Mock data collected",
			"site_id", req.SiteID,
			"visitor_id", visitorID,
			"session_id", sessionID,
			"page_url", req.PageURL,
			"client_ip", req.ClientIP,
		)

		return c.JSON(SuccessResponse(response, "Data collected successfully"))
	}

	// 真实数据库模式的数据收集逻辑
	h.logger.Info("Processing track request", "site_id", req.SiteID, "page_url", req.PageURL)

	// 1. 验证网站ID
	site, err := h.services.Site.GetByTrackingCode(req.SiteID)
	if err != nil {
		h.logger.Error("Invalid site ID", err, "site_id", req.SiteID)
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid site ID", fiber.StatusBadRequest))
	}

	// 2. 处理访客和会话信息
	visitorID := req.VisitorID
	if visitorID == "" {
		visitorID = generateVisitorID(req.ClientIP, req.UserAgent)
	}

	sessionID := req.SessionID
	if sessionID == "" {
		sessionID = generateSessionID(visitorID)
	}

	// 3. 存储访问数据（简化版本）
	trackData := &model.ProcessedTrackData{
		SiteID:       site.ID,
		PageURL:      req.PageURL,
		PageTitle:    req.PageTitle,
		VisitTime:    time.Unix(req.Timestamp, 0),
		StayDuration: req.StayDuration,
		ScrollDepth:  req.ScrollDepth,
		ClickCount:   req.ClickCount,
		LoadTime:     req.LoadTime,
		IsBounce:     req.IsBounce,
		IsNewVisitor: req.IsNewVisitor,
	}

	// TODO: 完整的数据处理和存储
	// - 解析User Agent
	// - 解析IP地址获取地理位置
	// - 存储到visits表
	// - 更新实时统计

	h.logger.Info("Track data processed",
		"site_id", site.ID,
		"visitor_id", visitorID,
		"page_url", trackData.PageURL,
		"load_time", trackData.LoadTime)

	response := model.TrackResponse{
		Success:   true,
		Message:   "Data collected successfully",
		VisitorID: visitorID,
		SessionID: sessionID,
	}

	return c.JSON(SuccessResponse(response, "Data collected successfully"))
}

// GetOnlineCount 获取在线人数
func (h *TrackHandler) GetOnlineCount(c *fiber.Ctx) error {
	siteID := c.Params("siteId")

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("GetOnlineCount request in no-database mode", "site_id", siteID)

		// 生成模拟的在线人数（1-50之间的随机数）
		onlineCount := (int(time.Now().Unix()) % 50) + 1

		response := model.OnlineStatsResponse{
			SiteID:      siteID,
			OnlineCount: onlineCount,
			PV5Min:      onlineCount * 2,
			UV5Min:      onlineCount,
			LastUpdate:  time.Now(),
		}

		return c.JSON(SuccessResponse(response, "Online count retrieved successfully"))
	}

	// TODO: 实现真实的在线人数获取逻辑
	return c.JSON(SuccessResponse(fiber.Map{
		"online_count": 0,
	}, "Online count retrieved successfully"))
}

// generateMockVisitorID 生成模拟访客ID
func generateMockVisitorID(ip, userAgent string) string {
	data := fmt.Sprintf("%s_%s_%d", ip, userAgent, time.Now().Unix()/3600) // 按小时变化
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("visitor_%x", hash)[:20]
}

// generateMockSessionID 生成模拟会话ID
func generateMockSessionID(visitorID string) string {
	data := fmt.Sprintf("%s_%d", visitorID, time.Now().Unix()/1800) // 按30分钟变化
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("session_%x", hash)[:20]
}

// generateVisitorID 生成访客ID
func generateVisitorID(ip, userAgent string) string {
	data := fmt.Sprintf("%s_%s_%d", ip, userAgent, time.Now().Unix()/86400) // 按天变化
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)[:32]
}

// generateSessionID 生成会话ID
func generateSessionID(visitorID string) string {
	data := fmt.Sprintf("%s_%d", visitorID, time.Now().Unix()/1800) // 按30分钟变化
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)[:32]
}
