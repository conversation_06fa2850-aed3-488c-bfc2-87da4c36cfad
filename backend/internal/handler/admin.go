package handler

import (
	"github.com/gofiber/fiber/v2"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// AdminHandler 管理员处理器
type AdminHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewAdminHandler 创建管理员处理器
func NewAdminHandler(services *service.Service, logger logger.Logger) *AdminHandler {
	return &AdminHandler{
		services: services,
		logger:   logger,
	}
}

// NewAdminHandlerWithoutDB 创建无数据库管理员处理器（用于测试）
func NewAdminHandlerWithoutDB(logger logger.Logger) *AdminHandler {
	return &AdminHandler{
		services: nil,
		logger:   logger,
	}
}

// ListUsers 获取用户列表
func (h *AdminHandler) ListUsers(c *fiber.Ctx) error {
	// TODO: 实现用户列表获取逻辑
	return c.JSON(SuccessResponse(nil, "Users retrieved successfully"))
}

// ListSites 获取网站列表
func (h *AdminHandler) ListSites(c *fiber.Ctx) error {
	// TODO: 实现网站列表获取逻辑
	return c.JSON(SuccessResponse(nil, "Sites retrieved successfully"))
}

// SystemStats 获取系统统计
func (h *AdminHandler) SystemStats(c *fiber.Ctx) error {
	// TODO: 实现系统统计获取逻辑
	return c.JSON(SuccessResponse(nil, "System stats retrieved successfully"))
}
