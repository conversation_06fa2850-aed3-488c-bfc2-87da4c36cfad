package handler

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"web-stats-backend/internal/model"
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	services *service.Service
	logger   logger.Logger
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(services *service.Service, logger logger.Logger) *AuthHandler {
	return &AuthHandler{
		services: services,
		logger:   logger,
	}
}

// NewAuthHandlerWithoutDB 创建无数据库认证处理器（用于测试）
func NewAuthHandlerWithoutDB(logger logger.Logger) *AuthHandler {
	return &AuthHandler{
		services: nil,
		logger:   logger,
	}
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
	Nickname string `json:"nickname"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string     `json:"token"`
	ExpiresAt time.Time  `json:"expires_at"`
	User      model.User `json:"user"`
}

// Register 用户注册
func (h *AuthHandler) Register(c *fiber.Ctx) error {
	var req RegisterRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid request body", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Register request in no-database mode")

		// 模拟用户数据
		user := model.User{
			ID:       1,
			Username: req.Username,
			Email:    req.Email,
			Nickname: req.Nickname,
			Status:   1,
		}

		// 生成模拟JWT令牌
		token, expiresAt, _ := h.generateMockToken(&user)

		return c.JSON(SuccessResponse(LoginResponse{
			Token:     token,
			ExpiresAt: expiresAt,
			User:      user,
		}, "User registered successfully (mock mode)"))
	}

	// 正常数据库模式的逻辑
	// 检查用户名是否已存在
	existingUser, _ := h.services.User.GetByUsername(req.Username)
	if existingUser != nil {
		return c.Status(fiber.StatusConflict).JSON(ErrorResponse("Username already exists", fiber.StatusConflict))
	}

	// 检查邮箱是否已存在
	existingUser, _ = h.services.User.GetByEmail(req.Email)
	if existingUser != nil {
		return c.Status(fiber.StatusConflict).JSON(ErrorResponse("Email already exists", fiber.StatusConflict))
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		h.logger.Error("Failed to hash password", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to create user", fiber.StatusInternalServerError))
	}

	// 创建用户
	user := &model.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
		Nickname:     req.Nickname,
		Status:       1,
	}

	if err := h.services.User.Create(user); err != nil {
		h.logger.Error("Failed to create user", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to create user", fiber.StatusInternalServerError))
	}

	// 生成JWT令牌
	token, expiresAt, err := h.generateToken(user)
	if err != nil {
		h.logger.Error("Failed to generate token", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to generate token", fiber.StatusInternalServerError))
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	h.services.User.Update(user)

	// 隐藏密码
	user.PasswordHash = ""

	return c.JSON(SuccessResponse(LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      *user,
	}, "User registered successfully"))
}

// Login 用户登录
func (h *AuthHandler) Login(c *fiber.Ctx) error {
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse("Invalid request body", fiber.StatusBadRequest))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("Login request in no-database mode")

		// 模拟用户验证（简单的用户名密码验证）
		if req.Username == "admin" && req.Password == "123456" {
			// 模拟管理员用户
			user := model.User{
				ID:       1,
				Username: "admin",
				Email:    "<EMAIL>",
				Nickname: "管理员",
				Status:   1,
			}

			// 生成模拟JWT令牌
			token, expiresAt, _ := h.generateMockToken(&user)

			return c.JSON(SuccessResponse(LoginResponse{
				Token:     token,
				ExpiresAt: expiresAt,
				User:      user,
			}, "Login successful (mock mode)"))
		} else if req.Username == "testuser3" && req.Password == "123456" {
			// 模拟普通用户
			user := model.User{
				ID:       4,
				Username: "testuser3",
				Email:    "<EMAIL>",
				Nickname: "测试用户3",
				Status:   1,
			}

			// 生成模拟JWT令牌
			token, expiresAt, _ := h.generateMockToken(&user)

			return c.JSON(SuccessResponse(LoginResponse{
				Token:     token,
				ExpiresAt: expiresAt,
				User:      user,
			}, "Login successful (mock mode)"))
		} else {
			return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("Invalid username or password", fiber.StatusUnauthorized))
		}
	}

	// 查找用户
	user, err := h.services.User.GetByUsername(req.Username)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("Invalid username or password", fiber.StatusUnauthorized))
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("Invalid username or password", fiber.StatusUnauthorized))
	}

	// 检查用户状态
	if user.Status != 1 {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse("User account is disabled", fiber.StatusForbidden))
	}

	// 生成JWT令牌
	token, expiresAt, err := h.generateToken(user)
	if err != nil {
		h.logger.Error("Failed to generate token", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to generate token", fiber.StatusInternalServerError))
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	h.services.User.Update(user)

	// 隐藏密码
	user.PasswordHash = ""

	return c.JSON(SuccessResponse(LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      *user,
	}, "Login successful"))
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *fiber.Ctx) error {
	// 从当前令牌中获取用户信息
	userID := c.Locals("userID").(uint)
	
	user, err := h.services.User.GetByID(userID)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("User not found", fiber.StatusUnauthorized))
	}

	// 生成新的JWT令牌
	token, expiresAt, err := h.generateToken(user)
	if err != nil {
		h.logger.Error("Failed to generate token", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse("Failed to generate token", fiber.StatusInternalServerError))
	}

	return c.JSON(SuccessResponse(fiber.Map{
		"token":      token,
		"expires_at": expiresAt,
	}, "Token refreshed successfully"))
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *fiber.Ctx) error {
	// 这里可以实现令牌黑名单机制
	// 暂时简单返回成功
	return c.JSON(SuccessResponse(nil, "Logout successful"))
}

// generateToken 生成JWT令牌
func (h *AuthHandler) generateToken(user *model.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(24 * time.Hour) // 24小时过期

	claims := jwt.MapClaims{
		"user_id":  user.ID,
		"username": user.Username,
		"email":    user.Email,
		"exp":      expiresAt.Unix(),
		"iat":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("your-super-secret-jwt-key-for-development")) // 使用与.env中相同的密钥

	return tokenString, expiresAt, err
}

// generateMockToken 生成模拟JWT令牌（无数据库模式）
func (h *AuthHandler) generateMockToken(user *model.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(24 * time.Hour) // 24小时过期

	claims := jwt.MapClaims{
		"user_id":  user.ID,
		"username": user.Username,
		"email":    user.Email,
		"exp":      expiresAt.Unix(),
		"iat":      time.Now().Unix(),
		"mock":     true, // 标识为模拟令牌
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("mock-secret-key"))

	return tokenString, expiresAt, err
}

// GetCurrentUser 获取当前用户信息
func (h *AuthHandler) GetCurrentUser(c *fiber.Ctx) error {
	// 从JWT中获取用户ID
	userID := c.Locals("user_id")
	if userID == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse("Unauthorized", fiber.StatusUnauthorized))
	}

	// 无数据库模式：返回模拟数据
	if h.services == nil {
		h.logger.Info("GetCurrentUser request in no-database mode")

		// 模拟用户数据
		user := model.User{
			ID:       uint64(1),
			Username: "admin",
			Email:    "<EMAIL>",
			Nickname: "管理员",
			Status:   1,
		}

		return c.JSON(SuccessResponse(user, "User info retrieved successfully (mock mode)"))
	}

	// 获取用户信息
	user, err := h.services.User.GetByID(uint(userID.(uint64)))
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse("User not found", fiber.StatusNotFound))
	}

	// 隐藏密码
	user.PasswordHash = ""

	return c.JSON(SuccessResponse(*user, "User info retrieved successfully"))
}
