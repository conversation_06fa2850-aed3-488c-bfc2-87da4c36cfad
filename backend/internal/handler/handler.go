package handler

import (
	"web-stats-backend/internal/service"
	"web-stats-backend/pkg/logger"
)

// Handler 处理器结构体
type Handler struct {
	services *service.Service
	logger   logger.Logger
	
	// 各个模块的处理器
	Auth   *AuthHandler
	User   *UserHandler
	Site   *SiteHandler
	Track  *TrackHandler
	Stats  *StatsHandler
	Admin  *AdminHandler
}

// New 创建新的处理器实例
func New(services *service.Service, logger logger.Logger) *Handler {
	h := &Handler{
		services: services,
		logger:   logger,
	}

	// 初始化各个模块的处理器
	h.Auth = NewAuthHandler(services, logger)
	h.User = NewUserHandler(services, logger)
	h.Site = NewSiteHandler(services, logger)
	h.Track = NewTrackHandler(services, logger)
	h.Stats = NewStatsHandler(services, logger)
	h.Admin = NewAdminHandler(services, logger)

	return h
}

// NewWithoutDB 创建不依赖数据库的处理器实例（用于测试）
func NewWithoutDB(logger logger.Logger) *Handler {
	h := &Handler{
		services: nil,
		logger:   logger,
	}

	// 初始化各个模块的处理器（无数据库版本）
	h.Auth = NewAuthHandlerWithoutDB(logger)
	h.User = NewUserHandlerWithoutDB(logger)
	h.Site = NewSiteHandlerWithoutDB(logger)
	h.Track = NewTrackHandlerWithoutDB(logger)
	h.Stats = NewStatsHandlerWithoutDB(logger)
	h.Admin = NewAdminHandlerWithoutDB(logger)

	return h
}

// Response 统一响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}, message string) Response {
	return Response{
		Success: true,
		Message: message,
		Data:    data,
		Code:    200,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(error string, code int) Response {
	return Response{
		Success: false,
		Error:   error,
		Code:    code,
	}
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `json:"page" query:"page"`
	PageSize int `json:"page_size" query:"page_size"`
}

// GetPagination 获取分页参数
func (p *PaginationRequest) GetPagination() (offset int, limit int) {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 10
	}
	if p.PageSize > 100 {
		p.PageSize = 100
	}

	offset = (p.Page - 1) * p.PageSize
	limit = p.PageSize
	return
}

// CalculateTotalPages 计算总页数
func CalculateTotalPages(total int64, pageSize int) int {
	if pageSize <= 0 {
		return 0
	}
	return int((total + int64(pageSize) - 1) / int64(pageSize))
}

// SuccessResponseWithPagination 带分页的成功响应
func SuccessResponseWithPagination(data interface{}, total int64, page, pageSize int, message string) Response {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	totalPages := CalculateTotalPages(total, pageSize)

	pagination := PaginationResponse{
		Data:       data,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	return Response{
		Success: true,
		Message: message,
		Data:    pagination,
		Code:    200,
	}
}
