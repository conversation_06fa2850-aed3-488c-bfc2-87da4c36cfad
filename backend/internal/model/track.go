package model

import (
	"time"
)

// TrackRequest 数据收集请求结构
type TrackRequest struct {
	// 基础信息
	SiteID      string `json:"site_id" validate:"required"`
	PageURL     string `json:"page_url" validate:"required,url"`
	PageTitle   string `json:"page_title"`
	Referrer    string `json:"referrer"`
	UserAgent   string `json:"user_agent"`
	
	// 访客信息
	VisitorID   string `json:"visitor_id"`
	SessionID   string `json:"session_id"`
	
	// 屏幕信息
	ScreenSize       string `json:"screen_size"`
	ScreenWidth      int    `json:"screen_width"`
	ScreenHeight     int    `json:"screen_height"`
	ViewportWidth    int    `json:"viewport_width"`
	ViewportHeight   int    `json:"viewport_height"`
	ColorDepth       int    `json:"color_depth"`
	
	// 环境信息
	Language         string `json:"language"`
	Timezone         string `json:"timezone"`
	ConnectionType   string `json:"connection_type"`
	
	// UTM参数
	UTMSource        string `json:"utm_source"`
	UTMMedium        string `json:"utm_medium"`
	UTMCampaign      string `json:"utm_campaign"`
	UTMTerm          string `json:"utm_term"`
	UTMContent       string `json:"utm_content"`
	
	// 行为数据
	StayDuration     int    `json:"stay_duration"`
	ScrollDepth      int    `json:"scroll_depth"`
	ClickCount       int    `json:"click_count"`
	IsBounce         bool   `json:"is_bounce"`
	IsNewVisitor     bool   `json:"is_new_visitor"`
	
	// 页面性能
	LoadTime         int    `json:"load_time"`
	DOMReadyTime     int    `json:"dom_ready_time"`
	FirstPaintTime   int    `json:"first_paint_time"`
	
	// 时间戳
	Timestamp        int64  `json:"timestamp"`
	
	// 客户端IP（由服务器获取）
	ClientIP         string `json:"-"`
}

// TrackResponse 数据收集响应结构
type TrackResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	VisitorID string `json:"visitor_id,omitempty"`
	SessionID string `json:"session_id,omitempty"`
}

// PageViewRequest 页面浏览请求结构
type PageViewRequest struct {
	SiteID         string `json:"site_id" validate:"required"`
	VisitorID      string `json:"visitor_id" validate:"required"`
	SessionID      string `json:"session_id" validate:"required"`
	PageURL        string `json:"page_url" validate:"required,url"`
	PageTitle      string `json:"page_title"`
	RefererURL     string `json:"referer_url"`
	StayDuration   int    `json:"stay_duration"`
	ScrollDepth    int    `json:"scroll_depth"`
	ClickCount     int    `json:"click_count"`
	LoadTime       int    `json:"load_time"`
	DOMReadyTime   int    `json:"dom_ready_time"`
	FirstPaintTime int    `json:"first_paint_time"`
	Timestamp      int64  `json:"timestamp"`
}

// EventRequest 事件跟踪请求结构
type EventRequest struct {
	SiteID      string                 `json:"site_id" validate:"required"`
	VisitorID   string                 `json:"visitor_id" validate:"required"`
	SessionID   string                 `json:"session_id" validate:"required"`
	EventType   string                 `json:"event_type" validate:"required"`
	EventName   string                 `json:"event_name" validate:"required"`
	EventValue  string                 `json:"event_value"`
	Properties  map[string]interface{} `json:"properties"`
	PageURL     string                 `json:"page_url"`
	Timestamp   int64                  `json:"timestamp"`
}

// OnlineStatsResponse 在线统计响应结构
type OnlineStatsResponse struct {
	SiteID       string `json:"site_id"`
	OnlineCount  int    `json:"online_count"`
	PV5Min       int    `json:"pv_5min"`
	UV5Min       int    `json:"uv_5min"`
	LastUpdate   time.Time `json:"last_update"`
}

// VisitorInfo 访客信息结构
type VisitorInfo struct {
	VisitorHash    string    `json:"visitor_hash"`
	FirstVisit     time.Time `json:"first_visit"`
	LastVisit      time.Time `json:"last_visit"`
	TotalVisits    int       `json:"total_visits"`
	TotalPageViews int       `json:"total_page_views"`
	IsNewVisitor   bool      `json:"is_new_visitor"`
	DeviceType     string    `json:"device_type"`
	Browser        string    `json:"browser"`
	OS             string    `json:"os"`
	Country        string    `json:"country"`
	Region         string    `json:"region"`
	City           string    `json:"city"`
}

// SessionInfo 会话信息结构
type SessionInfo struct {
	SessionHash   string    `json:"session_hash"`
	StartTime     time.Time `json:"start_time"`
	LastActivity  time.Time `json:"last_activity"`
	PageViews     int       `json:"page_views"`
	Duration      int       `json:"duration"`
	LandingPage   string    `json:"landing_page"`
	ExitPage      string    `json:"exit_page"`
	IsActive      bool      `json:"is_active"`
}

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	DeviceType       string `json:"device_type"`
	Browser          string `json:"browser"`
	BrowserVersion   string `json:"browser_version"`
	OS               string `json:"os"`
	OSVersion        string `json:"os_version"`
	ScreenResolution string `json:"screen_resolution"`
	ViewportSize     string `json:"viewport_size"`
	ColorDepth       int    `json:"color_depth"`
	Language         string `json:"language"`
	Timezone         string `json:"timezone"`
	IsMobile         bool   `json:"is_mobile"`
	IsTablet         bool   `json:"is_tablet"`
	IsBot            bool   `json:"is_bot"`
}

// LocationInfo 地理位置信息结构
type LocationInfo struct {
	IP        string  `json:"ip"`
	Country   string  `json:"country"`
	Province  string  `json:"province"`
	City      string  `json:"city"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	ISP       string  `json:"isp"`
	Timezone  string  `json:"timezone"`
}

// ReferrerInfo 来源信息结构
type ReferrerInfo struct {
	RefererURL      string `json:"referer_url"`
	RefererDomain   string `json:"referer_domain"`
	RefererType     string `json:"referer_type"` // search_engine, social_media, direct, external
	SearchEngine    string `json:"search_engine,omitempty"`
	SearchKeyword   string `json:"search_keyword,omitempty"`
	UTMSource       string `json:"utm_source,omitempty"`
	UTMMedium       string `json:"utm_medium,omitempty"`
	UTMCampaign     string `json:"utm_campaign,omitempty"`
	UTMTerm         string `json:"utm_term,omitempty"`
	UTMContent      string `json:"utm_content,omitempty"`
}

// ProcessedTrackData 处理后的跟踪数据
type ProcessedTrackData struct {
	SiteID       uint64        `json:"site_id"`
	Visitor      VisitorInfo   `json:"visitor"`
	Session      SessionInfo   `json:"session"`
	Device       DeviceInfo    `json:"device"`
	Location     LocationInfo  `json:"location"`
	Referrer     ReferrerInfo  `json:"referrer"`
	PageURL      string        `json:"page_url"`
	PageTitle    string        `json:"page_title"`
	VisitTime    time.Time     `json:"visit_time"`
	StayDuration int           `json:"stay_duration"`
	ScrollDepth  int           `json:"scroll_depth"`
	ClickCount   int           `json:"click_count"`
	LoadTime     int           `json:"load_time"`
	IsBounce     bool          `json:"is_bounce"`
	IsNewVisitor bool          `json:"is_new_visitor"`
}
