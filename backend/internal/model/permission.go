package model

import (
	"time"
)

// Role 角色表
type Role struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:50;uniqueIndex;not null;comment:角色名称"`
	DisplayName string    `json:"display_name" gorm:"size:100;not null;comment:显示名称"`
	Description string    `json:"description" gorm:"size:500;comment:角色描述"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;comment:是否系统角色"`
	Status      int8      `json:"status" gorm:"default:1;comment:状态 1-启用 0-禁用"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联
	Users       []User       `json:"users,omitempty" gorm:"many2many:user_roles;"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

// Permission 权限表
type Permission struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:权限名称"`
	DisplayName string    `json:"display_name" gorm:"size:100;not null;comment:显示名称"`
	Description string    `json:"description" gorm:"size:500;comment:权限描述"`
	Resource    string    `json:"resource" gorm:"size:50;not null;comment:资源类型"`
	Action      string    `json:"action" gorm:"size:50;not null;comment:操作类型"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;comment:是否系统权限"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联
	Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
}

// UserRole 用户角色关联表
type UserRole struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint64    `json:"user_id" gorm:"not null;index"`
	RoleID    uint      `json:"role_id" gorm:"not null;index"`
	CreatedAt time.Time `json:"created_at"`

	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

// RolePermission 角色权限关联表
type RolePermission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	RoleID       uint      `json:"role_id" gorm:"not null;index"`
	PermissionID uint      `json:"permission_id" gorm:"not null;index"`
	CreatedAt    time.Time `json:"created_at"`

	// 关联
	Role       Role       `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	Permission Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
}

// UserPermission 用户权限缓存表（用于快速权限检查）
type UserPermission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       uint64    `json:"user_id" gorm:"not null;index"`
	PermissionID uint      `json:"permission_id" gorm:"not null;index"`
	Resource     string    `json:"resource" gorm:"size:50;not null;index;comment:资源类型"`
	Action       string    `json:"action" gorm:"size:50;not null;index;comment:操作类型"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联
	User       User       `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Permission Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
}

// OperationLog 操作日志表
type OperationLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint64    `json:"user_id" gorm:"not null;index;comment:操作用户ID"`
	Username   string    `json:"username" gorm:"size:50;not null;comment:用户名"`
	Action     string    `json:"action" gorm:"size:100;not null;comment:操作类型"`
	Resource   string    `json:"resource" gorm:"size:100;comment:操作资源"`
	ResourceID uint64    `json:"resource_id" gorm:"comment:资源ID"`
	Method     string    `json:"method" gorm:"size:10;comment:HTTP方法"`
	Path       string    `json:"path" gorm:"size:255;comment:请求路径"`
	IP         string    `json:"ip" gorm:"size:45;comment:IP地址"`
	UserAgent  string    `json:"user_agent" gorm:"size:500;comment:用户代理"`
	Request    string    `json:"request" gorm:"type:text;comment:请求数据"`
	Response   string    `json:"response" gorm:"type:text;comment:响应数据"`
	Status     int       `json:"status" gorm:"comment:响应状态码"`
	Duration   int64     `json:"duration" gorm:"comment:执行时长(毫秒)"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// 权限常量定义
const (
	// 资源类型
	ResourceSite      = "site"
	ResourceStats     = "stats"
	ResourceUser      = "user"
	ResourceRole      = "role"
	ResourcePermission = "permission"
	ResourceSystem    = "system"

	// 操作类型
	ActionView   = "view"
	ActionCreate = "create"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionManage = "manage"
	ActionExport = "export"
	ActionImport = "import"

	// 系统角色
	RoleAdmin     = "admin"
	RoleUser      = "user"
	RoleViewer    = "viewer"
	RoleModerator = "moderator"
)

// 权限检查请求
type PermissionCheckRequest struct {
	UserID   uint64 `json:"user_id"`
	Resource string `json:"resource"`
	Action   string `json:"action"`
	SiteID   uint64 `json:"site_id,omitempty"` // 可选的网站ID，用于数据隔离
}

// 角色管理请求
type RoleCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	DisplayName string `json:"display_name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"omitempty,max=500"`
	Permissions []uint `json:"permissions" validate:"omitempty"`
}

type RoleUpdateRequest struct {
	DisplayName string `json:"display_name" validate:"omitempty,min=2,max=100"`
	Description string `json:"description" validate:"omitempty,max=500"`
	Permissions []uint `json:"permissions" validate:"omitempty"`
	Status      int8   `json:"status" validate:"omitempty,oneof=0 1"`
}

type RoleListRequest struct {
	Page     int    `json:"page" query:"page" validate:"omitempty,min=1"`
	PageSize int    `json:"page_size" query:"page_size" validate:"omitempty,min=1,max=100"`
	Search   string `json:"search" query:"search" validate:"omitempty,max=100"`
	Status   int8   `json:"status" query:"status" validate:"omitempty,oneof=0 1"`
}

// 权限管理请求
type PermissionCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	DisplayName string `json:"display_name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"omitempty,max=500"`
	Resource    string `json:"resource" validate:"required,max=50"`
	Action      string `json:"action" validate:"required,max=50"`
}

type PermissionUpdateRequest struct {
	DisplayName string `json:"display_name" validate:"omitempty,min=2,max=100"`
	Description string `json:"description" validate:"omitempty,max=500"`
	Resource    string `json:"resource" validate:"omitempty,max=50"`
	Action      string `json:"action" validate:"omitempty,max=50"`
}

// 用户角色分配请求
type UserRoleAssignRequest struct {
	UserID  uint64 `json:"user_id" validate:"required"`
	RoleIDs []uint `json:"role_ids" validate:"required"`
}

// 操作日志查询请求
type OperationLogRequest struct {
	Page      int    `json:"page" query:"page" validate:"omitempty,min=1"`
	PageSize  int    `json:"page_size" query:"page_size" validate:"omitempty,min=1,max=100"`
	UserID    uint64 `json:"user_id" query:"user_id" validate:"omitempty"`
	Action    string `json:"action" query:"action" validate:"omitempty,max=100"`
	Resource  string `json:"resource" query:"resource" validate:"omitempty,max=100"`
	StartDate string `json:"start_date" query:"start_date" validate:"omitempty"`
	EndDate   string `json:"end_date" query:"end_date" validate:"omitempty"`
}

// 响应结构
type RoleResponse struct {
	ID          uint                 `json:"id"`
	Name        string               `json:"name"`
	DisplayName string               `json:"display_name"`
	Description string               `json:"description"`
	IsSystem    bool                 `json:"is_system"`
	Status      int8                 `json:"status"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
	Permissions []PermissionResponse `json:"permissions,omitempty"`
	UserCount   int64                `json:"user_count,omitempty"`
}

type PermissionResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Description string    `json:"description"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	IsSystem    bool      `json:"is_system"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UserPermissionResponse struct {
	UserID      uint64               `json:"user_id"`
	Username    string               `json:"username"`
	Roles       []RoleResponse       `json:"roles"`
	Permissions []PermissionResponse `json:"permissions"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

func (Permission) TableName() string {
	return "permissions"
}

func (UserRole) TableName() string {
	return "user_roles"
}

func (RolePermission) TableName() string {
	return "role_permissions"
}

func (UserPermission) TableName() string {
	return "user_permissions"
}

func (OperationLog) TableName() string {
	return "operation_logs"
}
