package model

import (
	"time"
)

// SearchEngine 搜索引擎字典表
type SearchEngine struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:50;uniqueIndex;not null"`
	Domain    string    `json:"domain" gorm:"size:100"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (SearchEngine) TableName() string {
	return "search_engines"
}

// Keyword 关键词字典表
type Keyword struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Keyword     string    `json:"keyword" gorm:"size:200;uniqueIndex;not null"`
	KeywordHash string    `json:"keyword_hash" gorm:"size:64;uniqueIndex;not null"`
	FirstSeen   time.Time `json:"first_seen" gorm:"default:CURRENT_TIMESTAMP"`
	LastSeen    time.Time `json:"last_seen" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	TotalCount  int       `json:"total_count" gorm:"default:1"`
}

// TableName 指定表名
func (Keyword) TableName() string {
	return "keywords"
}

// Browser 浏览器字典表
type Browser struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:50;not null"`
	Version   string    `json:"version" gorm:"size:20"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (Browser) TableName() string {
	return "browsers"
}

// OperatingSystem 操作系统字典表
type OperatingSystem struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:50;not null"`
	Version   string    `json:"version" gorm:"size:20"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (OperatingSystem) TableName() string {
	return "operating_systems"
}

// Region 地区字典表
type Region struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Country   string    `json:"country" gorm:"size:50;not null"`
	Province  string    `json:"province" gorm:"size:50"`
	City      string    `json:"city" gorm:"size:50"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (Region) TableName() string {
	return "regions"
}

// PageURL 页面URL字典表
type PageURL struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	URL       string    `json:"url" gorm:"size:500;uniqueIndex;not null"`
	URLHash   string    `json:"url_hash" gorm:"size:64;uniqueIndex;not null"`
	Domain    string    `json:"domain" gorm:"size:100;index"`
	Path      string    `json:"path" gorm:"size:400"`
	Title     string    `json:"title" gorm:"size:200"`
	FirstSeen time.Time `json:"first_seen" gorm:"default:CURRENT_TIMESTAMP"`
	LastSeen  time.Time `json:"last_seen" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (PageURL) TableName() string {
	return "page_urls"
}

// RefererDomain 来源域名字典表
type RefererDomain struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	Domain     string    `json:"domain" gorm:"size:100;uniqueIndex;not null"`
	DomainType string    `json:"domain_type" gorm:"type:enum('search_engine','social_media','direct','external');default:external"`
	CreatedAt  time.Time `json:"created_at"`
}

// TableName 指定表名
func (RefererDomain) TableName() string {
	return "referer_domains"
}

// Session 会话字典表
type Session struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	SessionHash string    `json:"session_hash" gorm:"size:64;uniqueIndex;not null"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (Session) TableName() string {
	return "sessions"
}

// IPAddress IP地址字典表
type IPAddress struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	IPInt     uint32    `json:"ip_int" gorm:"uniqueIndex;comment:IPv4地址的整数表示"`
	IPString  string    `json:"ip_string" gorm:"size:45;index"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (IPAddress) TableName() string {
	return "ip_addresses"
}

// UserAgent User Agent字典表
type UserAgent struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	UAHash     string `json:"ua_hash" gorm:"size:64;uniqueIndex;not null"`
	UserAgent  string `json:"user_agent" gorm:"type:text"`
	BrowserID  uint   `json:"browser_id"`
	OSID       uint   `json:"os_id"`
	DeviceType string `json:"device_type" gorm:"type:enum('desktop','mobile','tablet')"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联
	Browser Browser         `json:"browser,omitempty" gorm:"foreignKey:BrowserID"`
	OS      OperatingSystem `json:"os,omitempty" gorm:"foreignKey:OSID"`
}

// TableName 指定表名
func (UserAgent) TableName() string {
	return "user_agents"
}
