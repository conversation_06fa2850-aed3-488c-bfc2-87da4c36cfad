package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型（适配现有数据库）
type User struct {
	ID          uint64     `json:"id" gorm:"primaryKey;column:id"`
	Username    string     `json:"username" gorm:"uniqueIndex;size:50;not null;column:username"`
	Email       string     `json:"email" gorm:"uniqueIndex;size:100;not null;column:email"`
	PasswordHash string    `json:"-" gorm:"size:255;not null;column:password_hash"`
	Nickname    string     `json:"nickname" gorm:"size:50;column:nickname"`
	Avatar      string     `json:"avatar" gorm:"size:255;column:avatar"`
	Phone       string     `json:"phone" gorm:"size:20;column:phone"`
	Status      int8       `json:"status" gorm:"default:1;column:status"`
	LastLoginAt *time.Time `json:"last_login_at" gorm:"column:last_login_at"`
	CreatedAt   time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"column:updated_at"`

	// 关联
	Sites []Site `json:"sites,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Site 网站模型（适配现有数据库）
type Site struct {
	ID           uint64    `json:"id" gorm:"primaryKey;column:id"`
	UserID       uint64    `json:"user_id" gorm:"not null;index;column:user_id"`
	SiteName     string    `json:"site_name" gorm:"size:100;not null;column:site_name"`
	Domain       string    `json:"domain" gorm:"size:100;not null;index;column:domain"`
	SiteURL      string    `json:"site_url" gorm:"size:255;not null;column:site_url"`
	SiteType     string    `json:"site_type" gorm:"size:50;default:other;column:site_type"`
	Region       string    `json:"region" gorm:"size:50;column:region"`
	Description  string    `json:"description" gorm:"type:text;column:description"`
	TrackingCode string    `json:"tracking_code" gorm:"size:32;uniqueIndex;not null;column:tracking_code"`
	ViewPassword string    `json:"view_password" gorm:"size:50;column:view_password"`
	IsPublic     int8      `json:"is_public" gorm:"default:0;column:is_public"`
	Status       int8      `json:"status" gorm:"default:1;column:status"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at"`

	// 关联
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Visitors []Visitor `json:"visitors,omitempty" gorm:"foreignKey:SiteID"`
	Visits   []Visit   `json:"visits,omitempty" gorm:"foreignKey:SiteID"`
}

// SiteCreateRequest 创建网站请求
type SiteCreateRequest struct {
	SiteName     string `json:"site_name" validate:"required,min=1,max=100"`
	Domain       string `json:"domain" validate:"required,min=1,max=100"`
	SiteURL      string `json:"site_url" validate:"required,url,max=255"`
	SiteType     string `json:"site_type" validate:"omitempty,oneof=blog ecommerce news corporate entertainment education other"`
	Region       string `json:"region" validate:"omitempty,max=50"`
	Description  string `json:"description" validate:"omitempty,max=1000"`
	ViewPassword string `json:"view_password" validate:"omitempty,max=50"`
	IsPublic     int8   `json:"is_public" validate:"omitempty,oneof=0 1"`
}

// SiteUpdateRequest 更新网站请求
type SiteUpdateRequest struct {
	SiteName     string `json:"site_name" validate:"omitempty,min=1,max=100"`
	Domain       string `json:"domain" validate:"omitempty,min=1,max=100"`
	SiteURL      string `json:"site_url" validate:"omitempty,url,max=255"`
	SiteType     string `json:"site_type" validate:"omitempty,oneof=blog ecommerce news corporate entertainment education other"`
	Region       string `json:"region" validate:"omitempty,max=50"`
	Description  string `json:"description" validate:"omitempty,max=1000"`
	ViewPassword string `json:"view_password" validate:"omitempty,max=50"`
	IsPublic     int8   `json:"is_public" validate:"omitempty,oneof=0 1"`
	Status       int8   `json:"status" validate:"omitempty,oneof=0 1"`
}

// SiteResponse 网站响应
type SiteResponse struct {
	ID           uint64    `json:"id"`
	SiteName     string    `json:"site_name"`
	Domain       string    `json:"domain"`
	SiteURL      string    `json:"site_url"`
	SiteType     string    `json:"site_type"`
	Region       string    `json:"region"`
	Description  string    `json:"description"`
	TrackingCode string    `json:"tracking_code"`
	IsPublic     int8      `json:"is_public"`
	Status       int8      `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 统计信息
	TotalVisits    int64 `json:"total_visits,omitempty"`
	TotalPageViews int64 `json:"total_page_views,omitempty"`
	LastVisit      *time.Time `json:"last_visit,omitempty"`
}

// SiteListRequest 网站列表请求
type SiteListRequest struct {
	Page     int    `json:"page" query:"page" validate:"omitempty,min=1"`
	PageSize int    `json:"page_size" query:"page_size" validate:"omitempty,min=1,max=100"`
	Search   string `json:"search" query:"search" validate:"omitempty,max=100"`
	SiteType string `json:"site_type" query:"site_type" validate:"omitempty,oneof=blog ecommerce news corporate entertainment education other"`
	Status   int8   `json:"status" query:"status" validate:"omitempty,oneof=0 1"`
}

// TableName 指定表名
func (Site) TableName() string {
	return "sites"
}

// Visitor 访客模型（适配现有数据库）
type Visitor struct {
	ID             uint64    `json:"id" gorm:"primaryKey;column:id"`
	SiteID         uint64    `json:"site_id" gorm:"not null;index;column:site_id"`
	VisitorHash    string    `json:"visitor_hash" gorm:"size:64;not null;column:visitor_hash"`
	FirstVisit     time.Time `json:"first_visit" gorm:"not null;column:first_visit"`
	LastVisit      time.Time `json:"last_visit" gorm:"not null;index;column:last_visit"`
	TotalVisits    int       `json:"total_visits" gorm:"default:1;column:total_visits"`
	TotalPageViews int       `json:"total_page_views" gorm:"default:1;column:total_page_views"`
	TotalDuration  int       `json:"total_duration" gorm:"default:0;column:total_duration"`
	CreatedAt      time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"column:updated_at"`

	// 关联
	Site   Site    `json:"site,omitempty" gorm:"foreignKey:SiteID"`
	Visits []Visit `json:"visits,omitempty" gorm:"foreignKey:VisitorID"`
}

// TableName 指定表名
func (Visitor) TableName() string {
	return "visitors"
}

// BeforeCreate 创建前的钩子
func (v *Visitor) BeforeCreate(tx *gorm.DB) error {
	// 确保唯一性
	tx.Where("site_id = ? AND visitor_hash = ?", v.SiteID, v.VisitorHash)
	return nil
}
