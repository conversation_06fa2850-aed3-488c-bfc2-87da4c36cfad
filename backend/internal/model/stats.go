package model

import (
	"time"
)

// OverviewStats 概况统计数据
type OverviewStats struct {
	SiteID             uint64    `json:"site_id"`
	StartDate          time.Time `json:"start_date"`
	EndDate            time.Time `json:"end_date"`
	PV                 int64     `json:"pv"`
	UV                 int64     `json:"uv"`
	IPCount            int64     `json:"ip_count"`
	SessionCount       int64     `json:"session_count"`
	NewVisitors        int64     `json:"new_visitors"`
	ReturningVisitors  int64     `json:"returning_visitors"`
	BounceCount        int64     `json:"bounce_count"`
	BounceRate         float64   `json:"bounce_rate"`
	AvgDuration        float64   `json:"avg_duration"`
	AvgPageViews       float64   `json:"avg_page_views"`
	
	// 设备分布
	DesktopPV          int64     `json:"desktop_pv"`
	MobilePV           int64     `json:"mobile_pv"`
	TabletPV           int64     `json:"tablet_pv"`
	
	// 来源分布
	SearchPV           int64     `json:"search_pv"`
	DirectPV           int64     `json:"direct_pv"`
	ExternalPV         int64     `json:"external_pv"`
	
	// 对比数据（与上一周期对比）
	PVGrowth           float64   `json:"pv_growth"`
	UVGrowth           float64   `json:"uv_growth"`
	BounceRateChange   float64   `json:"bounce_rate_change"`
	AvgDurationChange  float64   `json:"avg_duration_change"`
}

// TrendData 趋势数据
type TrendData struct {
	Date         time.Time `json:"date"`
	Hour         int       `json:"hour,omitempty"`
	PV           int64     `json:"pv"`
	UV           int64     `json:"uv"`
	IPCount      int64     `json:"ip_count"`
	SessionCount int64     `json:"session_count"`
	NewVisitors  int64     `json:"new_visitors"`
	BounceCount  int64     `json:"bounce_count"`
	BounceRate   float64   `json:"bounce_rate"`
	AvgDuration  float64   `json:"avg_duration"`
}

// RealtimeStats 实时统计数据
type RealtimeStats struct {
	SiteID         uint64                `json:"site_id"`
	OnlineCount    int                   `json:"online_count"`
	PV5Min         int                   `json:"pv_5min"`
	UV5Min         int                   `json:"uv_5min"`
	LastUpdate     time.Time             `json:"last_update"`
	RecentVisitors []*RecentVisitorInfo  `json:"recent_visitors"`
	TopPages       []*RealtimePageInfo   `json:"top_pages"`
}

// RecentVisitorInfo 最近访客信息
type RecentVisitorInfo struct {
	VisitorID    string    `json:"visitor_id"`
	PageURL      string    `json:"page_url"`
	PageTitle    string    `json:"page_title"`
	Referrer     string    `json:"referrer"`
	Location     string    `json:"location"`
	DeviceType   string    `json:"device_type"`
	Browser      string    `json:"browser"`
	VisitTime    time.Time `json:"visit_time"`
}

// RealtimePageInfo 实时页面信息
type RealtimePageInfo struct {
	PageURL      string `json:"page_url"`
	PageTitle    string `json:"page_title"`
	PV5Min       int    `json:"pv_5min"`
	UV5Min       int    `json:"uv_5min"`
}

// SourceStats 来源统计数据
type SourceStats struct {
	SourceType     string  `json:"source_type"`     // search_engine, social_media, direct, external
	SourceName     string  `json:"source_name"`     // 具体来源名称
	PV             int64   `json:"pv"`
	UV             int64   `json:"uv"`
	SessionCount   int64   `json:"session_count"`
	BounceRate     float64 `json:"bounce_rate"`
	AvgDuration    float64 `json:"avg_duration"`
	AvgPageViews   float64 `json:"avg_page_views"`
	Percentage     float64 `json:"percentage"`      // 占总流量的百分比
}

// PageStats 页面统计数据
type PageStats struct {
	PageURL        string  `json:"page_url"`
	PageTitle      string  `json:"page_title"`
	PV             int64   `json:"pv"`
	UV             int64   `json:"uv"`
	EntryCount     int64   `json:"entry_count"`     // 入口次数
	ExitCount      int64   `json:"exit_count"`      // 退出次数
	BounceCount    int64   `json:"bounce_count"`
	BounceRate     float64 `json:"bounce_rate"`
	ExitRate       float64 `json:"exit_rate"`
	AvgDuration    float64 `json:"avg_duration"`
	AvgScrollDepth float64 `json:"avg_scroll_depth"`
	AvgLoadTime    float64 `json:"avg_load_time"`
}

// VisitorStats 访客统计数据
type VisitorStats struct {
	// 地理分布
	RegionStats    []*RegionStatsItem    `json:"region_stats"`
	
	// 设备分布
	DeviceStats    []*DeviceStatsItem    `json:"device_stats"`
	
	// 浏览器分布
	BrowserStats   []*BrowserStatsItem   `json:"browser_stats"`
	
	// 操作系统分布
	OSStats        []*OSStatsItem        `json:"os_stats"`
	
	// 新老访客分布
	NewVsReturning *NewVsReturningStats  `json:"new_vs_returning"`
	
	// 访客忠诚度
	LoyaltyStats   []*LoyaltyStatsItem   `json:"loyalty_stats"`
}

// RegionStatsItem 地区统计项
type RegionStatsItem struct {
	Country      string  `json:"country"`
	Province     string  `json:"province"`
	City         string  `json:"city"`
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	SessionCount int64   `json:"session_count"`
	Percentage   float64 `json:"percentage"`
}

// DeviceStatsItem 设备统计项
type DeviceStatsItem struct {
	DeviceType   string  `json:"device_type"`
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	SessionCount int64   `json:"session_count"`
	BounceRate   float64 `json:"bounce_rate"`
	AvgDuration  float64 `json:"avg_duration"`
	Percentage   float64 `json:"percentage"`
}

// BrowserStatsItem 浏览器统计项
type BrowserStatsItem struct {
	BrowserName  string  `json:"browser_name"`
	Version      string  `json:"version"`
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	Percentage   float64 `json:"percentage"`
}

// OSStatsItem 操作系统统计项
type OSStatsItem struct {
	OSName       string  `json:"os_name"`
	Version      string  `json:"version"`
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	Percentage   float64 `json:"percentage"`
}

// NewVsReturningStats 新老访客统计
type NewVsReturningStats struct {
	NewVisitors      int64   `json:"new_visitors"`
	ReturningVisitors int64   `json:"returning_visitors"`
	NewVisitorRate   float64 `json:"new_visitor_rate"`
	ReturningRate    float64 `json:"returning_rate"`
}

// LoyaltyStatsItem 忠诚度统计项
type LoyaltyStatsItem struct {
	VisitCount   int     `json:"visit_count"`      // 访问次数范围
	VisitorCount int64   `json:"visitor_count"`    // 访客数量
	Percentage   float64 `json:"percentage"`       // 占比
	Description  string  `json:"description"`      // 描述（如：首次访问、2-5次访问等）
}

// SearchKeywordStats 搜索关键词统计
type SearchKeywordStats struct {
	Keyword      string  `json:"keyword"`
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	SessionCount int64   `json:"session_count"`
	Percentage   float64 `json:"percentage"`
	Trend        string  `json:"trend"`            // up, down, stable
}

// StatsRequest 统计查询请求
type StatsRequest struct {
	SiteID      uint64 `json:"site_id" query:"site_id" validate:"required"`
	StartDate   string `json:"start_date" query:"start_date" validate:"required"`
	EndDate     string `json:"end_date" query:"end_date" validate:"required"`
	Granularity string `json:"granularity" query:"granularity"` // day, hour
	Page        int    `json:"page" query:"page"`
	PageSize    int    `json:"page_size" query:"page_size"`
}

// StatsResponse 统计查询响应
type StatsResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Total   int64       `json:"total,omitempty"`
	Page    int         `json:"page,omitempty"`
	PageSize int        `json:"page_size,omitempty"`
}
