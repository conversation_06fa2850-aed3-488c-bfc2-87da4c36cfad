package model

import (
	"time"
)

// Visit 访问记录表
type Visit struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	SiteID           uint      `json:"site_id" gorm:"not null;index"`
	VisitorID        uint      `json:"visitor_id" gorm:"not null;index"`
	SessionID        uint      `json:"session_id" gorm:"not null;index"`
	IPID             uint      `json:"ip_id" gorm:"not null"`
	UserAgentID      uint      `json:"user_agent_id"`
	RefererDomainID  uint      `json:"referer_domain_id"`
	RefererURLID     uint      `json:"referer_url_id"`
	LandingPageID    uint      `json:"landing_page_id" gorm:"not null"`
	ExitPageID       uint      `json:"exit_page_id"`
	
	// 时间字段
	VisitTime    time.Time `json:"visit_time" gorm:"not null;index"`
	VisitDate    time.Time `json:"visit_date" gorm:"type:date;not null;index"`
	VisitHour    int8      `json:"visit_hour" gorm:"not null;comment:访问小时（0-23）"`
	VisitYear    int16     `json:"visit_year" gorm:"not null"`
	VisitMonth   int8      `json:"visit_month" gorm:"not null;comment:访问月份（1-12）"`
	VisitWeek    int8      `json:"visit_week" gorm:"not null;comment:访问周数（1-53）"`
	VisitWeekday int8      `json:"visit_weekday" gorm:"not null;comment:星期几（1-7）"`
	
	VisitDuration     int    `json:"visit_duration" gorm:"default:0;comment:访问时长(秒)"`
	PageViews         int    `json:"page_views" gorm:"default:1"`
	IsNewVisitor      int8   `json:"is_new_visitor" gorm:"default:0"`
	RegionID          uint   `json:"region_id"`
	DeviceType        string `json:"device_type" gorm:"type:enum('desktop','mobile','tablet')"`
	BrowserID         uint   `json:"browser_id"`
	OSID              uint   `json:"os_id"`
	ScreenResolution  string `json:"screen_resolution" gorm:"size:20"`
	Language          string `json:"language" gorm:"size:10"`
	SearchEngineID    uint   `json:"search_engine_id"`
	SearchKeywordID   uint   `json:"search_keyword_id"`
	UTMSource         string `json:"utm_source" gorm:"size:100"`
	UTMMedium         string `json:"utm_medium" gorm:"size:100"`
	UTMCampaign       string `json:"utm_campaign" gorm:"size:100"`
	CreatedAt         time.Time `json:"created_at"`

	// 关联
	Site           Site            `json:"site,omitempty" gorm:"foreignKey:SiteID"`
	Visitor        Visitor         `json:"visitor,omitempty" gorm:"foreignKey:VisitorID"`
	Session        Session         `json:"session,omitempty" gorm:"foreignKey:SessionID"`
	IP             IPAddress       `json:"ip,omitempty" gorm:"foreignKey:IPID"`
	UserAgent      UserAgent       `json:"user_agent,omitempty" gorm:"foreignKey:UserAgentID"`
	RefererDomain  RefererDomain   `json:"referer_domain,omitempty" gorm:"foreignKey:RefererDomainID"`
	RefererURL     PageURL         `json:"referer_url,omitempty" gorm:"foreignKey:RefererURLID"`
	LandingPage    PageURL         `json:"landing_page,omitempty" gorm:"foreignKey:LandingPageID"`
	ExitPage       PageURL         `json:"exit_page,omitempty" gorm:"foreignKey:ExitPageID"`
	Region         Region          `json:"region,omitempty" gorm:"foreignKey:RegionID"`
	Browser        Browser         `json:"browser,omitempty" gorm:"foreignKey:BrowserID"`
	OS             OperatingSystem `json:"os,omitempty" gorm:"foreignKey:OSID"`
	SearchEngine   SearchEngine    `json:"search_engine,omitempty" gorm:"foreignKey:SearchEngineID"`
	SearchKeyword  Keyword         `json:"search_keyword,omitempty" gorm:"foreignKey:SearchKeywordID"`
}

// TableName 指定表名
func (Visit) TableName() string {
	return "visits"
}

// PageView 页面浏览记录表
type PageView struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	SiteID        uint      `json:"site_id" gorm:"not null;index"`
	VisitorID     uint      `json:"visitor_id" gorm:"not null;index"`
	SessionID     uint      `json:"session_id" gorm:"not null;index"`
	PageURLID     uint      `json:"page_url_id" gorm:"not null"`
	RefererURLID  uint      `json:"referer_url_id"`
	ViewTime      time.Time `json:"view_time" gorm:"not null;index"`
	ViewDate      time.Time `json:"view_date" gorm:"type:date;not null;index"`
	ViewHour      int8      `json:"view_hour" gorm:"not null"`
	StayDuration  int       `json:"stay_duration" gorm:"default:0;comment:停留时长(秒)"`
	ScrollDepth   int       `json:"scroll_depth" gorm:"default:0;comment:滚动深度百分比"`
	IsBounce      int8      `json:"is_bounce" gorm:"default:0;comment:是否跳出"`
	CreatedAt     time.Time `json:"created_at"`

	// 关联
	Site       Site    `json:"site,omitempty" gorm:"foreignKey:SiteID"`
	Visitor    Visitor `json:"visitor,omitempty" gorm:"foreignKey:VisitorID"`
	Session    Session `json:"session,omitempty" gorm:"foreignKey:SessionID"`
	PageURL    PageURL `json:"page_url,omitempty" gorm:"foreignKey:PageURLID"`
	RefererURL PageURL `json:"referer_url,omitempty" gorm:"foreignKey:RefererURLID"`
}

// TableName 指定表名
func (PageView) TableName() string {
	return "page_views"
}

// StatsDaily 日级预聚合表
type StatsDaily struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	SiteID            uint      `json:"site_id" gorm:"not null;index"`
	StatDate          time.Time `json:"stat_date" gorm:"type:date;not null"`
	PV                int       `json:"pv" gorm:"default:0"`
	UV                int       `json:"uv" gorm:"default:0"`
	IPCount           int       `json:"ip_count" gorm:"default:0"`
	SessionCount      int       `json:"session_count" gorm:"default:0"`
	NewVisitors       int       `json:"new_visitors" gorm:"default:0"`
	ReturningVisitors int       `json:"returning_visitors" gorm:"default:0"`
	BounceCount       int       `json:"bounce_count" gorm:"default:0"`
	TotalDuration     int64     `json:"total_duration" gorm:"default:0"`
	AvgDuration       float64   `json:"avg_duration" gorm:"type:decimal(8,2);default:0"`
	AvgPageViews      float64   `json:"avg_page_views" gorm:"type:decimal(5,2);default:0"`
	DesktopPV         int       `json:"desktop_pv" gorm:"default:0"`
	MobilePV          int       `json:"mobile_pv" gorm:"default:0"`
	TabletPV          int       `json:"tablet_pv" gorm:"default:0"`
	SearchPV          int       `json:"search_pv" gorm:"default:0"`
	DirectPV          int       `json:"direct_pv" gorm:"default:0"`
	ExternalPV        int       `json:"external_pv" gorm:"default:0"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// 关联
	Site Site `json:"site,omitempty" gorm:"foreignKey:SiteID"`
}

// TableName 指定表名
func (StatsDaily) TableName() string {
	return "stats_daily"
}

// RealtimeStatsTable 实时统计表（数据库模型）
type RealtimeStatsTable struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	SiteID      uint      `json:"site_id" gorm:"not null;index"`
	TimeSlot    time.Time `json:"time_slot" gorm:"not null;comment:时间槽（每5分钟）"`
	PV5Min      int       `json:"pv_5min" gorm:"default:0"`
	UV5Min      int       `json:"uv_5min" gorm:"default:0"`
	OnlineCount int       `json:"online_count" gorm:"default:0"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联
	Site Site `json:"site,omitempty" gorm:"foreignKey:SiteID"`
}

// TableName 指定表名
func (RealtimeStatsTable) TableName() string {
	return "realtime_stats"
}
