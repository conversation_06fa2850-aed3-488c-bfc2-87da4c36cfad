package main

import (
	"log"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	fiberLogger "github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/joho/godotenv"

	"web-stats-backend/internal/config"
	"web-stats-backend/internal/database"
	"web-stats-backend/internal/handler"
	"web-stats-backend/internal/middleware"
	"web-stats-backend/internal/repository"
	"web-stats-backend/internal/service"
	appLogger "web-stats-backend/pkg/logger"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.New()

	// 初始化日志
	logger := appLogger.New(cfg.LogLevel)

	// 初始化数据库
	logger.Info("Connecting to database...")
	db, err := database.New(cfg.Database)
	if err != nil {
		logger.Fatal("Failed to connect to database", err)
	}
	logger.Info("Database connected successfully")

	// 自动迁移数据库表（暂时跳过，使用现有数据库结构）
	// if err := database.AutoMigrate(db); err != nil {
	// 	logger.Fatal("Failed to migrate database", err)
	// }

	// 初始化仓储层
	repos := repository.New(db)

	// 初始化服务层
	services := service.New(repos, logger)

	// 初始化处理器
	handlers := handler.New(services, logger)

	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		ErrorHandler: middleware.ErrorHandler,
		BodyLimit:    4 * 1024 * 1024, // 4MB
	})

	// 中间件
	app.Use(recover.New())
	app.Use(fiberLogger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins:     cfg.CORS.AllowOrigins,
		AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders:     "Origin,Content-Type,Accept,Authorization",
		AllowCredentials: true,
	}))

	// 自定义中间件
	app.Use(middleware.RequestID())
	app.Use(middleware.RateLimit())

	// 静态文件服务（JS SDK）
	app.Static("/js", "../js-sdk/dist")

	// 路由
	setupRoutes(app, handlers)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "9002"
	}

	logger.Info("Server starting on port " + port)
	if err := app.Listen(":" + port); err != nil {
		logger.Fatal("Failed to start server", err)
	}
}

func setupRoutes(app *fiber.App, h *handler.Handler) {
	// API版本1
	v1 := app.Group("/api/v1")

	// 健康检查
	v1.Get("/health", h.Health)

	// 数据收集接口
	track := v1.Group("/track")
	track.Post("/", h.Track.Collect)
	track.Get("/online/:siteId", h.Track.GetOnlineCount)

	// 认证接口
	auth := v1.Group("/auth")
	auth.Post("/register", h.Auth.Register)
	auth.Post("/login", h.Auth.Login)
	auth.Post("/refresh", h.Auth.RefreshToken)
	auth.Post("/logout", middleware.AuthRequired(), h.Auth.Logout)
	auth.Get("/me", middleware.AuthRequired(), h.Auth.GetCurrentUser)

	// 用户接口
	user := v1.Group("/user", middleware.AuthRequired())
	user.Get("/profile", h.User.GetProfile)
	user.Put("/profile", h.User.UpdateProfile)
	user.Put("/password", h.User.ChangePassword)

	// 用户管理接口
	users := v1.Group("/users", middleware.AuthRequired())
	users.Get("/:id/permissions", h.User.GetUserPermissions)

	// 网站管理接口
	sites := v1.Group("/sites", middleware.AuthRequired())
	sites.Get("/", h.Site.List)
	sites.Post("/", h.Site.Create)
	sites.Get("/:id", h.Site.GetByID)
	sites.Put("/:id", h.Site.Update)
	sites.Delete("/:id", h.Site.Delete)
	sites.Get("/:id/code", h.Site.GetTrackingCode)

	// 统计数据接口
	stats := v1.Group("/stats", middleware.AuthRequired())
	stats.Get("/:siteId/overview", h.Stats.Overview)
	stats.Get("/:siteId/trend", h.Stats.Trend)
	stats.Get("/:siteId/source", h.Stats.Source)
	stats.Get("/:siteId/pages", h.Stats.Pages)
	stats.Get("/:siteId/visitors", h.Stats.Visitors)
	stats.Get("/:siteId/realtime", h.Stats.Realtime)
	stats.Get("/:siteId/regions", h.Stats.Regions)
	stats.Get("/:siteId/devices", h.Stats.Devices)

	// 管理员接口
	admin := v1.Group("/admin", middleware.AuthRequired(), middleware.AdminRequired())
	admin.Get("/users", h.Admin.ListUsers)
	admin.Get("/sites", h.Admin.ListSites)
	admin.Get("/stats", h.Admin.SystemStats)
}
