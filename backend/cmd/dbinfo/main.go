package main

import (
	"fmt"
	"log"

	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"web-stats-backend/internal/config"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(".env"); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.New()

	// 连接数据库
	dsn := cfg.Database.GetDSN()
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("=== 数据库连接成功 ===")
	fmt.Printf("数据库: %s\n", cfg.Database.DBName)
	fmt.Printf("主机: %s:%d\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Println()

	// 查询所有表
	var tables []string
	if err := db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		log.Fatal("Failed to get tables:", err)
	}

	fmt.Println("=== 现有表列表 ===")
	for i, table := range tables {
		fmt.Printf("%d. %s\n", i+1, table)
	}
	fmt.Println()

	// 查询每个表的结构
	for _, table := range tables {
		fmt.Printf("=== 表 %s 的结构 ===\n", table)
		
		var columns []struct {
			Field   string `gorm:"column:Field"`
			Type    string `gorm:"column:Type"`
			Null    string `gorm:"column:Null"`
			Key     string `gorm:"column:Key"`
			Default *string `gorm:"column:Default"`
			Extra   string `gorm:"column:Extra"`
		}
		
		if err := db.Raw(fmt.Sprintf("DESCRIBE %s", table)).Scan(&columns).Error; err != nil {
			fmt.Printf("Error describing table %s: %v\n", table, err)
			continue
		}
		
		for _, col := range columns {
			defaultVal := "NULL"
			if col.Default != nil {
				defaultVal = *col.Default
			}
			fmt.Printf("  %-20s %-20s %-5s %-5s %-10s %s\n", 
				col.Field, col.Type, col.Null, col.Key, defaultVal, col.Extra)
		}
		fmt.Println()
	}

	// 查询一些示例数据
	fmt.Println("=== 示例数据 ===")
	for _, table := range tables {
		var count int64
		if err := db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count).Error; err != nil {
			fmt.Printf("Error counting %s: %v\n", table, err)
			continue
		}
		fmt.Printf("表 %s: %d 条记录\n", table, count)
	}
}
