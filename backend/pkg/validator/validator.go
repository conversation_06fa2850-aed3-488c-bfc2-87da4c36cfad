package validator

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	v := reflect.ValueOf(s)
	t := reflect.TypeOf(s)

	// 如果是指针，获取指向的值
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
		t = t.Elem()
	}

	// 只处理结构体
	if v.Kind() != reflect.Struct {
		return fmt.Errorf("expected struct, got %s", v.Kind())
	}

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 获取validate标签
		validateTag := fieldType.Tag.Get("validate")
		if validateTag == "" {
			continue
		}

		// 解析验证规则
		rules := strings.Split(validateTag, ",")
		for _, rule := range rules {
			rule = strings.TrimSpace(rule)
			if err := validateField(field, fieldType.Name, rule); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateField 验证单个字段
func validateField(field reflect.Value, fieldName, rule string) error {
	switch {
	case rule == "required":
		return validateRequired(field, fieldName)
	case strings.HasPrefix(rule, "min="):
		return validateMin(field, fieldName, rule)
	case strings.HasPrefix(rule, "max="):
		return validateMax(field, fieldName, rule)
	case rule == "email":
		return validateEmail(field, fieldName)
	case rule == "url":
		return validateURL(field, fieldName)
	case strings.HasPrefix(rule, "oneof="):
		return validateOneOf(field, fieldName, rule)
	case rule == "omitempty":
		// 如果字段为空，跳过其他验证
		if isEmpty(field) {
			return nil
		}
	}
	return nil
}

// validateRequired 验证必填字段
func validateRequired(field reflect.Value, fieldName string) error {
	if isEmpty(field) {
		return fmt.Errorf("%s is required", fieldName)
	}
	return nil
}

// validateMin 验证最小值/长度
func validateMin(field reflect.Value, fieldName, rule string) error {
	minStr := strings.TrimPrefix(rule, "min=")
	var min int
	fmt.Sscanf(minStr, "%d", &min)

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) < min {
			return fmt.Errorf("%s must be at least %d characters", fieldName, min)
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if field.Int() < int64(min) {
			return fmt.Errorf("%s must be at least %d", fieldName, min)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if field.Uint() < uint64(min) {
			return fmt.Errorf("%s must be at least %d", fieldName, min)
		}
	}
	return nil
}

// validateMax 验证最大值/长度
func validateMax(field reflect.Value, fieldName, rule string) error {
	maxStr := strings.TrimPrefix(rule, "max=")
	var max int
	fmt.Sscanf(maxStr, "%d", &max)

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) > max {
			return fmt.Errorf("%s must be at most %d characters", fieldName, max)
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if field.Int() > int64(max) {
			return fmt.Errorf("%s must be at most %d", fieldName, max)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if field.Uint() > uint64(max) {
			return fmt.Errorf("%s must be at most %d", fieldName, max)
		}
	}
	return nil
}

// validateEmail 验证邮箱格式
func validateEmail(field reflect.Value, fieldName string) error {
	if field.Kind() != reflect.String {
		return nil
	}

	email := field.String()
	if email == "" {
		return nil // 空值由required规则处理
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("%s must be a valid email address", fieldName)
	}
	return nil
}

// validateURL 验证URL格式
func validateURL(field reflect.Value, fieldName string) error {
	if field.Kind() != reflect.String {
		return nil
	}

	url := field.String()
	if url == "" {
		return nil // 空值由required规则处理
	}

	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(url) {
		return fmt.Errorf("%s must be a valid URL", fieldName)
	}
	return nil
}

// validateOneOf 验证枚举值
func validateOneOf(field reflect.Value, fieldName, rule string) error {
	if field.Kind() != reflect.String && field.Kind() != reflect.Int8 {
		return nil
	}

	oneofStr := strings.TrimPrefix(rule, "oneof=")
	validValues := strings.Split(oneofStr, " ")

	var fieldValue string
	if field.Kind() == reflect.String {
		fieldValue = field.String()
	} else {
		fieldValue = fmt.Sprintf("%d", field.Int())
	}

	if fieldValue == "" {
		return nil // 空值由required规则处理
	}

	for _, validValue := range validValues {
		if fieldValue == validValue {
			return nil
		}
	}

	return fmt.Errorf("%s must be one of: %s", fieldName, strings.Join(validValues, ", "))
}

// isEmpty 检查字段是否为空
func isEmpty(field reflect.Value) bool {
	switch field.Kind() {
	case reflect.String:
		return field.String() == ""
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return field.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return field.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return field.Float() == 0
	case reflect.Bool:
		return !field.Bool()
	case reflect.Ptr, reflect.Interface:
		return field.IsNil()
	case reflect.Slice, reflect.Map, reflect.Array:
		return field.Len() == 0
	}
	return false
}
