package logger

import (
	"log"
	"os"
)

// Logger 日志接口
type Logger interface {
	Info(msg string, args ...interface{})
	Error(msg string, err error, args ...interface{})
	Debug(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Fatal(msg string, err error, args ...interface{})
}

// logger 日志实现
type logger struct {
	infoLogger  *log.Logger
	errorLogger *log.Logger
	debugLogger *log.Logger
	warnLogger  *log.Logger
	fatalLogger *log.Logger
	level       string
}

// New 创建新的日志实例
func New(level string) Logger {
	return &logger{
		infoLogger:  log.New(os.Stdout, "[INFO] ", log.LstdFlags|log.Lshortfile),
		errorLogger: log.New(os.Stderr, "[ERROR] ", log.LstdFlags|log.Lshortfile),
		debugLogger: log.New(os.Stdout, "[DEBUG] ", log.LstdFlags|log.Lshortfile),
		warnLogger:  log.New(os.Stdo<PERSON>, "[WARN] ", log.LstdFlags|log.Lshortfile),
		fatalLogger: log.New(os.Stderr, "[FATAL] ", log.LstdFlags|log.Lshortfile),
		level:       level,
	}
}

// Info 信息日志
func (l *logger) Info(msg string, args ...interface{}) {
	if l.shouldLog("info") {
		if len(args) > 0 {
			l.infoLogger.Printf(msg, args...)
		} else {
			l.infoLogger.Println(msg)
		}
	}
}

// Error 错误日志
func (l *logger) Error(msg string, err error, args ...interface{}) {
	if l.shouldLog("error") {
		if err != nil {
			msg = msg + ": " + err.Error()
		}
		if len(args) > 0 {
			l.errorLogger.Printf(msg, args...)
		} else {
			l.errorLogger.Println(msg)
		}
	}
}

// Debug 调试日志
func (l *logger) Debug(msg string, args ...interface{}) {
	if l.shouldLog("debug") {
		if len(args) > 0 {
			l.debugLogger.Printf(msg, args...)
		} else {
			l.debugLogger.Println(msg)
		}
	}
}

// Warn 警告日志
func (l *logger) Warn(msg string, args ...interface{}) {
	if l.shouldLog("warn") {
		if len(args) > 0 {
			l.warnLogger.Printf(msg, args...)
		} else {
			l.warnLogger.Println(msg)
		}
	}
}

// Fatal 致命错误日志
func (l *logger) Fatal(msg string, err error, args ...interface{}) {
	if err != nil {
		msg = msg + ": " + err.Error()
	}
	if len(args) > 0 {
		l.fatalLogger.Printf(msg, args...)
	} else {
		l.fatalLogger.Println(msg)
	}
	os.Exit(1)
}

// shouldLog 检查是否应该记录日志
func (l *logger) shouldLog(level string) bool {
	levels := map[string]int{
		"debug": 0,
		"info":  1,
		"warn":  2,
		"error": 3,
		"fatal": 4,
	}

	currentLevel, exists := levels[l.level]
	if !exists {
		currentLevel = 1 // 默认info级别
	}

	logLevel, exists := levels[level]
	if !exists {
		return true
	}

	return logLevel >= currentLevel
}
