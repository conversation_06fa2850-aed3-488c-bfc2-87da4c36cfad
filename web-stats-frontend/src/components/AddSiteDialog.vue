<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加网站"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="网站名称" prop="site_name">
        <el-input
          v-model="form.site_name"
          placeholder="请输入网站名称"
        />
      </el-form-item>
      
      <el-form-item label="网站域名" prop="domain">
        <el-input
          v-model="form.domain"
          placeholder="example.com"
        />
      </el-form-item>
      
      <el-form-item label="网站URL" prop="site_url">
        <el-input
          v-model="form.site_url"
          placeholder="https://example.com"
        />
      </el-form-item>
      
      <el-form-item label="网站类型" prop="site_type">
        <el-select v-model="form.site_type" placeholder="请选择网站类型">
          <el-option label="博客" value="blog" />
          <el-option label="企业官网" value="corporate" />
          <el-option label="电商网站" value="ecommerce" />
          <el-option label="新闻网站" value="news" />
          <el-option label="论坛社区" value="community" />
          <el-option label="在线教育" value="education" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="所在地区" prop="region">
        <el-input
          v-model="form.region"
          placeholder="请输入地区（可选）"
        />
      </el-form-item>
      
      <el-form-item label="网站描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入网站描述（可选）"
        />
      </el-form-item>
      
      <el-form-item label="查看密码" prop="view_password">
        <el-input
          v-model="form.view_password"
          placeholder="设置查看密码（可选）"
          show-password
        />
        <div class="form-tip">
          设置后，其他人需要输入密码才能查看统计数据
        </div>
      </el-form-item>
      
      <el-form-item label="公开设置">
        <el-switch
          v-model="form.is_public"
          active-text="公开"
          inactive-text="私有"
        />
        <div class="form-tip">
          公开后，其他人可以查看您的网站统计数据
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useSitesStore } from '@/stores/sites'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const sitesStore = useSitesStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

const dialogVisible = ref(false)

const form = reactive({
  site_name: '',
  domain: '',
  site_url: '',
  site_type: '',
  region: '',
  description: '',
  view_password: '',
  is_public: false
})

const rules: FormRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 50, message: '网站名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请输入网站域名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      message: '请输入有效的域名',
      trigger: 'blur'
    }
  ],
  site_url: [
    { required: true, message: '请输入网站URL', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/,
      message: '请输入有效的URL（以http://或https://开头）',
      trigger: 'blur'
    }
  ],
  site_type: [
    { required: true, message: '请选择网站类型', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 自动填充URL
watch(() => form.domain, (domain) => {
  if (domain && !form.site_url) {
    form.site_url = `https://${domain}`
  }
})

const resetForm = () => {
  Object.assign(form, {
    site_name: '',
    domain: '',
    site_url: '',
    site_type: '',
    region: '',
    description: '',
    view_password: '',
    is_public: false
  })
  formRef.value?.clearValidate()
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const result = await sitesStore.createSite(form)
        if (result.success) {
          ElMessage.success('网站添加成功')
          emit('success')
        } else {
          ElMessage.error(result.message)
        }
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
