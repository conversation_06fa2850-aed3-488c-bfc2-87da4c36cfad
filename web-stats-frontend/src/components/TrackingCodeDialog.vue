<template>
  <el-dialog
    v-model="dialogVisible"
    title="获取统计代码"
    width="600px"
    :before-close="handleClose"
  >
    <div v-if="site" class="tracking-code-content">
      <div class="site-info">
        <h3>{{ site.site_name }}</h3>
        <p>{{ site.domain }}</p>
      </div>
      
      <el-divider />
      
      <div class="code-section">
        <div class="section-header">
          <h4>统计代码</h4>
          <div class="actions">
            <el-button
              size="small"
              :icon="RefreshRight"
              @click="regenerateCode"
              :loading="regenerating"
            >
              重新生成
            </el-button>
            <el-button
              size="small"
              :icon="CopyDocument"
              @click="copyCode"
            >
              复制代码
            </el-button>
          </div>
        </div>
        
        <div class="code-block">
          <pre><code ref="codeRef">{{ trackingCode }}</code></pre>
        </div>
      </div>
      
      <el-divider />
      
      <div class="instructions">
        <h4>安装说明</h4>
        <ol>
          <li>复制上面的统计代码</li>
          <li>将代码粘贴到您网站的每个页面的 <code>&lt;/head&gt;</code> 标签之前</li>
          <li>保存并发布您的网站</li>
          <li>等待几分钟后，您就可以在统计报告中看到访问数据了</li>
        </ol>
        
        <el-alert
          title="注意事项"
          type="info"
          :closable="false"
          show-icon
        >
          <ul>
            <li>请确保统计代码安装在所有需要统计的页面上</li>
            <li>代码应该放在页面的 &lt;head&gt; 部分，以确保正确统计</li>
            <li>如果您使用了缓存，请清除缓存后再查看统计数据</li>
            <li>统计数据通常在安装代码后 10-30 分钟开始显示</li>
          </ul>
        </el-alert>
      </div>
      
      <div class="verification">
        <h4>验证安装</h4>
        <p>您可以通过以下方式验证统计代码是否正确安装：</p>
        <ul>
          <li>在浏览器中访问您的网站</li>
          <li>打开浏览器开发者工具（F12）</li>
          <li>查看网络请求，应该能看到发送到统计服务器的请求</li>
          <li>或者等待几分钟后查看统计报告中的实时访客数据</li>
        </ul>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { RefreshRight, CopyDocument } from '@element-plus/icons-vue'
import { useSitesStore } from '@/stores/sites'
import type { Site } from '@/stores/sites'

interface Props {
  modelValue: boolean
  site: Site | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const sitesStore = useSitesStore()
const codeRef = ref<HTMLElement>()
const regenerating = ref(false)

const dialogVisible = ref(false)

const trackingCode = computed(() => {
  if (!props.site) return ''
  
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
  
  return `<!-- 网站统计代码 -->
<script>
(function() {
  var script = document.createElement('script');
  script.src = '${baseUrl}/js/stats.js';
  script.async = true;
  script.setAttribute('data-site-id', '${props.site.tracking_code}');
  var firstScript = document.getElementsByTagName('script')[0];
  firstScript.parentNode.insertBefore(script, firstScript);
})();
</script>
<!-- 网站统计代码结束 -->`
})

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

const handleClose = () => {
  dialogVisible.value = false
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(trackingCode.value)
    ElMessage.success('统计代码已复制到剪贴板')
  } catch (error) {
    // 降级方案：选择文本
    if (codeRef.value) {
      const range = document.createRange()
      range.selectNode(codeRef.value)
      const selection = window.getSelection()
      selection?.removeAllRanges()
      selection?.addRange(range)
      
      try {
        document.execCommand('copy')
        ElMessage.success('统计代码已复制到剪贴板')
      } catch (err) {
        ElMessage.error('复制失败，请手动选择并复制代码')
      }
      
      selection?.removeAllRanges()
    }
  }
}

const regenerateCode = async () => {
  if (!props.site) return
  
  regenerating.value = true
  try {
    const result = await sitesStore.regenerateTrackingCode(props.site.id)
    if (result.success) {
      ElMessage.success('统计代码已重新生成')
    } else {
      ElMessage.error(result.message)
    }
  } finally {
    regenerating.value = false
  }
}
</script>

<style scoped>
.tracking-code-content {
  max-height: 70vh;
  overflow-y: auto;
}

.site-info {
  text-align: center;
  margin-bottom: 16px;
}

.site-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.code-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.instructions h4,
.verification h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.instructions ol,
.verification ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.instructions ol li,
.verification ul li {
  margin-bottom: 8px;
}

.instructions code {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e83e8c;
}

.el-alert {
  margin-top: 16px;
}

.el-alert ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.el-alert li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.verification {
  margin-top: 24px;
}

.verification p {
  margin: 0 0 8px 0;
  color: #606266;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
