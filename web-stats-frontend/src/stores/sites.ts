import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/utils/request'

export interface Site {
  id: number
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region?: string
  description?: string
  tracking_code: string
  view_password?: string
  is_public: boolean
  status: number
  created_at: string
  updated_at: string
}

export interface SiteForm {
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region?: string
  description?: string
  view_password?: string
  is_public: boolean
}

export interface SiteStats {
  pv: number
  uv: number
  ip_count: number
  bounce_rate: number
  avg_duration: number
  new_visitors: number
  returning_visitors: number
}

export const useSitesStore = defineStore('sites', () => {
  const sites = ref<Site[]>([])
  const currentSite = ref<Site | null>(null)
  const loading = ref(false)
  const stats = ref<Record<number, SiteStats>>({})

  // 获取网站列表
  const fetchSites = async () => {
    loading.value = true
    try {
      const response = await request.get('/api/sites')
      sites.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch sites:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取单个网站信息
  const fetchSite = async (id: number) => {
    loading.value = true
    try {
      const response = await request.get(`/api/sites/${id}`)
      currentSite.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch site:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建网站
  const createSite = async (siteForm: SiteForm) => {
    loading.value = true
    try {
      const response = await request.post('/api/sites', siteForm)
      sites.value.push(response.data)
      return { success: true, data: response.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '创建网站失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 更新网站
  const updateSite = async (id: number, siteForm: Partial<SiteForm>) => {
    loading.value = true
    try {
      const response = await request.put(`/api/sites/${id}`, siteForm)
      const index = sites.value.findIndex(site => site.id === id)
      if (index !== -1) {
        sites.value[index] = response.data
      }
      if (currentSite.value?.id === id) {
        currentSite.value = response.data
      }
      return { success: true, data: response.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '更新网站失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 删除网站
  const deleteSite = async (id: number) => {
    loading.value = true
    try {
      await request.delete(`/api/sites/${id}`)
      sites.value = sites.value.filter(site => site.id !== id)
      if (currentSite.value?.id === id) {
        currentSite.value = null
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '删除网站失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取网站统计数据
  const fetchSiteStats = async (id: number, dateRange?: { start: string; end: string }) => {
    try {
      const params = dateRange ? { start_date: dateRange.start, end_date: dateRange.end } : {}
      const response = await request.get(`/api/sites/${id}/stats`, { params })
      stats.value[id] = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch site stats:', error)
      throw error
    }
  }

  // 生成新的跟踪代码
  const regenerateTrackingCode = async (id: number) => {
    loading.value = true
    try {
      const response = await request.post(`/api/sites/${id}/regenerate-code`)
      const index = sites.value.findIndex(site => site.id === id)
      if (index !== -1) {
        sites.value[index].tracking_code = response.data.tracking_code
      }
      if (currentSite.value?.id === id) {
        currentSite.value.tracking_code = response.data.tracking_code
      }
      return { success: true, tracking_code: response.data.tracking_code }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '生成跟踪代码失败' 
      }
    } finally {
      loading.value = false
    }
  }

  return {
    sites,
    currentSite,
    loading,
    stats,
    fetchSites,
    fetchSite,
    createSite,
    updateSite,
    deleteSite,
    fetchSiteStats,
    regenerateTrackingCode
  }
})
