import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/sites'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/Register.vue')
    },
    {
      path: '/sites',
      name: 'sites',
      component: () => import('../views/Sites.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id',
      name: 'site-overview',
      component: () => import('../views/SiteOverview.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id/traffic',
      name: 'traffic-analysis',
      component: () => import('../views/TrafficAnalysis.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id/sources',
      name: 'source-analysis',
      component: () => import('../views/SourceAnalysis.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id/pages',
      name: 'page-analysis',
      component: () => import('../views/PageAnalysis.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id/visitors',
      name: 'visitor-analysis',
      component: () => import('../views/VisitorAnalysis.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/site/:id/settings',
      name: 'site-settings',
      component: () => import('../views/SiteSettings.vue'),
      meta: { requiresAuth: true }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router
