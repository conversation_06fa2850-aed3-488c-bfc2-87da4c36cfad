<template>
  <div class="sites-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>我的网站</h1>
        <p>管理您的网站统计</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showAddSiteDialog">
          添加网站
        </el-button>
        <el-dropdown @command="handleUserAction">
          <el-button :icon="User" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="settings">设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview" v-if="overviewStats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon pv">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(overviewStats.total_pv) }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon uv">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(overviewStats.total_uv) }}</div>
              <div class="stat-label">总访客数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon sites">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ sitesStore.sites.length }}</div>
              <div class="stat-label">网站数量</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-card">
            <div class="stat-icon duration">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatDuration(overviewStats.avg_duration) }}</div>
              <div class="stat-label">平均停留</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 网站列表 -->
    <div class="sites-list">
      <el-card v-loading="sitesStore.loading">
        <template #header>
          <div class="card-header">
            <span>网站列表</span>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索网站..."
              :prefix-icon="Search"
              style="width: 200px"
              clearable
            />
          </div>
        </template>

        <div v-if="filteredSites.length === 0" class="empty-state">
          <el-empty description="暂无网站数据">
            <el-button type="primary" @click="showAddSiteDialog">添加第一个网站</el-button>
          </el-empty>
        </div>

        <div v-else class="sites-grid">
          <div
            v-for="site in filteredSites"
            :key="site.id"
            class="site-card"
            @click="goToSiteOverview(site.id)"
          >
            <div class="site-header">
              <div class="site-info">
                <h3>{{ site.site_name }}</h3>
                <p>{{ site.domain }}</p>
              </div>
              <div class="site-actions">
                <el-dropdown @command="(command) => handleSiteAction(command, site)">
                  <el-button :icon="MoreFilled" circle size="small" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="code">获取代码</el-dropdown-item>
                      <el-dropdown-item command="settings">设置</el-dropdown-item>
                      <el-dropdown-item divided command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="site-stats" v-if="sitesStore.stats[site.id]">
              <div class="stat-item">
                <span class="label">今日PV</span>
                <span class="value">{{ formatNumber(sitesStore.stats[site.id].pv) }}</span>
              </div>
              <div class="stat-item">
                <span class="label">今日UV</span>
                <span class="value">{{ formatNumber(sitesStore.stats[site.id].uv) }}</span>
              </div>
              <div class="stat-item">
                <span class="label">跳出率</span>
                <span class="value">{{ (sitesStore.stats[site.id].bounce_rate * 100).toFixed(1) }}%</span>
              </div>
            </div>

            <div class="site-footer">
              <el-tag :type="site.status === 1 ? 'success' : 'danger'" size="small">
                {{ site.status === 1 ? '正常' : '停用' }}
              </el-tag>
              <span class="created-time">
                {{ formatDate(site.created_at) }}
              </span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 添加网站对话框 -->
    <AddSiteDialog
      v-model="showAddDialog"
      @success="handleAddSuccess"
    />

    <!-- 编辑网站对话框 -->
    <EditSiteDialog
      v-model="showEditDialog"
      :site="currentEditSite"
      @success="handleEditSuccess"
    />

    <!-- 获取代码对话框 -->
    <TrackingCodeDialog
      v-model="showCodeDialog"
      :site="currentCodeSite"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, User, View, Monitor, Timer, Search, MoreFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useSitesStore } from '@/stores/sites'
import type { Site } from '@/stores/sites'
import AddSiteDialog from '@/components/AddSiteDialog.vue'
import EditSiteDialog from '@/components/EditSiteDialog.vue'
import TrackingCodeDialog from '@/components/TrackingCodeDialog.vue'

const router = useRouter()
const authStore = useAuthStore()
const sitesStore = useSitesStore()

const searchKeyword = ref('')
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showCodeDialog = ref(false)
const currentEditSite = ref<Site | null>(null)
const currentCodeSite = ref<Site | null>(null)
const overviewStats = ref<any>(null)

const filteredSites = computed(() => {
  if (!searchKeyword.value) return sitesStore.sites
  return sitesStore.sites.filter(site =>
    site.site_name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    site.domain.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const formatNumber = (num: number) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
  return num.toString()
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const showAddSiteDialog = () => {
  showAddDialog.value = true
}

const handleAddSuccess = () => {
  showAddDialog.value = false
  loadSitesData()
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  loadSitesData()
}

const handleUserAction = (command: string) => {
  switch (command) {
    case 'profile':
      // TODO: 打开个人资料页面
      ElMessage.info('个人资料功能开发中')
      break
    case 'settings':
      // TODO: 打开设置页面
      ElMessage.info('设置功能开发中')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}

const handleSiteAction = (command: string, site: Site) => {
  switch (command) {
    case 'edit':
      currentEditSite.value = site
      showEditDialog.value = true
      break
    case 'code':
      currentCodeSite.value = site
      showCodeDialog.value = true
      break
    case 'settings':
      router.push(`/site/${site.id}/settings`)
      break
    case 'delete':
      handleDeleteSite(site)
      break
  }
}

const handleDeleteSite = (site: Site) => {
  ElMessageBox.confirm(
    `确定要删除网站 "${site.site_name}" 吗？此操作不可恢复。`,
    '删除网站',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const result = await sitesStore.deleteSite(site.id)
    if (result.success) {
      ElMessage.success('网站删除成功')
      loadSitesData()
    } else {
      ElMessage.error(result.message)
    }
  }).catch(() => {
    // 用户取消
  })
}

const goToSiteOverview = (siteId: number) => {
  router.push(`/site/${siteId}`)
}

const loadSitesData = async () => {
  try {
    await sitesStore.fetchSites()
    
    // 加载每个网站的统计数据
    for (const site of sitesStore.sites) {
      await sitesStore.fetchSiteStats(site.id)
    }
    
    // 计算总览统计
    calculateOverviewStats()
  } catch (error) {
    console.error('Failed to load sites data:', error)
  }
}

const calculateOverviewStats = () => {
  const stats = Object.values(sitesStore.stats)
  if (stats.length === 0) return
  
  overviewStats.value = {
    total_pv: stats.reduce((sum, stat) => sum + stat.pv, 0),
    total_uv: stats.reduce((sum, stat) => sum + stat.uv, 0),
    avg_duration: stats.reduce((sum, stat) => sum + stat.avg_duration, 0) / stats.length
  }
}

onMounted(() => {
  loadSitesData()
})
</script>

<style scoped>
.sites-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.pv { background: #409eff; }
.stat-icon.uv { background: #67c23a; }
.stat-icon.sites { background: #e6a23c; }
.stat-icon.duration { background: #f56c6c; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.sites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.site-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.site-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.site-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.site-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-item .label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-item .value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.site-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.created-time {
  font-size: 12px;
  color: #c0c4cc;
}

@media (max-width: 768px) {
  .sites-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    align-self: flex-end;
  }
  
  .sites-grid {
    grid-template-columns: 1fr;
  }
}
</style>
