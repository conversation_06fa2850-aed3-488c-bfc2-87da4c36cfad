# 网站统计系统全栈技术栈规划

## 项目概述
开发一个类似百度统计、CNZZ的网站统计程序，提供完整的网站流量分析功能。

## 技术栈确认

### 后端技术栈
- **语言**: Go 1.23.9
- **Web框架**: Go Fiber v2
- **ORM**: GORM v1.30.0
- **数据库**: MySQL 8.0
- **认证**: JWT (golang-jwt/jwt/v5)
- **加密**: golang.org/x/crypto

### 前端技术栈
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5
- **开发语言**: TypeScript
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **代码规范**: ESLint

### 端口分配
- **用户前端**: 2002 (普通用户访问)
- **后台管理前端**: 4002 (管理员管理)
- **后台管理后端**: 9002 (提供API服务)

## 系统架构设计

### 1. 数据流程架构
```
网站页面 → JS统计代码 → 数据收集API → 数据处理 → MySQL存储 → 前端展示
```

### 2. 核心模块划分

#### A. 数据收集模块 (JS SDK)
- **功能**: 在用户网站中嵌入统计代码，收集访问数据
- **技术**: 原生JavaScript (异步加载)
- **数据收集内容**:
  - 页面浏览量(PV)
  - 独立访客(UV)
  - IP地址
  - 来源信息(搜索引擎、关键词、来路页面)
  - 用户环境(浏览器、操作系统、屏幕分辨率)
  - 地理位置信息
  - 访问时长和跳出率

#### B. 数据接收API模块 (Go Fiber)
- **端口**: 9002
- **功能**: 接收JS发送的统计数据
- **主要接口**:
  - `POST /api/track` - 接收页面访问数据
  - `POST /api/event` - 接收事件跟踪数据
  - `GET /api/online` - 获取在线人数

#### C. 数据处理模块 (Go)
- **功能**: 数据清洗、去重、聚合计算
- **处理流程**:
  1. 数据验证和清洗
  2. IP地址解析(地理位置)
  3. User Agent解析(浏览器、操作系统)
  4. 访客识别和会话管理
  5. 实时统计计算
  6. 预聚合数据生成

#### D. 用户前端模块 (Vue 3 + TypeScript)
- **端口**: 2002
- **功能**: 普通用户查看统计报告
- **主要页面**:
  - 用户首页(网站列表)
  - 网站概况页
  - 流量分析(趋势分析、对比分析、当前在线、访问明细)
  - 来源分析(全部来源、搜索分析、搜索词等)
  - 受访分析(受访域名、受访页面等)
  - 访客分析(地区分布、系统环境、新老访客等)
  - 设置页面

#### E. 后台管理模块 (Vue 3 + TypeScript)
- **端口**: 4002
- **功能**: 系统管理和用户管理
- **主要功能**:
  - 用户管理
  - 网站管理
  - 系统配置
  - 数据统计
  - 权限管理

## 详细实施计划

### 第一阶段：基础架构搭建 (1-2周)

#### 1.1 数据库优化和完善
- **任务**: 基于现有database_simple.sql进行优化
- **重点**:
  - 确认分区表设计
  - 优化索引策略
  - 添加必要的约束和触发器
  - 创建测试数据

#### 1.2 Go后端API框架搭建
- **目录结构**:
```
backend/
├── cmd/
│   └── server/
│       └── main.go
├── internal/
│   ├── config/
│   ├── handler/
│   ├── middleware/
│   ├── model/
│   ├── service/
│   └── repository/
├── pkg/
│   ├── database/
│   ├── logger/
│   └── utils/
└── api/
    └── routes/
```

#### 1.3 前端项目初始化
- **用户前端项目** (端口2002)
- **管理后台项目** (端口4002)
- **共享组件库**

### 第二阶段：核心功能开发 (3-4周)

#### 2.1 JS统计代码开发
- **异步加载机制**
- **数据收集逻辑**
- **防重复提交**
- **错误处理机制**

#### 2.2 数据接收API开发
- **数据接收接口**
- **数据验证和清洗**
- **实时数据处理**
- **批量数据入库**

#### 2.3 用户认证系统
- **用户注册/登录**
- **JWT认证**
- **权限控制**
- **会话管理**

### 第三阶段：统计功能实现 (4-5周)

#### 3.1 基础统计功能
- **PV/UV/IP统计**
- **实时在线人数**
- **访问趋势分析**
- **地理位置分析**

#### 3.2 高级分析功能
- **来源分析**
- **搜索词分析**
- **页面分析**
- **用户行为分析**

#### 3.3 数据可视化
- **图表组件开发**
- **实时数据更新**
- **交互式报表**

### 第四阶段：前端界面开发 (3-4周)

#### 4.1 用户前端开发
- **首页和网站列表**
- **统计报告页面**
- **各类分析页面**
- **设置和配置页面**

#### 4.2 管理后台开发
- **用户管理界面**
- **网站管理界面**
- **系统配置界面**
- **数据监控界面**

### 第五阶段：性能优化和测试 (2-3周)

#### 5.1 性能优化
- **数据库查询优化**
- **缓存策略实施**
- **前端性能优化**
- **API响应优化**

#### 5.2 测试和部署
- **单元测试**
- **集成测试**
- **压力测试**
- **部署配置**

## 关键技术难点和解决方案

### 1. 大数据量处理
- **分区表设计**: 按月分区存储访问数据
- **预聚合策略**: 日级、小时级数据预计算
- **索引优化**: 复合索引和覆盖索引
- **数据归档**: 历史数据定期归档

### 2. 实时性能要求
- **异步处理**: 数据收集和处理分离
- **缓存机制**: Redis缓存热点数据
- **批量处理**: 批量插入和更新
- **连接池**: 数据库连接池优化

### 3. 数据准确性
- **去重机制**: 基于访客标识去重
- **数据验证**: 多层数据验证
- **异常处理**: 完善的错误处理机制
- **数据修复**: 数据一致性检查和修复

## 部署架构

### 生产环境部署
```
负载均衡器 (Nginx)
├── 用户前端 (2002端口)
├── 管理后台 (4002端口)
└── 后端API (9002端口)
    └── MySQL 8.0 (主从复制)
```

### 开发环境配置
- **Docker容器化部署**
- **热重载开发环境**
- **数据库迁移工具**
- **API文档自动生成**

## 项目里程碑

1. **第1周**: 数据库设计完成，后端框架搭建
2. **第3周**: JS统计代码完成，数据收集API完成
3. **第6周**: 基础统计功能完成
4. **第10周**: 前端界面开发完成
5. **第12周**: 系统测试和优化完成，准备上线

## 风险评估和应对

### 技术风险
- **大数据量性能问题**: 提前进行压力测试，优化数据库设计
- **实时性要求**: 采用异步处理和缓存策略
- **数据准确性**: 建立完善的数据验证和监控机制

### 项目风险
- **开发周期**: 采用敏捷开发，分阶段交付
- **需求变更**: 预留20%的缓冲时间
- **技术难点**: 提前进行技术预研和原型验证

## 详细技术实现方案

### JS统计代码实现

#### 统计代码结构
```javascript
// 异步加载统计代码 (推荐方式)
<script>
var _stats = _stats || [];
(function () {
    var um = document.createElement("script");
    um.src = "https://stats.yourdomain.com/js/track.js?id=SITE_ID&async=1";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(um, s);
})();
</script>
```

#### 数据收集功能
- **页面访问跟踪**: 自动收集PV数据
- **用户行为跟踪**: 点击、滚动、停留时间
- **来源分析**: document.referrer解析
- **设备信息**: navigator对象解析
- **地理位置**: IP地址获取(后端处理)

### 数据库设计优化建议

#### 基于现有database_simple.sql的改进
1. **分区策略优化**: 当前按年月分区，建议考虑按日分区以提高查询性能
2. **索引优化**: 添加更多复合索引支持复杂查询
3. **数据归档**: 自动归档超过1年的历史数据
4. **读写分离**: 主库写入，从库查询

#### 性能优化策略
- **预聚合表**: 已设计stats_daily表，建议增加小时级聚合
- **实时统计**: realtime_stats表支持5分钟级实时数据
- **缓存策略**: 热点数据Redis缓存
- **批量处理**: 访问数据批量插入，减少数据库压力

### API接口设计

#### 数据收集接口
```go
// POST /api/v1/track
type TrackRequest struct {
    SiteID      string `json:"site_id"`
    PageURL     string `json:"page_url"`
    PageTitle   string `json:"page_title"`
    Referrer    string `json:"referrer"`
    UserAgent   string `json:"user_agent"`
    ScreenSize  string `json:"screen_size"`
    Language    string `json:"language"`
    Timestamp   int64  `json:"timestamp"`
    VisitorID   string `json:"visitor_id"`
    SessionID   string `json:"session_id"`
}
```

#### 统计查询接口
```go
// GET /api/v1/stats/overview
// GET /api/v1/stats/trend
// GET /api/v1/stats/source
// GET /api/v1/stats/pages
// GET /api/v1/stats/visitors
// GET /api/v1/stats/realtime
```

### 前端页面功能详细设计

#### 用户前端 (端口2002)
基于您提供的需求文档，需要实现以下页面：

1. **用户首页**
   - 网站列表展示
   - 今日/昨日/预计今日数据对比
   - 快速操作(查看报告、获取代码、设置、删除)

2. **网站概况页**
   - 实时流量数据
   - 趋势图表(支持今天/昨天/最近7天/最近30天)
   - Top10搜索词、来源网站、入口页面、受访页面
   - 新老访客分析
   - 地域分布地图

3. **流量分析模块**
   - 趋势分析: 按时间维度的详细数据
   - 对比分析: 不同时间段的数据对比
   - 当前在线: 实时在线访客数和30分钟访问情况
   - 访问明细: 详细的访问记录列表

4. **来源分析模块**
   - 全部来源: 搜索引擎、外部链接、直接访问分类
   - 搜索分析: 各搜索引擎流量分布
   - 搜索词: 关键词排行和趋势
   - 最近搜索: 实时搜索词记录
   - 来路域名/页面: 外部来源详细分析
   - 来源升降榜: 来源变化趋势

5. **受访分析模块**
   - 受访域名: 网站内各域名访问情况
   - 受访页面: 页面访问排行和停留时间
   - 受访升降榜: 页面访问变化趋势

6. **访客分析模块**
   - 地区分布: 中国地图热力图显示
   - 系统环境: 操作系统、浏览器、设备类型分析
   - 新老访客: 访客类型分析和行为对比
   - 忠诚度: 访问深度和频次分析
   - 活跃度: 访问时长分布

7. **设置模块**
   - 站点资料: 网站基本信息设置
   - 获取代码: 多种形式的统计代码
   - 排除设置: 排除受访、来路、IP、搜索词、爬虫
   - 查看密码: 统计数据访问权限设置
   - 关闭统计: 统计服务开关

#### 管理后台 (端口4002)
1. **用户管理**: 用户注册、登录、权限管理
2. **网站管理**: 网站添加、编辑、删除、状态管理
3. **系统配置**: 系统参数设置、功能开关
4. **数据监控**: 系统运行状态、数据库性能监控
5. **统计报表**: 全局统计数据、用户使用情况

### 部署和运维方案

#### 服务器配置建议
- **CPU**: 8核以上
- **内存**: 16GB以上
- **存储**: SSD 500GB以上
- **网络**: 100Mbps以上带宽

#### 部署架构
```
Internet
    ↓
Nginx (负载均衡 + SSL终止)
    ↓
┌─────────────────────────────────────┐
│  Docker Compose 容器编排             │
├─────────────────────────────────────┤
│  用户前端 (Vue3) - 端口2002          │
│  管理后台 (Vue3) - 端口4002          │
│  后端API (Go Fiber) - 端口9002      │
│  MySQL 8.0 (主从复制)               │
│  Redis (缓存)                       │
│  Nginx (静态文件服务)               │
└─────────────────────────────────────┘
```

#### 监控和日志
- **应用监控**: Prometheus + Grafana
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **错误追踪**: Sentry
- **性能监控**: APM工具

### 开发工具和流程

#### 开发环境
- **代码管理**: Git + GitLab/GitHub
- **API文档**: Swagger/OpenAPI
- **数据库迁移**: GORM AutoMigrate + 自定义迁移脚本
- **测试框架**: Go testing + Vue Test Utils
- **CI/CD**: GitLab CI 或 GitHub Actions

#### 代码规范
- **Go**: gofmt + golint + go vet
- **Vue**: ESLint + Prettier
- **数据库**: 统一命名规范和注释规范
- **API**: RESTful设计规范

这个规划涵盖了从JS代码部署到网站，数据获取、入库，前台用户访问数据报告，到后台管理的完整流程。每个阶段都有明确的目标和交付物，确保项目能够按计划顺利进行。

## 具体实施步骤

### 第一周：环境搭建和数据库优化
**目标**: 完成开发环境搭建和数据库设计优化

#### Day 1-2: 开发环境搭建
- [ ] 配置Go开发环境 (Go 1.23.9)
- [ ] 安装MySQL 8.0并配置
- [ ] 搭建前端开发环境 (Node.js + Vue3 + Vite)
- [ ] 配置代码版本控制 (Git)

#### Day 3-4: 数据库设计优化
- [ ] 分析现有database_simple.sql
- [ ] 优化分区策略和索引设计
- [ ] 创建测试数据脚本
- [ ] 数据库性能测试

#### Day 5-7: 后端框架搭建
- [ ] 创建Go Fiber项目结构
- [ ] 配置GORM和数据库连接
- [ ] 实现基础中间件 (CORS、日志、错误处理)
- [ ] 创建基础模型和仓储层

### 第二周：核心API开发
**目标**: 完成数据收集和基础查询API

#### Day 8-10: 数据收集API
- [ ] 实现数据接收接口 `/api/v1/track`
- [ ] 数据验证和清洗逻辑
- [ ] IP地址解析和地理位置获取
- [ ] User Agent解析

#### Day 11-14: 基础统计API
- [ ] 实现概况数据查询接口
- [ ] 实现趋势分析接口
- [ ] 实现实时统计接口
- [ ] API文档编写和测试

### 第三周：JS统计代码开发
**目标**: 完成前端数据收集SDK

#### Day 15-17: JS SDK核心功能
- [ ] 异步加载机制实现
- [ ] 页面访问数据收集
- [ ] 用户行为跟踪 (点击、滚动)
- [ ] 访客识别和会话管理

#### Day 18-21: JS SDK优化
- [ ] 防重复提交机制
- [ ] 错误处理和重试机制
- [ ] 性能优化和压缩
- [ ] 多种集成方式支持

### 第四周：用户认证系统
**目标**: 完成用户管理和权限控制

#### Day 22-24: 用户认证后端
- [ ] 用户注册/登录API
- [ ] JWT认证中间件
- [ ] 权限控制系统
- [ ] 密码加密和安全策略

#### Day 25-28: 网站管理功能
- [ ] 网站添加/编辑/删除API
- [ ] 统计代码生成逻辑
- [ ] 网站权限验证
- [ ] 数据隔离机制

### 第五-六周：前端基础框架
**目标**: 完成前端项目搭建和基础组件

#### 用户前端项目 (端口2002)
- [ ] Vue3 + TypeScript项目初始化
- [ ] Element Plus集成和主题配置
- [ ] 路由配置和页面结构
- [ ] 状态管理 (Pinia) 配置
- [ ] 基础组件开发 (图表、表格、筛选器)

#### 管理后台项目 (端口4002)
- [ ] 管理后台项目初始化
- [ ] 用户认证页面
- [ ] 主布局和导航菜单
- [ ] 权限控制组件

### 第七-八周：统计功能实现
**目标**: 完成核心统计分析功能

#### 基础统计功能
- [ ] 网站概况页面
- [ ] 实时数据展示
- [ ] 趋势图表组件
- [ ] 数据对比功能

#### 高级分析功能
- [ ] 来源分析页面
- [ ] 搜索词分析
- [ ] 地理位置分析
- [ ] 访客行为分析

### 第九-十周：完整页面开发
**目标**: 完成所有功能页面

#### 用户前端页面
- [ ] 用户首页和网站列表
- [ ] 流量分析模块 (趋势、对比、在线、明细)
- [ ] 来源分析模块 (来源、搜索、关键词、域名)
- [ ] 受访分析模块 (域名、页面、升降榜)
- [ ] 访客分析模块 (地区、环境、新老访客、忠诚度)
- [ ] 设置模块 (站点资料、代码获取、排除设置)

#### 管理后台页面
- [ ] 用户管理界面
- [ ] 网站管理界面
- [ ] 系统配置界面
- [ ] 数据监控界面

### 第十一周：性能优化
**目标**: 系统性能优化和稳定性提升

#### 后端优化
- [ ] 数据库查询优化
- [ ] 缓存策略实施 (Redis)
- [ ] 批量处理优化
- [ ] API响应时间优化

#### 前端优化
- [ ] 代码分割和懒加载
- [ ] 图片和资源优化
- [ ] 首屏加载优化
- [ ] 移动端适配

### 第十二周：测试和部署
**目标**: 完成测试和生产环境部署

#### 测试阶段
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能压力测试
- [ ] 用户体验测试

#### 部署准备
- [ ] Docker容器化配置
- [ ] Nginx配置
- [ ] SSL证书配置
- [ ] 监控和日志系统配置

## 技术难点和解决方案

### 1. 大数据量处理
**挑战**: 每日可能产生百万级访问记录
**解决方案**:
- 分区表按月存储，提高查询效率
- 预聚合表存储日级、小时级统计数据
- 异步批量处理，避免实时计算压力
- 数据归档策略，定期清理历史数据

### 2. 实时性要求
**挑战**: 用户需要看到实时的访问数据
**解决方案**:
- Redis缓存热点数据，5分钟更新一次
- WebSocket推送实时在线人数
- 前端定时刷新机制
- 数据库读写分离，查询不影响写入

### 3. 跨域和安全性
**挑战**: JS统计代码需要跨域发送数据
**解决方案**:
- CORS配置允许跨域请求
- JSONP支持老版本浏览器
- 数据加密传输 (HTTPS)
- 防刷机制和频率限制

### 4. 数据准确性
**挑战**: 确保统计数据的准确性和一致性
**解决方案**:
- 访客去重算法 (基于IP+UA+时间窗口)
- 数据验证和异常检测
- 定时数据一致性检查
- 错误数据修复机制

## 注意事项

1. **删除暂不做的页面**: 根据需求文档，seo推荐(第9项)和热点图(第16项)标记为暂不做，已在规划中排除
2. **数据库可重建**: 如发现database_simple.sql不完美，可以根据实际需求重建或修改
3. **性能优先**: 考虑到大数据量查询，数据库设计已采用分区表、预聚合等优化策略
4. **用户体验**: 前端设计遵循现代化UI/UX标准，响应式设计支持多设备访问
5. **安全考虑**: 所有API接口都需要进行安全验证，防止数据泄露和恶意攻击
6. **扩展性**: 系统架构设计考虑未来功能扩展，采用模块化设计

## 项目成功标准

1. **功能完整性**: 实现需求文档中除暂不做外的所有功能
2. **性能指标**: API响应时间 < 200ms，页面加载时间 < 3s
3. **数据准确性**: 统计数据误差 < 1%
4. **系统稳定性**: 99.9%可用性，支持并发1000+用户
5. **用户体验**: 界面友好，操作简单，响应式设计

这个完整的规划涵盖了从需求分析到最终部署的全过程，确保项目能够按时高质量交付。
