(function() {
    'use strict';

    // 获取脚本标签和配置
    var script = document.currentScript || (function() {
        var scripts = document.getElementsByTagName('script');
        return scripts[scripts.length - 1];
    })();

    var siteId = script.getAttribute('data-site-id');
    if (!siteId) {
        console.warn('Web Stats: Missing data-site-id attribute');
        return;
    }

    // 配置
    var config = {
        apiUrl: 'http://localhost:9090/api/collect',
        siteId: siteId,
        sessionTimeout: 30 * 60 * 1000, // 30分钟
        heartbeatInterval: 30 * 1000 // 30秒
    };

    // 工具函数
    var utils = {
        // 生成UUID
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0;
                var v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },

        // 获取或创建访客ID
        getVisitorId: function() {
            var key = 'ws_visitor_id';
            var visitorId = localStorage.getItem(key);
            if (!visitorId) {
                visitorId = this.generateUUID();
                localStorage.setItem(key, visitorId);
            }
            return visitorId;
        },

        // 获取或创建会话ID
        getSessionId: function() {
            var key = 'ws_session_id';
            var sessionKey = 'ws_session_time';
            var now = Date.now();
            var sessionTime = parseInt(sessionStorage.getItem(sessionKey) || '0');
            
            // 检查会话是否过期
            if (now - sessionTime > config.sessionTimeout) {
                sessionStorage.removeItem(key);
            }

            var sessionId = sessionStorage.getItem(key);
            if (!sessionId) {
                sessionId = this.generateUUID();
                sessionStorage.setItem(key, sessionId);
            }
            
            sessionStorage.setItem(sessionKey, now.toString());
            return sessionId;
        },

        // 获取页面信息
        getPageInfo: function() {
            return {
                url: window.location.href,
                title: document.title || '',
                referrer: document.referrer || ''
            };
        },

        // 获取屏幕信息
        getScreenInfo: function() {
            return {
                screenWidth: screen.width || 0,
                screenHeight: screen.height || 0,
                viewportWidth: window.innerWidth || document.documentElement.clientWidth || 0,
                viewportHeight: window.innerHeight || document.documentElement.clientHeight || 0
            };
        },

        // 获取浏览器信息
        getBrowserInfo: function() {
            return {
                language: navigator.language || navigator.userLanguage || '',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || '',
                userAgent: navigator.userAgent || ''
            };
        },

        // 发送数据
        sendData: function(data) {
            // 使用fetch API
            if (typeof fetch !== 'undefined') {
                fetch(config.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                }).catch(function(error) {
                    console.warn('Web Stats: Failed to send data', error);
                });
            } else {
                // 降级到XMLHttpRequest
                var xhr = new XMLHttpRequest();
                xhr.open('POST', config.apiUrl, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(data));
            }
        }
    };

    // 统计跟踪器
    var tracker = {
        visitorId: utils.getVisitorId(),
        sessionId: utils.getSessionId(),
        startTime: Date.now(),
        lastActivity: Date.now(),

        // 初始化
        init: function() {
            this.trackPageView();
            this.bindEvents();
            this.startHeartbeat();
        },

        // 跟踪页面浏览
        trackPageView: function() {
            var pageInfo = utils.getPageInfo();
            var screenInfo = utils.getScreenInfo();
            var browserInfo = utils.getBrowserInfo();

            var data = {
                site_id: config.siteId,
                visitor_id: this.visitorId,
                session_id: this.sessionId,
                page_url: pageInfo.url,
                page_title: pageInfo.title,
                referrer_url: pageInfo.referrer,
                screen_width: screenInfo.screenWidth,
                screen_height: screenInfo.screenHeight,
                viewport_width: screenInfo.viewportWidth,
                viewport_height: screenInfo.viewportHeight,
                language: browserInfo.language,
                timezone: browserInfo.timezone,
                user_agent: browserInfo.userAgent
            };

            utils.sendData(data);
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 页面可见性变化
            if (typeof document.hidden !== 'undefined') {
                document.addEventListener('visibilitychange', function() {
                    if (!document.hidden) {
                        self.lastActivity = Date.now();
                    }
                });
            }

            // 用户活动
            var events = ['click', 'scroll', 'keypress', 'mousemove'];
            events.forEach(function(event) {
                document.addEventListener(event, function() {
                    self.lastActivity = Date.now();
                }, { passive: true });
            });

            // 页面卸载
            window.addEventListener('beforeunload', function() {
                self.trackSession();
            });

            // 页面隐藏（移动端）
            window.addEventListener('pagehide', function() {
                self.trackSession();
            });
        },

        // 开始心跳
        startHeartbeat: function() {
            var self = this;
            setInterval(function() {
                // 检查用户是否活跃
                var now = Date.now();
                if (now - self.lastActivity < config.heartbeatInterval * 2) {
                    self.trackHeartbeat();
                }
            }, config.heartbeatInterval);
        },

        // 跟踪心跳
        trackHeartbeat: function() {
            // 这里可以发送心跳数据，用于计算在线时间
            // 为了减少服务器负载，这里暂时不实现
        },

        // 跟踪会话结束
        trackSession: function() {
            var sessionDuration = Date.now() - this.startTime;
            // 这里可以发送会话结束数据
            // 实际的会话统计在服务器端处理
        }
    };

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            tracker.init();
        });
    } else {
        tracker.init();
    }

    // 导出到全局（用于调试）
    if (typeof window !== 'undefined') {
        window.WebStats = {
            tracker: tracker,
            utils: utils,
            config: config
        };
    }

})();
