package handlers

import (
	"errors"
	"strconv"
	"time"

	"web-stats-backend/internal/database"
	"web-stats-backend/internal/middleware"
	"web-stats-backend/internal/models"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// AnalyticsHandler 统计数据处理器
type AnalyticsHandler struct{}

// NewAnalyticsHandler 创建统计数据处理器
func NewAnalyticsHandler() *AnalyticsHandler {
	return &AnalyticsHandler{}
}

// GetSiteStats 获取网站统计数据
func (h *AnalyticsHandler) GetSiteStats(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.<PERSON>("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 获取日期范围参数
	startDate := c.Query("start_date", time.Now().AddDate(0, 0, -30).Format("2006-01-02"))
	endDate := c.Query("end_date", time.Now().Format("2006-01-02"))

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid start_date format, use YYYY-MM-DD",
		})
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid end_date format, use YYYY-MM-DD",
		})
	}

	// 确保结束日期包含整天
	end = end.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	// 计算基础统计数据
	stats, err := h.calculateBasicStats(uint(siteID), start, end)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to calculate statistics",
		})
	}

	return c.JSON(fiber.Map{
		"site_id":    siteID,
		"start_date": startDate,
		"end_date":   endDate,
		"stats":      stats,
	})
}

// GetDailyStats 获取每日统计数据
func (h *AnalyticsHandler) GetDailyStats(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 获取日期范围参数
	startDate := c.Query("start_date", time.Now().AddDate(0, 0, -30).Format("2006-01-02"))
	endDate := c.Query("end_date", time.Now().Format("2006-01-02"))

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid start_date format, use YYYY-MM-DD",
		})
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid end_date format, use YYYY-MM-DD",
		})
	}

	// 获取每日统计数据
	dailyStats, err := h.getDailyStats(uint(siteID), start, end)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to get daily statistics",
		})
	}

	return c.JSON(fiber.Map{
		"site_id":     siteID,
		"start_date":  startDate,
		"end_date":    endDate,
		"daily_stats": dailyStats,
	})
}

// calculateBasicStats 计算基础统计数据
func (h *AnalyticsHandler) calculateBasicStats(siteID uint, start, end time.Time) (*models.SiteStatsResponse, error) {
	var stats models.SiteStatsResponse

	// PV (Page Views) - 总页面浏览量
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ?", siteID, start, end).
		Count(&stats.PV).Error; err != nil {
		return nil, err
	}

	// UV (Unique Visitors) - 独立访客数
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ?", siteID, start, end).
		Distinct("visitor_id").
		Count(&stats.UV).Error; err != nil {
		return nil, err
	}

	// IP Count - 独立IP数
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ?", siteID, start, end).
		Distinct("ip_address").
		Count(&stats.IPCount).Error; err != nil {
		return nil, err
	}

	// New Visitors - 新访客数
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ? AND is_new_visitor = ?", siteID, start, end, true).
		Distinct("visitor_id").
		Count(&stats.NewVisitors).Error; err != nil {
		return nil, err
	}

	// Returning Visitors - 回访访客数
	stats.ReturnVisitors = stats.UV - stats.NewVisitors

	// Bounce Rate - 跳出率
	var totalSessions int64
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ?", siteID, start, end).
		Distinct("session_id").
		Count(&totalSessions).Error; err != nil {
		return nil, err
	}

	var bounceSessions int64
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ? AND is_bounce = ?", siteID, start, end, true).
		Distinct("session_id").
		Count(&bounceSessions).Error; err != nil {
		return nil, err
	}

	if totalSessions > 0 {
		stats.BounceRate = float64(bounceSessions) / float64(totalSessions)
	}

	// Average Duration - 平均停留时间
	var avgDuration *float64
	if err := database.DB.Model(&models.SiteAnalytics{}).
		Where("site_id = ? AND visit_date BETWEEN ? AND ? AND session_duration > 0", siteID, start, end).
		Select("AVG(session_duration)").
		Scan(&avgDuration).Error; err != nil {
		return nil, err
	}
	if avgDuration != nil {
		stats.AvgDuration = int(*avgDuration)
	} else {
		stats.AvgDuration = 0
	}

	return &stats, nil
}

// getDailyStats 获取每日统计数据
func (h *AnalyticsHandler) getDailyStats(siteID uint, start, end time.Time) ([]models.DailyStats, error) {
	var dailyStats []models.DailyStats

	// 使用SQL查询获取每日统计
	query := `
		SELECT
			DATE(visit_date) as date,
			COUNT(*) as pv,
			COUNT(DISTINCT visitor_id) as uv,
			COUNT(DISTINCT ip_address) as ip_count,
			COUNT(DISTINCT CASE WHEN is_new_visitor = 1 THEN visitor_id END) as new_visitors,
			COUNT(DISTINCT CASE WHEN is_new_visitor = 0 THEN visitor_id END) as returning_visitors,
			COALESCE(AVG(CASE WHEN session_duration > 0 THEN session_duration END), 0) as avg_duration,
			COALESCE(
				COUNT(DISTINCT CASE WHEN is_bounce = 1 THEN session_id END) /
				NULLIF(COUNT(DISTINCT session_id), 0),
				0
			) as bounce_rate
		FROM site_analytics
		WHERE site_id = ? AND visit_date BETWEEN ? AND ? AND deleted_at IS NULL
		GROUP BY DATE(visit_date)
		ORDER BY date ASC
	`

	if err := database.DB.Raw(query, siteID, start, end).Scan(&dailyStats).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回空数组而不是nil
	if dailyStats == nil {
		dailyStats = []models.DailyStats{}
	}

	return dailyStats, nil
}
