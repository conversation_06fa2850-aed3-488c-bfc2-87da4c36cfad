package handlers

import (
	"errors"
	"strconv"
	"time"

	"web-stats-backend/internal/database"
	"web-stats-backend/internal/middleware"
	"web-stats-backend/internal/models"
	"web-stats-backend/internal/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SitesHandler 网站管理处理器
type SitesHandler struct{}

// NewSitesHandler 创建网站管理处理器
func NewSitesHandler() *SitesHandler {
	return &SitesHandler{}
}

// GetSites 获取用户的网站列表
func (h *SitesHandler) GetSites(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	// 获取分页参数
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 20)
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 获取搜索参数
	search := c.Query("search", "")

	// 构建查询
	query := database.DB.Where("user_id = ?", userID)
	if search != "" {
		query = query.Where("site_name LIKE ? OR domain LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	var total int64
	if err := query.Model(&models.Site{}).Count(&total).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to count sites",
		})
	}

	// 获取网站列表
	var sites []models.Site
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&sites).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to fetch sites",
		})
	}

	// 转换为响应格式
	var siteResponses []models.SiteResponse
	for _, site := range sites {
		siteResponses = append(siteResponses, site.ToResponse())
	}

	return c.JSON(fiber.Map{
		"sites": siteResponses,
		"pagination": fiber.Map{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetSite 获取单个网站信息
func (h *SitesHandler) GetSite(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	return c.JSON(fiber.Map{
		"site": site.ToResponse(),
	})
}

// CreateSite 创建新网站
func (h *SitesHandler) CreateSite(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	var req models.SiteCreateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid request body",
		})
	}

	// 验证请求数据
	if err := utils.ValidateStruct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Validation Error",
			"message": err.Error(),
		})
	}

	// 检查域名是否已存在
	var existingSite models.Site
	if err := database.DB.Where("domain = ?", req.Domain).First(&existingSite).Error; err == nil {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error":   "Conflict",
			"message": "Domain already exists",
		})
	}

	// 生成跟踪代码
	trackingCode := generateTrackingCode()

	// 创建网站
	site := models.Site{
		UserID:       userID,
		SiteName:     req.SiteName,
		Domain:       req.Domain,
		SiteURL:      req.SiteURL,
		SiteType:     req.SiteType,
		Region:       req.Region,
		Description:  req.Description,
		TrackingCode: trackingCode,
		ViewPassword: req.ViewPassword,
		IsPublic:     req.IsPublic,
		Status:       1,
	}

	if err := database.DB.Create(&site).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to create site",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Site created successfully",
		"site":    site.ToResponse(),
	})
}

// UpdateSite 更新网站信息
func (h *SitesHandler) UpdateSite(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	var req models.SiteUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid request body",
		})
	}

	// 验证请求数据
	if err := utils.ValidateStruct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Validation Error",
			"message": err.Error(),
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 如果更新域名，检查是否与其他网站冲突
	if req.Domain != "" && req.Domain != site.Domain {
		var existingSite models.Site
		if err := database.DB.Where("domain = ? AND id != ?", req.Domain, siteID).First(&existingSite).Error; err == nil {
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"error":   "Conflict",
				"message": "Domain already exists",
			})
		}
	}

	// 构建更新数据
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if req.SiteName != "" {
		updates["site_name"] = req.SiteName
	}
	if req.Domain != "" {
		updates["domain"] = req.Domain
	}
	if req.SiteURL != "" {
		updates["site_url"] = req.SiteURL
	}
	if req.SiteType != "" {
		updates["site_type"] = req.SiteType
	}
	if req.Region != "" {
		updates["region"] = req.Region
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.ViewPassword != "" {
		updates["view_password"] = req.ViewPassword
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	// 更新网站信息
	if err := database.DB.Model(&site).Updates(updates).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to update site",
		})
	}

	// 获取更新后的网站信息
	if err := database.DB.Where("id = ?", siteID).First(&site).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to fetch updated site",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Site updated successfully",
		"site":    site.ToResponse(),
	})
}

// DeleteSite 删除网站
func (h *SitesHandler) DeleteSite(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 软删除网站（GORM会自动设置deleted_at字段）
	if err := database.DB.Delete(&site).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to delete site",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Site deleted successfully",
	})
}

// RegenerateTrackingCode 重新生成跟踪代码
func (h *SitesHandler) RegenerateTrackingCode(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 生成新的跟踪代码
	newTrackingCode := generateTrackingCode()

	// 更新跟踪代码
	if err := database.DB.Model(&site).Updates(map[string]interface{}{
		"tracking_code": newTrackingCode,
		"updated_at":    time.Now(),
	}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to regenerate tracking code",
		})
	}

	return c.JSON(fiber.Map{
		"message":       "Tracking code regenerated successfully",
		"tracking_code": newTrackingCode,
	})
}

// GetTrackingCode 获取网站跟踪代码
func (h *SitesHandler) GetTrackingCode(c *fiber.Ctx) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error":   "Unauthorized",
			"message": "User not authenticated",
		})
	}

	siteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid site ID",
		})
	}

	// 检查网站是否存在且属于当前用户
	var site models.Site
	if err := database.DB.Where("id = ? AND user_id = ?", siteID, userID).First(&site).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error":   "Not Found",
				"message": "Site not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Database error",
		})
	}

	// 生成JavaScript跟踪代码
	trackingScript := generateTrackingScript(site.TrackingCode)

	return c.JSON(fiber.Map{
		"site_id":        site.ID,
		"site_name":      site.SiteName,
		"tracking_code":  site.TrackingCode,
		"tracking_script": trackingScript,
	})
}

// generateTrackingCode 生成唯一的跟踪代码
func generateTrackingCode() string {
	return uuid.New().String()[:8] + uuid.New().String()[:8]
}

// generateTrackingScript 生成JavaScript跟踪脚本
func generateTrackingScript(trackingCode string) string {
	return `<!-- 网站统计代码 -->
<script>
(function() {
  var script = document.createElement('script');
  script.src = 'http://localhost:9090/js/stats.js';
  script.async = true;
  script.setAttribute('data-site-id', '` + trackingCode + `');
  var firstScript = document.getElementsByTagName('script')[0];
  firstScript.parentNode.insertBefore(script, firstScript);
})();
</script>
<!-- 网站统计代码结束 -->`
}
