package handlers

import (
	"net"
	"net/url"
	"strings"
	"time"

	"web-stats-backend/internal/database"
	"web-stats-backend/internal/models"
	"web-stats-backend/internal/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// CollectHandler 数据收集处理器
type CollectHandler struct{}

// NewCollectHandler 创建数据收集处理器
func NewCollectHandler() *CollectHandler {
	return &CollectHandler{}
}

// CollectData 收集访问数据
func (h *CollectHandler) CollectData(c *fiber.Ctx) error {
	var req models.AnalyticsCreateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Bad Request",
			"message": "Invalid request body",
		})
	}

	// 验证请求数据
	if err := utils.ValidateStruct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "Validation Error",
			"message": err.Error(),
		})
	}

	// 根据跟踪代码查找网站
	var site models.Site
	if err := database.DB.Where("tracking_code = ? AND status = 1", req.SiteID).First(&site).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error":   "Not Found",
			"message": "Invalid tracking code",
		})
	}

	// 获取客户端IP
	clientIP := h.getClientIP(c)

	// 解析User-Agent
	userAgent := c.Get("User-Agent")
	browserInfo := h.parseUserAgent(userAgent)

	// 解析来源URL
	referrerInfo := h.parseReferrer(req.ReferrerURL)

	// 解析地理位置（简化版本，实际应用中可以使用GeoIP库）
	locationInfo := h.parseLocation(clientIP)

	// 检查是否为新访客
	isNewVisitor := h.checkNewVisitor(req.VisitorID, site.ID)

	// 创建统计记录
	analytics := models.SiteAnalytics{
		SiteID:          site.ID,
		VisitorID:       req.VisitorID,
		SessionID:       req.SessionID,
		IPAddress:       clientIP,
		UserAgent:       userAgent,
		PageURL:         req.PageURL,
		PageTitle:       req.PageTitle,
		ReferrerURL:     req.ReferrerURL,
		ReferrerDomain:  referrerInfo.Domain,
		SearchEngine:    referrerInfo.SearchEngine,
		SearchKeywords:  req.SearchKeywords,
		Country:         locationInfo.Country,
		Region:          locationInfo.Region,
		City:            locationInfo.City,
		Browser:         browserInfo.Browser,
		BrowserVersion:  browserInfo.Version,
		OS:              browserInfo.OS,
		OSVersion:       browserInfo.OSVersion,
		Device:          browserInfo.Device,
		DeviceType:      browserInfo.DeviceType,
		ScreenWidth:     req.ScreenWidth,
		ScreenHeight:    req.ScreenHeight,
		ViewportWidth:   req.ViewportWidth,
		ViewportHeight:  req.ViewportHeight,
		Language:        req.Language,
		Timezone:        req.Timezone,
		IsNewVisitor:    isNewVisitor,
		SessionDuration: 0, // 初始为0，后续更新
		PageViews:       1,
		IsBounce:        false, // 初始为false，后续根据会话行为更新
		VisitDate:       time.Now(),
	}

	// 保存统计数据
	if err := database.DB.Create(&analytics).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Internal Server Error",
			"message": "Failed to save analytics data",
		})
	}

	// 更新会话信息
	go h.updateSessionInfo(req.SessionID, site.ID)

	return c.JSON(fiber.Map{
		"status":  "success",
		"message": "Data collected successfully",
	})
}

// getClientIP 获取客户端真实IP
func (h *CollectHandler) getClientIP(c *fiber.Ctx) string {
	// 检查X-Forwarded-For头
	if xff := c.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// 检查X-Real-IP头
	if xri := c.Get("X-Real-IP"); xri != "" {
		if net.ParseIP(xri) != nil {
			return xri
		}
	}

	// 使用连接IP
	return c.IP()
}

// BrowserInfo 浏览器信息
type BrowserInfo struct {
	Browser    string
	Version    string
	OS         string
	OSVersion  string
	Device     string
	DeviceType string
}

// parseUserAgent 解析User-Agent（简化版本）
func (h *CollectHandler) parseUserAgent(userAgent string) BrowserInfo {
	info := BrowserInfo{
		Browser:    "Unknown",
		Version:    "",
		OS:         "Unknown",
		OSVersion:  "",
		Device:     "Unknown",
		DeviceType: "desktop",
	}

	ua := strings.ToLower(userAgent)

	// 检测浏览器
	if strings.Contains(ua, "chrome") && !strings.Contains(ua, "edg") {
		info.Browser = "Chrome"
	} else if strings.Contains(ua, "firefox") {
		info.Browser = "Firefox"
	} else if strings.Contains(ua, "safari") && !strings.Contains(ua, "chrome") {
		info.Browser = "Safari"
	} else if strings.Contains(ua, "edg") {
		info.Browser = "Edge"
	} else if strings.Contains(ua, "opera") {
		info.Browser = "Opera"
	}

	// 检测操作系统
	if strings.Contains(ua, "windows") {
		info.OS = "Windows"
	} else if strings.Contains(ua, "macintosh") || strings.Contains(ua, "mac os") {
		info.OS = "macOS"
	} else if strings.Contains(ua, "linux") {
		info.OS = "Linux"
	} else if strings.Contains(ua, "android") {
		info.OS = "Android"
		info.DeviceType = "mobile"
	} else if strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		info.OS = "iOS"
		if strings.Contains(ua, "ipad") {
			info.DeviceType = "tablet"
		} else {
			info.DeviceType = "mobile"
		}
	}

	// 检测设备类型
	if strings.Contains(ua, "mobile") && info.DeviceType == "desktop" {
		info.DeviceType = "mobile"
	} else if strings.Contains(ua, "tablet") {
		info.DeviceType = "tablet"
	}

	return info
}

// ReferrerInfo 来源信息
type ReferrerInfo struct {
	Domain       string
	SearchEngine string
}

// parseReferrer 解析来源URL
func (h *CollectHandler) parseReferrer(referrerURL string) ReferrerInfo {
	info := ReferrerInfo{}

	if referrerURL == "" {
		return info
	}

	parsedURL, err := url.Parse(referrerURL)
	if err != nil {
		return info
	}

	info.Domain = parsedURL.Host

	// 检测搜索引擎
	domain := strings.ToLower(parsedURL.Host)
	if strings.Contains(domain, "google") {
		info.SearchEngine = "Google"
	} else if strings.Contains(domain, "baidu") {
		info.SearchEngine = "Baidu"
	} else if strings.Contains(domain, "bing") {
		info.SearchEngine = "Bing"
	} else if strings.Contains(domain, "yahoo") {
		info.SearchEngine = "Yahoo"
	} else if strings.Contains(domain, "duckduckgo") {
		info.SearchEngine = "DuckDuckGo"
	}

	return info
}

// LocationInfo 地理位置信息
type LocationInfo struct {
	Country string
	Region  string
	City    string
}

// parseLocation 解析地理位置（简化版本）
func (h *CollectHandler) parseLocation(ip string) LocationInfo {
	// 这里是简化版本，实际应用中应该使用GeoIP数据库
	// 如MaxMind GeoLite2或其他地理位置服务
	return LocationInfo{
		Country: "Unknown",
		Region:  "Unknown",
		City:    "Unknown",
	}
}

// checkNewVisitor 检查是否为新访客
func (h *CollectHandler) checkNewVisitor(visitorID string, siteID uint) bool {
	var count int64
	database.DB.Model(&models.SiteAnalytics{}).
		Where("visitor_id = ? AND site_id = ?", visitorID, siteID).
		Count(&count)
	return count == 0
}

// updateSessionInfo 更新会话信息
func (h *CollectHandler) updateSessionInfo(sessionID string, siteID uint) {
	// 计算会话页面浏览量
	var pageViews int64
	database.DB.Model(&models.SiteAnalytics{}).
		Where("session_id = ? AND site_id = ?", sessionID, siteID).
		Count(&pageViews)

	// 获取会话的第一次和最后一次访问时间
	var firstVisit, lastVisit time.Time
	database.DB.Model(&models.SiteAnalytics{}).
		Where("session_id = ? AND site_id = ?", sessionID, siteID).
		Select("MIN(visit_date)").
		Scan(&firstVisit)

	database.DB.Model(&models.SiteAnalytics{}).
		Where("session_id = ? AND site_id = ?", sessionID, siteID).
		Select("MAX(visit_date)").
		Scan(&lastVisit)

	// 计算会话持续时间（秒）
	sessionDuration := int(lastVisit.Sub(firstVisit).Seconds())

	// 判断是否为跳出（只有一个页面浏览且停留时间很短）
	isBounce := pageViews == 1 && sessionDuration < 30

	// 更新会话信息
	database.DB.Model(&models.SiteAnalytics{}).
		Where("session_id = ? AND site_id = ?", sessionID, siteID).
		Updates(map[string]interface{}{
			"session_duration": sessionDuration,
			"page_views":       pageViews,
			"is_bounce":        isBounce,
		})
}

// GenerateVisitorID 生成访客ID
func (h *CollectHandler) GenerateVisitorID(c *fiber.Ctx) error {
	visitorID := uuid.New().String()
	return c.JSON(fiber.Map{
		"visitor_id": visitorID,
	})
}

// GenerateSessionID 生成会话ID
func (h *CollectHandler) GenerateSessionID(c *fiber.Ctx) error {
	sessionID := uuid.New().String()
	return c.JSON(fiber.Map{
		"session_id": sessionID,
	})
}
