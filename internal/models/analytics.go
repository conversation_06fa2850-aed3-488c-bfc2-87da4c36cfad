package models

import (
	"time"

	"gorm.io/gorm"
)

// SiteAnalytics 网站统计数据模型
type SiteAnalytics struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	SiteID          uint           `json:"site_id" gorm:"not null;index"`
	VisitorID       string         `json:"visitor_id" gorm:"size:32;not null;index"`
	SessionID       string         `json:"session_id" gorm:"size:32;not null;index"`
	IPAddress       string         `json:"ip_address" gorm:"size:45;not null;index"`
	UserAgent       string         `json:"user_agent" gorm:"type:text"`
	PageURL         string         `json:"page_url" gorm:"size:500;not null;index"`
	PageTitle       string         `json:"page_title" gorm:"size:200"`
	ReferrerURL     string         `json:"referrer_url" gorm:"size:500;index"`
	ReferrerDomain  string         `json:"referrer_domain" gorm:"size:100;index"`
	SearchEngine    string         `json:"search_engine" gorm:"size:50;index"`
	SearchKeywords  string         `json:"search_keywords" gorm:"size:200;index"`
	Country         string         `json:"country" gorm:"size:50;index"`
	Region          string         `json:"region" gorm:"size:50;index"`
	City            string         `json:"city" gorm:"size:50;index"`
	Browser         string         `json:"browser" gorm:"size:50;index"`
	BrowserVersion  string         `json:"browser_version" gorm:"size:20"`
	OS              string         `json:"os" gorm:"size:50;index"`
	OSVersion       string         `json:"os_version" gorm:"size:20"`
	Device          string         `json:"device" gorm:"size:50;index"`
	DeviceType      string         `json:"device_type" gorm:"size:20;index"`
	ScreenWidth     int            `json:"screen_width"`
	ScreenHeight    int            `json:"screen_height"`
	ViewportWidth   int            `json:"viewport_width"`
	ViewportHeight  int            `json:"viewport_height"`
	Language        string         `json:"language" gorm:"size:10;index"`
	Timezone        string         `json:"timezone" gorm:"size:50"`
	IsNewVisitor    bool           `json:"is_new_visitor" gorm:"index"`
	SessionDuration int            `json:"session_duration" gorm:"default:0"`
	PageViews       int            `json:"page_views" gorm:"default:1"`
	IsBounce        bool           `json:"is_bounce" gorm:"default:false;index"`
	VisitDate       time.Time      `json:"visit_date" gorm:"index"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Site Site `json:"site,omitempty" gorm:"foreignKey:SiteID"`
}

// AnalyticsCreateRequest 统计数据创建请求
type AnalyticsCreateRequest struct {
	SiteID         string `json:"site_id" validate:"required"`
	VisitorID      string `json:"visitor_id" validate:"required"`
	SessionID      string `json:"session_id" validate:"required"`
	PageURL        string `json:"page_url" validate:"required,url"`
	PageTitle      string `json:"page_title" validate:"max=200"`
	ReferrerURL    string `json:"referrer_url" validate:"url"`
	SearchKeywords string `json:"search_keywords" validate:"max=200"`
	UserAgent      string `json:"user_agent"`
	Language       string `json:"language" validate:"max=10"`
	Timezone       string `json:"timezone" validate:"max=50"`
	ScreenWidth    int    `json:"screen_width"`
	ScreenHeight   int    `json:"screen_height"`
	ViewportWidth  int    `json:"viewport_width"`
	ViewportHeight int    `json:"viewport_height"`
}

// DailyStats 日统计数据
type DailyStats struct {
	Date            time.Time `json:"date"`
	PV              int64     `json:"pv"`
	UV              int64     `json:"uv"`
	IPCount         int64     `json:"ip_count"`
	NewVisitors     int64     `json:"new_visitors"`
	ReturningVisitors int64   `json:"returning_visitors"`
	BounceRate      float64   `json:"bounce_rate"`
	AvgDuration     float64   `json:"avg_duration"`
}

// PageStats 页面统计数据
type PageStats struct {
	PageURL     string  `json:"page_url"`
	PageTitle   string  `json:"page_title"`
	PV          int64   `json:"pv"`
	UV          int64   `json:"uv"`
	BounceRate  float64 `json:"bounce_rate"`
	AvgDuration float64 `json:"avg_duration"`
}

// ReferrerStats 来源统计数据
type ReferrerStats struct {
	ReferrerDomain string `json:"referrer_domain"`
	ReferrerURL    string `json:"referrer_url"`
	Visits         int64  `json:"visits"`
	Percentage     float64 `json:"percentage"`
}

// SearchStats 搜索统计数据
type SearchStats struct {
	SearchEngine   string `json:"search_engine"`
	SearchKeywords string `json:"search_keywords"`
	Visits         int64  `json:"visits"`
	Percentage     float64 `json:"percentage"`
}

// DeviceStats 设备统计数据
type DeviceStats struct {
	DeviceType string  `json:"device_type"`
	Browser    string  `json:"browser"`
	OS         string  `json:"os"`
	Visits     int64   `json:"visits"`
	Percentage float64 `json:"percentage"`
}

// LocationStats 地理位置统计数据
type LocationStats struct {
	Country    string  `json:"country"`
	Region     string  `json:"region"`
	City       string  `json:"city"`
	Visits     int64   `json:"visits"`
	Percentage float64 `json:"percentage"`
}

// RealtimeStats 实时统计数据
type RealtimeStats struct {
	OnlineVisitors int64                    `json:"online_visitors"`
	TodayPV        int64                    `json:"today_pv"`
	TodayUV        int64                    `json:"today_uv"`
	RecentVisits   []RecentVisit            `json:"recent_visits"`
	TopPages       []PageStats              `json:"top_pages"`
	TopReferrers   []ReferrerStats          `json:"top_referrers"`
}

// RecentVisit 最近访问记录
type RecentVisit struct {
	VisitorID   string    `json:"visitor_id"`
	PageURL     string    `json:"page_url"`
	PageTitle   string    `json:"page_title"`
	ReferrerURL string    `json:"referrer_url"`
	Country     string    `json:"country"`
	City        string    `json:"city"`
	Browser     string    `json:"browser"`
	OS          string    `json:"os"`
	DeviceType  string    `json:"device_type"`
	VisitTime   time.Time `json:"visit_time"`
}

// TableName 指定表名
func (SiteAnalytics) TableName() string {
	return "site_analytics"
}
