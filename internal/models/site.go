package models

import (
	"time"

	"gorm.io/gorm"
)

// Site 网站模型
type Site struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UserID       uint           `json:"user_id" gorm:"not null;index"`
	SiteName     string         `json:"site_name" gorm:"size:100;not null"`
	Domain       string         `json:"domain" gorm:"size:100;not null;index"`
	SiteURL      string         `json:"site_url" gorm:"size:255;not null"`
	SiteType     string         `json:"site_type" gorm:"size:50;not null"`
	Region       string         `json:"region" gorm:"size:50"`
	Description  string         `json:"description" gorm:"type:text"`
	TrackingCode string         `json:"tracking_code" gorm:"size:32;uniqueIndex;not null"`
	ViewPassword string         `json:"view_password,omitempty" gorm:"size:255"`
	IsPublic     bool           `json:"is_public" gorm:"default:false"`
	Status       int            `json:"status" gorm:"default:1;comment:网站状态 1-正常 0-停用"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User      User            `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Analytics []SiteAnalytics `json:"analytics,omitempty" gorm:"foreignKey:SiteID"`
}

// SiteCreateRequest 网站创建请求
type SiteCreateRequest struct {
	SiteName     string `json:"site_name" validate:"required,min=2,max=100"`
	Domain       string `json:"domain" validate:"required,fqdn"`
	SiteURL      string `json:"site_url" validate:"required,url"`
	SiteType     string `json:"site_type" validate:"required,oneof=blog corporate ecommerce news community education other"`
	Region       string `json:"region" validate:"max=50"`
	Description  string `json:"description" validate:"max=1000"`
	ViewPassword string `json:"view_password" validate:"max=50"`
	IsPublic     bool   `json:"is_public"`
}

// SiteUpdateRequest 网站更新请求
type SiteUpdateRequest struct {
	SiteName     string `json:"site_name" validate:"min=2,max=100"`
	Domain       string `json:"domain" validate:"fqdn"`
	SiteURL      string `json:"site_url" validate:"url"`
	SiteType     string `json:"site_type" validate:"oneof=blog corporate ecommerce news community education other"`
	Region       string `json:"region" validate:"max=50"`
	Description  string `json:"description" validate:"max=1000"`
	ViewPassword string `json:"view_password" validate:"max=50"`
	IsPublic     *bool  `json:"is_public"`
	Status       *int   `json:"status" validate:"oneof=0 1"`
}

// SiteResponse 网站响应
type SiteResponse struct {
	ID           uint      `json:"id"`
	SiteName     string    `json:"site_name"`
	Domain       string    `json:"domain"`
	SiteURL      string    `json:"site_url"`
	SiteType     string    `json:"site_type"`
	Region       string    `json:"region"`
	Description  string    `json:"description"`
	TrackingCode string    `json:"tracking_code"`
	IsPublic     bool      `json:"is_public"`
	Status       int       `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// SiteStatsResponse 网站统计响应
type SiteStatsResponse struct {
	PV           int64   `json:"pv"`
	UV           int64   `json:"uv"`
	IPCount      int64   `json:"ip_count"`
	BounceRate   float64 `json:"bounce_rate"`
	AvgDuration  int     `json:"avg_duration"`
	NewVisitors  int64   `json:"new_visitors"`
	ReturnVisitors int64 `json:"returning_visitors"`
}

// ToResponse 转换为响应格式
func (s *Site) ToResponse() SiteResponse {
	return SiteResponse{
		ID:           s.ID,
		SiteName:     s.SiteName,
		Domain:       s.Domain,
		SiteURL:      s.SiteURL,
		SiteType:     s.SiteType,
		Region:       s.Region,
		Description:  s.Description,
		TrackingCode: s.TrackingCode,
		IsPublic:     s.IsPublic,
		Status:       s.Status,
		CreatedAt:    s.CreatedAt,
		UpdatedAt:    s.UpdatedAt,
	}
}

// TableName 指定表名
func (Site) TableName() string {
	return "sites"
}
