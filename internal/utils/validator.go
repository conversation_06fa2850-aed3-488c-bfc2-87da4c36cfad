package utils

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrors 验证错误集合
type ValidationErrors []ValidationError

func (ve ValidationErrors) Error() string {
	var messages []string
	for _, err := range ve {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return fmt.Errorf("expected struct, got %s", v.Kind())
	}

	t := v.Type()
	var errors ValidationErrors

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 获取validate标签
		validateTag := fieldType.Tag.Get("validate")
		if validateTag == "" {
			continue
		}

		// 解析验证规则
		rules := strings.Split(validateTag, ",")
		fieldName := getFieldName(fieldType)

		for _, rule := range rules {
			rule = strings.TrimSpace(rule)
			if err := validateField(fieldName, field, rule); err != nil {
				errors = append(errors, *err)
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}

	return nil
}

// getFieldName 获取字段名
func getFieldName(field reflect.StructField) string {
	jsonTag := field.Tag.Get("json")
	if jsonTag != "" && jsonTag != "-" {
		parts := strings.Split(jsonTag, ",")
		return parts[0]
	}
	return strings.ToLower(field.Name)
}

// validateField 验证字段
func validateField(fieldName string, field reflect.Value, rule string) *ValidationError {
	parts := strings.Split(rule, "=")
	ruleName := parts[0]
	var ruleValue string
	if len(parts) > 1 {
		ruleValue = parts[1]
	}

	switch ruleName {
	case "required":
		return validateRequired(fieldName, field)
	case "min":
		return validateMin(fieldName, field, ruleValue)
	case "max":
		return validateMax(fieldName, field, ruleValue)
	case "email":
		return validateEmail(fieldName, field)
	case "url":
		return validateURL(fieldName, field)
	case "fqdn":
		return validateFQDN(fieldName, field)
	case "oneof":
		return validateOneOf(fieldName, field, ruleValue)
	case "eqfield":
		// 这个需要特殊处理，暂时跳过
		return nil
	}

	return nil
}

// validateRequired 验证必填
func validateRequired(fieldName string, field reflect.Value) *ValidationError {
	switch field.Kind() {
	case reflect.String:
		if field.String() == "" {
			return &ValidationError{
				Field:   fieldName,
				Message: "This field is required",
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if field.Int() == 0 {
			return &ValidationError{
				Field:   fieldName,
				Message: "This field is required",
			}
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if field.Uint() == 0 {
			return &ValidationError{
				Field:   fieldName,
				Message: "This field is required",
			}
		}
	case reflect.Bool:
		// bool类型不需要required验证
		return nil
	case reflect.Slice, reflect.Array:
		if field.Len() == 0 {
			return &ValidationError{
				Field:   fieldName,
				Message: "This field is required",
			}
		}
	}
	return nil
}

// validateMin 验证最小值/长度
func validateMin(fieldName string, field reflect.Value, ruleValue string) *ValidationError {
	minVal := parseInt(ruleValue)
	if minVal == 0 {
		return nil
	}

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) < minVal {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("Minimum length is %d", minVal),
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if int(field.Int()) < minVal {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("Minimum value is %d", minVal),
			}
		}
	}
	return nil
}

// validateMax 验证最大值/长度
func validateMax(fieldName string, field reflect.Value, ruleValue string) *ValidationError {
	maxVal := parseInt(ruleValue)
	if maxVal == 0 {
		return nil
	}

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) > maxVal {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("Maximum length is %d", maxVal),
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if int(field.Int()) > maxVal {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("Maximum value is %d", maxVal),
			}
		}
	}
	return nil
}

// validateEmail 验证邮箱
func validateEmail(fieldName string, field reflect.Value) *ValidationError {
	if field.Kind() != reflect.String {
		return nil
	}

	email := field.String()
	if email == "" {
		return nil
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return &ValidationError{
			Field:   fieldName,
			Message: "Invalid email format",
		}
	}
	return nil
}

// validateURL 验证URL
func validateURL(fieldName string, field reflect.Value) *ValidationError {
	if field.Kind() != reflect.String {
		return nil
	}

	url := field.String()
	if url == "" {
		return nil
	}

	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(url) {
		return &ValidationError{
			Field:   fieldName,
			Message: "Invalid URL format",
		}
	}
	return nil
}

// validateFQDN 验证域名
func validateFQDN(fieldName string, field reflect.Value) *ValidationError {
	if field.Kind() != reflect.String {
		return nil
	}

	domain := field.String()
	if domain == "" {
		return nil
	}

	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`)
	if !domainRegex.MatchString(domain) {
		return &ValidationError{
			Field:   fieldName,
			Message: "Invalid domain format",
		}
	}
	return nil
}

// validateOneOf 验证枚举值
func validateOneOf(fieldName string, field reflect.Value, ruleValue string) *ValidationError {
	if field.Kind() != reflect.String {
		return nil
	}

	value := field.String()
	if value == "" {
		return nil
	}

	options := strings.Split(ruleValue, " ")
	for _, option := range options {
		if value == option {
			return nil
		}
	}

	return &ValidationError{
		Field:   fieldName,
		Message: fmt.Sprintf("Must be one of: %s", strings.Join(options, ", ")),
	}
}

// parseInt 解析整数
func parseInt(s string) int {
	var result int
	fmt.Sscanf(s, "%d", &result)
	return result
}
