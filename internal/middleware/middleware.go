package middleware

import (
	"log"
	"strings"
	"time"

	"web-stats-backend/internal/config"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/limiter"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"
)

// SetupMiddleware 设置中间件
func SetupMiddleware(app *fiber.App, cfg *config.Config) {
	// 恢复中间件 - 捕获panic
	app.Use(recover.New())

	// 请求ID中间件
	app.Use(requestid.New())

	// 日志中间件
	if cfg.IsDevelopment() {
		app.Use(logger.New(logger.Config{
			Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
		}))
	} else {
		app.Use(logger.New(logger.Config{
			Format: "${time} | ${status} | ${latency} | ${ip} | ${method} | ${path} | ${error}\n",
		}))
	}

	// CORS中间件
	app.Use(cors.New(cors.Config{
		AllowOrigins:     strings.Join(cfg.CORS.Origins, ","),
		AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders:     "Origin,Content-Type,Accept,Authorization,X-Requested-With",
		AllowCredentials: true,
		MaxAge:           86400, // 24小时
	}))

	// 限流中间件
	app.Use(limiter.New(limiter.Config{
		Max:        cfg.RateLimit.Max,
		Expiration: time.Duration(cfg.RateLimit.Window) * time.Second,
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.IP()
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(fiber.StatusTooManyRequests).JSON(fiber.Map{
				"error":   "Too Many Requests",
				"message": "Rate limit exceeded",
			})
		},
	}))

	// 安全头中间件
	app.Use(SecurityHeaders())

	// API版本中间件
	app.Use(APIVersion())
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() fiber.Handler {
	return func(c *fiber.Ctx) error {
		c.Set("X-Content-Type-Options", "nosniff")
		c.Set("X-Frame-Options", "DENY")
		c.Set("X-XSS-Protection", "1; mode=block")
		c.Set("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Set("Content-Security-Policy", "default-src 'self'")
		return c.Next()
	}
}

// APIVersion API版本中间件
func APIVersion() fiber.Handler {
	return func(c *fiber.Ctx) error {
		c.Set("API-Version", config.GlobalConfig.App.Version)
		return c.Next()
	}
}

// ErrorHandler 全局错误处理中间件
func ErrorHandler() fiber.ErrorHandler {
	return func(c *fiber.Ctx, err error) error {
		code := fiber.StatusInternalServerError
		message := "Internal Server Error"

		// 检查是否是Fiber错误
		if e, ok := err.(*fiber.Error); ok {
			code = e.Code
			message = e.Message
		}

		// 记录错误日志
		if code >= 500 {
			// 服务器错误
			log.Printf("Server Error: %v", err)
		}

		// 返回错误响应
		return c.Status(code).JSON(fiber.Map{
			"error":     true,
			"message":   message,
			"timestamp": time.Now().Unix(),
		})
	}
}

// ValidateJSON JSON验证中间件
func ValidateJSON() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if c.Method() == "POST" || c.Method() == "PUT" || c.Method() == "PATCH" {
			contentType := c.Get("Content-Type")
			if contentType != "" && !strings.Contains(contentType, "application/json") {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"error":   "Bad Request",
					"message": "Content-Type must be application/json",
				})
			}
		}
		return c.Next()
	}
}

// Pagination 分页中间件
func Pagination() fiber.Handler {
	return func(c *fiber.Ctx) error {
		page := c.QueryInt("page", 1)
		if page < 1 {
			page = 1
		}

		limit := c.QueryInt("limit", 20)
		if limit < 1 {
			limit = 20
		}
		if limit > 100 {
			limit = 100
		}

		offset := (page - 1) * limit

		c.Locals("page", page)
		c.Locals("limit", limit)
		c.Locals("offset", offset)

		return c.Next()
	}
}

// GetPagination 获取分页参数
func GetPagination(c *fiber.Ctx) (page, limit, offset int) {
	page = c.Locals("page").(int)
	limit = c.Locals("limit").(int)
	offset = c.Locals("offset").(int)
	return
}
