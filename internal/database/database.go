package database

import (
	"fmt"
	"log"
	"time"

	"web-stats-backend/internal/config"
	"web-stats-backend/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// createDatabaseIfNotExists 创建数据库（如果不存在）
func createDatabaseIfNotExists(cfg *config.Config) error {
	// 构建不包含数据库名的DSN
	dsn := cfg.Database.User + ":" + cfg.Database.Password + "@tcp(" + cfg.Database.Host + ":" + fmt.Sprintf("%d", cfg.Database.Port) + ")/?charset=utf8mb4&parseTime=True&loc=Local"

	// 连接到MySQL服务器（不指定数据库）
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to MySQL server: %w", err)
	}

	// 创建数据库
	createSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", cfg.Database.Name)
	if err := db.Exec(createSQL).Error; err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	// 关闭连接
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	log.Printf("Database '%s' created or already exists", cfg.Database.Name)
	return nil
}

// InitDatabase 初始化数据库连接
func InitDatabase(cfg *config.Config) error {
	var err error

	// 配置GORM日志级别
	logLevel := logger.Silent
	if cfg.IsDevelopment() {
		logLevel = logger.Info
	}

	// 首先尝试创建数据库
	if err := createDatabaseIfNotExists(cfg); err != nil {
		log.Printf("Warning: Failed to create database: %v", err)
	}

	// 连接数据库
	DB, err = gorm.Open(mysql.Open(cfg.Database.DSN), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	})

	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)                   // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)                  // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour)         // 连接最大生存时间

	// 测试数据库连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connected successfully")
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate() error {
	err := DB.AutoMigrate(
		&models.User{},
		&models.Site{},
		&models.SiteAnalytics{},
	)

	if err != nil {
		return fmt.Errorf("failed to auto migrate: %w", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(*gorm.DB) error) error {
	return DB.Transaction(fn)
}

// CreateIndexes 创建额外的索引
func CreateIndexes() error {
	// 为site_analytics表创建复合索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_site_date ON site_analytics(site_id, visit_date)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_visitor_session ON site_analytics(visitor_id, session_id)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_ip_date ON site_analytics(ip_address, visit_date)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_referrer_domain ON site_analytics(referrer_domain)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_search_engine ON site_analytics(search_engine)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_country_region ON site_analytics(country, region)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_browser_os ON site_analytics(browser, os)",
		"CREATE INDEX IF NOT EXISTS idx_site_analytics_device_type ON site_analytics(device_type)",
	}

	for _, indexSQL := range indexes {
		if err := DB.Exec(indexSQL).Error; err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	log.Println("Database indexes created successfully")
	return nil
}

// SeedData 初始化种子数据
func SeedData() error {
	// 检查是否已有管理员用户
	var count int64
	DB.Model(&models.User{}).Where("username = ?", "admin").Count(&count)
	
	if count == 0 {
		// 创建默认管理员用户
		adminUser := models.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Nickname: "管理员",
			Status:   1,
		}

		if err := DB.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}

		log.Println("Default admin user created (username: admin, password: password)")
	}

	return nil
}

// HealthCheck 数据库健康检查
func HealthCheck() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}
