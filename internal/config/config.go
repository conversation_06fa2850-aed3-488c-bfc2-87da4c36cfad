package config

import (
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

type Config struct {
	// 数据库配置
	Database DatabaseConfig

	// 服务器配置
	Server ServerConfig

	// JWT配置
	JWT JWTConfig

	// 应用配置
	App AppConfig

	// CORS配置
	CORS CORSConfig

	// 日志配置
	Log LogConfig

	// 文件上传配置
	Upload UploadConfig

	// 缓存配置
	Cache CacheConfig

	// 限流配置
	RateLimit RateLimitConfig
}

type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Name     string
	DSN      string
}

type ServerConfig struct {
	Host string
	Port int
}

type JWTConfig struct {
	Secret      string
	ExpireHours int
}

type AppConfig struct {
	Env     string
	Name    string
	Version string
}

type CORSConfig struct {
	Origins []string
}

type LogConfig struct {
	Level  string
	Format string
}

type UploadConfig struct {
	MaxSize int64
	Path    string
}

type CacheConfig struct {
	TTL int
}

type RateLimitConfig struct {
	Max    int
	Window int
}

var GlobalConfig *Config

// LoadConfig 加载配置
func LoadConfig() *Config {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	config := &Config{
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 3306),
			User:     getEnv("DB_USER", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Name:     getEnv("DB_NAME", "web_statistics"),
		},
		Server: ServerConfig{
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Port: getEnvAsInt("SERVER_PORT", 3000),
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "default-secret-change-this"),
			ExpireHours: getEnvAsInt("JWT_EXPIRE_HOURS", 24),
		},
		App: AppConfig{
			Env:     getEnv("APP_ENV", "development"),
			Name:    getEnv("APP_NAME", "Web Statistics Backend"),
			Version: getEnv("APP_VERSION", "1.0.0"),
		},
		CORS: CORSConfig{
			Origins: strings.Split(getEnv("CORS_ORIGINS", "http://localhost:5173"), ","),
		},
		Log: LogConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		Upload: UploadConfig{
			MaxSize: getEnvAsInt64("UPLOAD_MAX_SIZE", 10485760), // 10MB
			Path:    getEnv("UPLOAD_PATH", "./uploads"),
		},
		Cache: CacheConfig{
			TTL: getEnvAsInt("CACHE_TTL", 3600),
		},
		RateLimit: RateLimitConfig{
			Max:    getEnvAsInt("RATE_LIMIT_MAX", 100),
			Window: getEnvAsInt("RATE_LIMIT_WINDOW", 60),
		},
	}

	// 构建数据库DSN
	config.Database.DSN = buildDSN(config.Database)

	GlobalConfig = config
	return config
}

// buildDSN 构建数据库连接字符串
func buildDSN(db DatabaseConfig) string {
	return db.User + ":" + db.Password + "@tcp(" + db.Host + ":" + strconv.Itoa(db.Port) + ")/" + db.Name + "?charset=utf8mb4&parseTime=True&loc=Local"
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为int
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsInt64 获取环境变量并转换为int64
func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// IsDevelopment 判断是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.App.Env == "development"
}

// IsProduction 判断是否为生产环境
func (c *Config) IsProduction() bool {
	return c.App.Env == "production"
}

// GetServerAddress 获取服务器地址
func (c *Config) GetServerAddress() string {
	return c.Server.Host + ":" + strconv.Itoa(c.Server.Port)
}
