# 网站统计系统 Makefile

# 变量定义
BINARY_NAME=web-stats-backend
MAIN_PATH=./backend/cmd/server
BUILD_DIR=./build
GO_FILES=$(shell find . -name "*.go" -type f)

# 默认目标
.PHONY: all
all: clean build

# 构建
.PHONY: build
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@cd backend && go build -o ../$(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "Build completed: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行
.PHONY: run
run:
	@echo "Running $(BINARY_NAME)..."
	@cd backend && go run $(MAIN_PATH)

# 开发模式运行（热重载）
.PHONY: dev
dev:
	@echo "Running in development mode..."
	@cd backend && air

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	@cd backend && go test -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	@cd backend && go test -v -coverprofile=coverage.out ./...
	@cd backend && go tool cover -html=coverage.out -o coverage.html

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	@cd backend && go fmt ./...

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@cd backend && golangci-lint run

# 清理
.PHONY: clean
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)
	@cd backend && go clean

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	@cd backend && go mod download
	@cd backend && go mod tidy

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "Running database migration..."
	@mysql -u root -p web_stats < database_simple.sql

# 创建测试数据
.PHONY: seed
seed:
	@echo "Creating test data..."
	@mysql -u root -p web_stats < simple_test_data.sql

# 清理测试数据
.PHONY: clean-data
clean-data:
	@echo "Cleaning test data..."
	@mysql -u root -p web_stats < clear_test_data.sql

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(BINARY_NAME):latest .

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	@docker run -p 9002:9002 $(BINARY_NAME):latest

# 前端开发
.PHONY: frontend-user
frontend-user:
	@echo "Starting user frontend development server..."
	@cd frontend/user && npm run dev

.PHONY: frontend-admin
frontend-admin:
	@echo "Starting admin frontend development server..."
	@cd frontend/admin && npm run dev

# 安装前端依赖
.PHONY: frontend-deps
frontend-deps:
	@echo "Installing frontend dependencies..."
	@cd frontend/user && npm install
	@cd frontend/admin && npm install

# 构建前端
.PHONY: frontend-build
frontend-build:
	@echo "Building frontend..."
	@cd frontend/user && npm run build
	@cd frontend/admin && npm run build

# 帮助
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build          - Build the backend binary"
	@echo "  run            - Run the backend server"
	@echo "  dev            - Run in development mode with hot reload"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  fmt            - Format Go code"
	@echo "  lint           - Run linter"
	@echo "  clean          - Clean build artifacts"
	@echo "  deps           - Install Go dependencies"
	@echo "  migrate        - Run database migration"
	@echo "  seed           - Create test data"
	@echo "  clean-data     - Clean test data"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  frontend-user  - Start user frontend dev server"
	@echo "  frontend-admin - Start admin frontend dev server"
	@echo "  frontend-deps  - Install frontend dependencies"
	@echo "  frontend-build - Build frontend"
	@echo "  help           - Show this help message"
