-- 简化完整测试数据
-- 确保所有表都有数据，方便开发测试
USE web_stats;

-- ============================================================================
-- 1. 用户数据
-- ============================================================================
INSERT INTO users (username, email, password_hash, nickname, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXgwHTMLnqzYgbvLnLgPv6OOlhm', '管理员', 1),
('demo', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXgwHTMLnqzYgbvLnLgPv6OOlhm', '演示用户', 1),
('test', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXgwHTMLnqzYgbvLnLgPv6OOlhm', '测试用户', 1);

-- ============================================================================
-- 2. 网站数据
-- ============================================================================
INSERT INTO sites (user_id, site_name, domain, site_url, site_type, description, tracking_code, is_public, status) VALUES
(1, '技术博客', 'blog.test.com', 'https://blog.test.com', 'blog', '个人技术博客', 'TRACK001', 1, 1),
(1, '公司官网', 'company.test.com', 'https://company.test.com', 'corporate', '公司官网', 'TRACK002', 1, 1),
(2, '电商网站', 'shop.test.com', 'https://shop.test.com', 'ecommerce', '在线商城', 'TRACK003', 1, 1),
(3, '新闻网站', 'news.test.com', 'https://news.test.com', 'news', '新闻资讯', 'TRACK004', 1, 1);

-- ============================================================================
-- 3. IP地址数据
-- ============================================================================
INSERT INTO ip_addresses (ip_int, ip_string) VALUES
(INET_ATON('*************'), '*************'),
(INET_ATON('*************'), '*************'),
(INET_ATON('*********'), '*********'),
(INET_ATON('***********'), '***********'),
(INET_ATON('************'), '************'),
(INET_ATON('***************'), '***************'),
(INET_ATON('*******'), '*******'),
(INET_ATON('*******'), '*******');

-- ============================================================================
-- 4. 关键词数据
-- ============================================================================
INSERT INTO keywords (keyword, keyword_hash, total_count) VALUES
('Go语言教程', SHA2('Go语言教程', 256), 156),
('Vue3开发', SHA2('Vue3开发', 256), 134),
('React入门', SHA2('React入门', 256), 128),
('JavaScript', SHA2('JavaScript', 256), 145),
('TypeScript', SHA2('TypeScript', 256), 98),
('Node.js', SHA2('Node.js', 256), 87),
('MySQL优化', SHA2('MySQL优化', 256), 76),
('Redis缓存', SHA2('Redis缓存', 256), 65),
('Docker', SHA2('Docker', 256), 89),
('前端开发', SHA2('前端开发', 256), 112);

-- ============================================================================
-- 5. 页面URL数据
-- ============================================================================
INSERT INTO page_urls (url, url_hash, domain, path, title) VALUES
-- 技术博客页面
('https://blog.test.com/', SHA2('https://blog.test.com/', 256), 'blog.test.com', '/', '博客首页'),
('https://blog.test.com/golang', SHA2('https://blog.test.com/golang', 256), 'blog.test.com', '/golang', 'Go语言教程'),
('https://blog.test.com/vue3', SHA2('https://blog.test.com/vue3', 256), 'blog.test.com', '/vue3', 'Vue3开发指南'),
('https://blog.test.com/react', SHA2('https://blog.test.com/react', 256), 'blog.test.com', '/react', 'React入门'),
('https://blog.test.com/about', SHA2('https://blog.test.com/about', 256), 'blog.test.com', '/about', '关于我'),

-- 公司官网页面
('https://company.test.com/', SHA2('https://company.test.com/', 256), 'company.test.com', '/', '公司首页'),
('https://company.test.com/products', SHA2('https://company.test.com/products', 256), 'company.test.com', '/products', '产品介绍'),
('https://company.test.com/about', SHA2('https://company.test.com/about', 256), 'company.test.com', '/about', '关于我们'),
('https://company.test.com/contact', SHA2('https://company.test.com/contact', 256), 'company.test.com', '/contact', '联系我们'),

-- 电商网站页面
('https://shop.test.com/', SHA2('https://shop.test.com/', 256), 'shop.test.com', '/', '商城首页'),
('https://shop.test.com/electronics', SHA2('https://shop.test.com/electronics', 256), 'shop.test.com', '/electronics', '电子产品'),
('https://shop.test.com/computers', SHA2('https://shop.test.com/computers', 256), 'shop.test.com', '/computers', '电脑数码'),
('https://shop.test.com/cart', SHA2('https://shop.test.com/cart', 256), 'shop.test.com', '/cart', '购物车'),

-- 新闻网站页面
('https://news.test.com/', SHA2('https://news.test.com/', 256), 'news.test.com', '/', '新闻首页'),
('https://news.test.com/tech', SHA2('https://news.test.com/tech', 256), 'news.test.com', '/tech', '科技新闻'),
('https://news.test.com/ai', SHA2('https://news.test.com/ai', 256), 'news.test.com', '/ai', '人工智能'),
('https://news.test.com/blockchain', SHA2('https://news.test.com/blockchain', 256), 'news.test.com', '/blockchain', '区块链');

-- ============================================================================
-- 6. User Agent数据
-- ============================================================================
INSERT INTO user_agents (ua_hash, user_agent, browser_id, os_id, device_type) VALUES
(SHA2('Chrome-Windows', 256), 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/120.0.0.0', 1, 2, 'desktop'),
(SHA2('Chrome-Mac', 256), 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 Chrome/120.0.0.0', 1, 3, 'desktop'),
(SHA2('Firefox-Windows', 256), 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0', 2, 2, 'desktop'),
(SHA2('Safari-iPhone', 256), 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 Safari/604.1', 3, 6, 'mobile'),
(SHA2('Firefox-Android', 256), 'Mozilla/5.0 (Android 14; Mobile; rv:119.0) Gecko/119.0 Firefox/119.0', 2, 8, 'mobile'),
(SHA2('Safari-iPad', 256), 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 Safari/604.1', 3, 6, 'tablet');

-- ============================================================================
-- 7. 会话数据
-- ============================================================================
INSERT INTO sessions (session_hash) VALUES
('sess_001'), ('sess_002'), ('sess_003'), ('sess_004'), ('sess_005'),
('sess_006'), ('sess_007'), ('sess_008'), ('sess_009'), ('sess_010'),
('sess_011'), ('sess_012'), ('sess_013'), ('sess_014'), ('sess_015'),
('sess_016'), ('sess_017'), ('sess_018'), ('sess_019'), ('sess_020');

-- ============================================================================
-- 8. 访客数据
-- ============================================================================
INSERT INTO visitors (site_id, visitor_hash, first_visit, last_visit, total_visits, total_page_views, total_duration) VALUES
-- 技术博客访客
(1, 'visitor_blog_001', '2024-12-01 09:00:00', '2025-01-02 15:30:00', 25, 125, 18000),
(1, 'visitor_blog_002', '2024-12-02 10:15:00', '2025-01-01 14:20:00', 18, 72, 12600),
(1, 'visitor_blog_003', '2024-12-03 11:30:00', '2024-12-28 16:45:00', 12, 48, 8400),
(1, 'visitor_blog_004', '2024-12-05 14:20:00', '2024-12-30 11:10:00', 8, 32, 5600),

-- 公司官网访客
(2, 'visitor_corp_001', '2024-12-01 08:30:00', '2024-12-25 17:20:00', 6, 24, 4200),
(2, 'visitor_corp_002', '2024-12-03 13:45:00', '2024-12-20 10:15:00', 4, 16, 2800),
(2, 'visitor_corp_003', '2024-12-05 15:20:00', '2024-12-22 14:30:00', 3, 12, 2100),

-- 电商网站访客
(3, 'visitor_shop_001', '2024-12-02 10:00:00', '2025-01-01 20:30:00', 22, 110, 15400),
(3, 'visitor_shop_002', '2024-12-04 12:30:00', '2024-12-31 18:45:00', 16, 80, 11200),
(3, 'visitor_shop_003', '2024-12-06 14:15:00', '2024-12-29 16:20:00', 12, 60, 8400),

-- 新闻网站访客
(4, 'visitor_news_001', '2024-12-03 07:45:00', '2025-01-02 19:15:00', 28, 84, 8400),
(4, 'visitor_news_002', '2024-12-04 09:20:00', '2025-01-01 21:30:00', 24, 72, 7200),
(4, 'visitor_news_003', '2024-12-05 11:10:00', '2024-12-30 08:45:00', 20, 60, 6000);

-- ============================================================================
-- 9. 访问记录数据（30天数据）
-- ============================================================================
INSERT INTO visits (
    site_id, visitor_id, session_id, ip_id, user_agent_id, landing_page_id,
    visit_time, visit_date, visit_hour, visit_year, visit_month, visit_week, visit_weekday,
    visit_duration, page_views, is_new_visitor, region_id, device_type, browser_id, os_id,
    search_engine_id, search_keyword_id
) VALUES
-- 2024-12-01 数据
(1, 1, 1, 1, 1, 1, '2024-12-01 09:15:00', '2024-12-01', 9, 2024, 12, 48, 1, 320, 3, 1, 1, 'desktop', 1, 2, 1, 1),
(1, 2, 2, 2, 2, 2, '2024-12-01 10:30:00', '2024-12-01', 10, 2024, 12, 48, 1, 450, 2, 1, 2, 'desktop', 1, 3, 1, 2),
(2, 5, 3, 3, 1, 6, '2024-12-01 11:45:00', '2024-12-01', 11, 2024, 12, 48, 1, 280, 4, 1, 3, 'desktop', 1, 2, NULL, NULL),
(3, 8, 4, 4, 4, 10, '2024-12-01 14:20:00', '2024-12-01', 14, 2024, 12, 48, 1, 380, 3, 1, 4, 'mobile', 3, 6, 2, 3),

-- 2024-12-02 数据
(1, 1, 5, 1, 1, 3, '2024-12-02 08:30:00', '2024-12-02', 8, 2024, 12, 48, 2, 520, 4, 0, 1, 'desktop', 1, 2, 1, 4),
(2, 6, 6, 5, 3, 7, '2024-12-02 13:15:00', '2024-12-02', 13, 2024, 12, 48, 2, 340, 2, 1, 5, 'desktop', 2, 2, NULL, NULL),
(4, 11, 7, 6, 1, 14, '2024-12-02 16:45:00', '2024-12-02', 16, 2024, 12, 48, 2, 180, 2, 1, 6, 'desktop', 1, 2, 1, 5),
(3, 9, 8, 7, 5, 11, '2024-12-02 19:20:00', '2024-12-02', 19, 2024, 12, 48, 2, 720, 5, 1, 7, 'mobile', 2, 8, 3, 6),

-- 2024-12-03 数据
(1, 3, 9, 8, 6, 4, '2024-12-03 10:10:00', '2024-12-03', 10, 2024, 12, 48, 3, 420, 3, 1, 8, 'tablet', 3, 6, 1, 7),
(3, 10, 10, 1, 2, 12, '2024-12-03 12:30:00', '2024-12-03', 12, 2024, 12, 48, 3, 480, 4, 1, 9, 'desktop', 1, 3, 2, 8),
(4, 12, 11, 2, 1, 15, '2024-12-03 15:45:00', '2024-12-03', 15, 2024, 12, 48, 3, 360, 3, 1, 10, 'desktop', 1, 2, 1, 9),

-- 2024-12-04 数据
(1, 4, 12, 3, 1, 1, '2024-12-04 09:00:00', '2024-12-04', 9, 2024, 12, 49, 4, 300, 2, 1, 1, 'desktop', 1, 2, 1, 10),
(2, 7, 13, 4, 3, 8, '2024-12-04 11:20:00', '2024-12-04', 11, 2024, 12, 49, 4, 420, 3, 0, 2, 'desktop', 2, 2, NULL, NULL),
(4, 11, 14, 5, 4, 16, '2024-12-04 14:30:00', '2024-12-04', 14, 2024, 12, 49, 4, 240, 2, 0, 3, 'mobile', 3, 6, 1, 1),
(3, 8, 15, 6, 1, 13, '2024-12-04 17:15:00', '2024-12-04', 17, 2024, 12, 49, 4, 680, 4, 0, 4, 'desktop', 1, 2, 3, 2),

-- 2024-12-05 数据
(1, 2, 16, 7, 1, 2, '2024-12-05 08:45:00', '2024-12-05', 8, 2024, 12, 49, 5, 380, 3, 0, 5, 'desktop', 1, 2, 1, 3),
(3, 9, 17, 8, 5, 11, '2024-12-05 12:10:00', '2024-12-05', 12, 2024, 12, 49, 5, 520, 4, 0, 6, 'mobile', 2, 8, 2, 4),
(4, 13, 18, 1, 2, 14, '2024-12-05 16:30:00', '2024-12-05', 16, 2024, 12, 49, 5, 440, 3, 1, 7, 'desktop', 2, 2, 1, 5),

-- 继续添加更多天的数据...
(1, 1, 19, 2, 1, 3, '2024-12-15 10:20:00', '2024-12-15', 10, 2024, 12, 50, 1, 460, 3, 0, 8, 'desktop', 1, 2, 1, 6),
(2, 5, 20, 3, 3, 9, '2024-12-15 13:40:00', '2024-12-15', 13, 2024, 12, 50, 1, 320, 2, 0, 9, 'desktop', 2, 2, NULL, NULL),
(4, 12, 1, 4, 4, 15, '2024-12-15 16:50:00', '2024-12-15', 16, 2024, 12, 50, 1, 280, 2, 0, 10, 'mobile', 3, 6, 1, 7),

-- 2024-12-25 数据
(1, 3, 2, 5, 1, 4, '2024-12-25 09:30:00', '2024-12-25', 9, 2024, 12, 52, 4, 540, 4, 0, 1, 'desktop', 1, 2, 1, 8),
(3, 8, 3, 6, 5, 12, '2024-12-25 14:15:00', '2024-12-25', 14, 2024, 12, 52, 4, 420, 3, 0, 2, 'mobile', 2, 8, 2, 9),
(2, 6, 4, 7, 1, 7, '2024-12-25 18:45:00', '2024-12-25', 18, 2024, 12, 52, 4, 380, 2, 0, 3, 'desktop', 1, 2, NULL, NULL),

-- 2025-01-01 数据
(1, 4, 5, 8, 1, 5, '2025-01-01 11:00:00', '2025-01-01', 11, 2025, 1, 1, 4, 380, 3, 0, 4, 'desktop', 1, 2, 1, 10),
(2, 7, 6, 1, 3, 6, '2025-01-01 15:20:00', '2025-01-01', 15, 2025, 1, 1, 4, 300, 2, 0, 5, 'desktop', 2, 2, NULL, NULL),
(4, 11, 7, 2, 4, 16, '2025-01-01 19:30:00', '2025-01-01', 19, 2025, 1, 1, 4, 480, 4, 0, 6, 'mobile', 3, 6, 1, 1),

-- 2025-01-02 数据（最新）
(1, 1, 8, 3, 1, 1, '2025-01-02 10:15:00', '2025-01-02', 10, 2025, 1, 1, 5, 420, 3, 0, 7, 'desktop', 1, 2, 1, 2),
(4, 13, 9, 4, 4, 14, '2025-01-02 14:45:00', '2025-01-02', 14, 2025, 1, 1, 5, 320, 2, 0, 8, 'mobile', 3, 6, 1, 3),
(3, 10, 10, 5, 1, 13, '2025-01-02 17:30:00', '2025-01-02', 17, 2025, 1, 1, 5, 680, 4, 0, 9, 'desktop', 1, 2, 3, 4);

-- ============================================================================
-- 10. 页面浏览记录数据
-- ============================================================================
INSERT INTO page_views (site_id, visitor_id, session_id, page_url_id, view_time, view_date, view_hour, stay_duration, scroll_depth) VALUES
-- 博客页面浏览
(1, 1, 1, 1, '2024-12-01 09:15:00', '2024-12-01', 9, 120, 80),
(1, 1, 1, 2, '2024-12-01 09:18:00', '2024-12-01', 9, 180, 95),
(1, 1, 1, 3, '2024-12-01 09:22:00', '2024-12-01', 9, 150, 70),
(1, 2, 2, 2, '2024-12-01 10:30:00', '2024-12-01', 10, 200, 60),
(1, 2, 2, 1, '2024-12-01 10:35:00', '2024-12-01', 10, 250, 85),

-- 公司网站页面浏览
(2, 5, 3, 6, '2024-12-01 11:45:00', '2024-12-01', 11, 180, 85),
(2, 5, 3, 7, '2024-12-01 11:50:00', '2024-12-01', 11, 240, 90),
(2, 5, 3, 8, '2024-12-01 11:55:00', '2024-12-01', 11, 180, 75),
(2, 5, 3, 9, '2024-12-01 12:00:00', '2024-12-01', 12, 160, 80),

-- 电商网站页面浏览
(3, 8, 4, 10, '2024-12-01 14:20:00', '2024-12-01', 14, 120, 55),
(3, 8, 4, 11, '2024-12-01 14:25:00', '2024-12-01', 14, 160, 80),
(3, 8, 4, 12, '2024-12-01 14:30:00', '2024-12-01', 14, 100, 40),

-- 新闻网站页面浏览
(4, 11, 7, 14, '2024-12-02 16:45:00', '2024-12-02', 16, 90, 90),
(4, 11, 7, 15, '2024-12-02 16:50:00', '2024-12-02', 16, 90, 85),

-- 更多页面浏览记录
(1, 3, 9, 4, '2024-12-03 10:10:00', '2024-12-03', 10, 140, 75),
(1, 3, 9, 5, '2024-12-03 10:15:00', '2024-12-03', 10, 140, 80),
(1, 3, 9, 1, '2024-12-03 10:20:00', '2024-12-03', 10, 140, 65),

(3, 10, 10, 12, '2024-12-03 12:30:00', '2024-12-03', 12, 120, 70),
(3, 10, 10, 11, '2024-12-03 12:35:00', '2024-12-03', 12, 120, 75),
(3, 10, 10, 13, '2024-12-03 12:40:00', '2024-12-03', 12, 120, 80),
(3, 10, 10, 10, '2024-12-03 12:45:00', '2024-12-03', 12, 120, 60);

-- ============================================================================
-- 11. 日级统计数据
-- ============================================================================
INSERT INTO stats_daily (
    site_id, stat_date, pv, uv, ip_count, session_count,
    new_visitors, returning_visitors, bounce_count, total_duration,
    avg_duration, avg_page_views, desktop_pv, mobile_pv, tablet_pv,
    search_pv, direct_pv, external_pv
) VALUES
(1, '2024-12-01', 2, 2, 2, 2, 2, 0, 0, 770, 385.0, 2.5, 2, 0, 0, 2, 0, 0),
(2, '2024-12-01', 1, 1, 1, 1, 1, 0, 0, 280, 280.0, 4.0, 1, 0, 0, 0, 1, 0),
(3, '2024-12-01', 1, 1, 1, 1, 1, 0, 0, 380, 380.0, 3.0, 0, 1, 0, 1, 0, 0),
(1, '2024-12-02', 1, 1, 1, 1, 0, 1, 0, 520, 520.0, 4.0, 1, 0, 0, 1, 0, 0),
(2, '2024-12-02', 1, 1, 1, 1, 1, 0, 0, 340, 340.0, 2.0, 1, 0, 0, 0, 1, 0),
(3, '2024-12-02', 1, 1, 1, 1, 1, 0, 0, 720, 720.0, 5.0, 0, 1, 0, 1, 0, 0),
(4, '2024-12-02', 1, 1, 1, 1, 1, 0, 0, 180, 180.0, 2.0, 1, 0, 0, 1, 0, 0),
(1, '2024-12-03', 1, 1, 1, 1, 1, 0, 0, 420, 420.0, 3.0, 0, 0, 1, 1, 0, 0),
(3, '2024-12-03', 1, 1, 1, 1, 1, 0, 0, 480, 480.0, 4.0, 1, 0, 0, 1, 0, 0),
(4, '2024-12-03', 1, 1, 1, 1, 1, 0, 0, 360, 360.0, 3.0, 1, 0, 0, 1, 0, 0);

-- ============================================================================
-- 12. 实时统计数据
-- ============================================================================
INSERT INTO realtime_stats (site_id, time_slot, pv_5min, uv_5min, online_count) VALUES
(1, '2025-01-02 10:15:00', 1, 1, 1),
(1, '2025-01-02 10:20:00', 0, 0, 1),
(3, '2025-01-02 17:30:00', 1, 1, 1),
(3, '2025-01-02 17:35:00', 0, 0, 1),
(4, '2025-01-02 14:45:00', 1, 1, 1),
(4, '2025-01-02 14:50:00', 0, 0, 1);

-- ============================================================================
-- 13. 显示结果
-- ============================================================================
SELECT 'Simple test data created successfully!' as status;

SELECT
    '用户数' as item, COUNT(*) as count FROM users
UNION ALL
SELECT
    '网站数' as item, COUNT(*) as count FROM sites
UNION ALL
SELECT
    '访客数' as item, COUNT(*) as count FROM visitors
UNION ALL
SELECT
    '访问记录数' as item, COUNT(*) as count FROM visits
UNION ALL
SELECT
    '页面浏览数' as item, COUNT(*) as count FROM page_views
UNION ALL
SELECT
    '关键词数' as item, COUNT(*) as count FROM keywords
UNION ALL
SELECT
    '页面URL数' as item, COUNT(*) as count FROM page_urls
UNION ALL
SELECT
    '日级统计数' as item, COUNT(*) as count FROM stats_daily
UNION ALL
SELECT
    '实时统计数' as item, COUNT(*) as count FROM realtime_stats;

-- 显示各网站访问统计
SELECT
    s.site_name,
    COUNT(v.id) as visits,
    COUNT(DISTINCT v.visitor_id) as unique_visitors,
    SUM(v.page_views) as page_views,
    ROUND(AVG(v.visit_duration), 1) as avg_duration
FROM sites s
LEFT JOIN visits v ON s.id = v.site_id
GROUP BY s.id, s.site_name
ORDER BY visits DESC;
