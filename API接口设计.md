# 网站统计系统API接口设计文档

## API基础信息

### 基础URL
```
开发环境: http://localhost:8080/api
生产环境: https://your-domain.com/api
```

### 认证方式
- JWT Token认证
- Header: `Authorization: Bearer <token>`

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": 1672531200
}
```

## 1. 用户认证模块

### 1.1 用户注册
```
POST /auth/register
```

**请求参数:**
```json
{
    "username": "string",
    "email": "string",
    "password": "string",
    "nickname": "string"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "token": "eyJhbGciOiJIUzI1NiIs..."
    }
}
```

### 1.2 用户登录
```
POST /auth/login
```

**请求参数:**
```json
{
    "username": "string",
    "password": "string"
}
```

### 1.3 获取用户信息
```
GET /auth/profile
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "avatar": "https://example.com/avatar.jpg",
        "created_at": "2025-01-01T00:00:00Z"
    }
}
```

## 2. 站点管理模块

### 2.1 获取站点列表
```
GET /sites
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `status`: 状态筛选 (可选)

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "list": [
            {
                "id": 1,
                "site_name": "我的网站",
                "domain": "example.com",
                "site_url": "https://example.com",
                "tracking_code": "abc123def456",
                "status": 1,
                "today_stats": {
                    "pv": 1234,
                    "uv": 567,
                    "ip_count": 456,
                    "bounce_rate": 65.5,
                    "avg_duration": 180
                },
                "yesterday_stats": {
                    "pv": 2345,
                    "uv": 678,
                    "ip_count": 567
                }
            }
        ],
        "total": 5,
        "page": 1,
        "limit": 10
    }
}
```

### 2.2 创建站点
```
POST /sites
```

**请求参数:**
```json
{
    "site_name": "string",
    "domain": "string",
    "site_url": "string",
    "site_type": "string",
    "region": "string",
    "description": "string"
}
```

### 2.3 更新站点
```
PUT /sites/{id}
```

### 2.4 删除站点
```
DELETE /sites/{id}
```

### 2.5 获取统计代码
```
GET /sites/{id}/tracking-code
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "tracking_code": "abc123def456",
        "async_code": "<script>var _stats = _stats || [];(function() {var s = document.createElement(\"script\");s.src = \"https://your-domain.com/js/stats.js?id=abc123def456&async=1\";s.async = true;var x = document.getElementsByTagName(\"script\")[0];x.parentNode.insertBefore(s, x);})();</script>",
        "sync_code": "<script type=\"text/javascript\" src=\"https://your-domain.com/js/stats.js?id=abc123def456\"></script>",
        "image_code": "<script type=\"text/javascript\">document.write(unescape(\"%3Cspan id='stats_icon_abc123def456'%3E%3C/span%3E%3Cscript src='https://your-domain.com/js/stats.js%3Fid%3Dabc123def456%26show%3Dpic' type='text/javascript'%3E%3C/script%3E\"));</script>"
    }
}
```

## 3. 数据收集模块

### 3.1 页面浏览统计
```
POST /track/pageview
```

**请求参数:**
```json
{
    "site_id": "string",
    "page_url": "string",
    "page_title": "string",
    "referer": "string",
    "visitor_id": "string",
    "session_id": "string",
    "user_agent": "string",
    "ip": "string",
    "screen_resolution": "string",
    "language": "string",
    "timestamp": 1672531200
}
```

### 3.2 事件统计
```
POST /track/event
```

**请求参数:**
```json
{
    "site_id": "string",
    "visitor_id": "string",
    "session_id": "string",
    "event_category": "string",
    "event_action": "string",
    "event_label": "string",
    "event_value": 100.50,
    "page_url": "string"
}
```

### 3.3 获取实时在线人数
```
GET /track/online/{site_id}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "online_count": 156,
        "update_time": "2025-01-01T12:00:00Z"
    }
}
```

## 4. 数据分析模块

### 4.1 网站概况
```
GET /stats/overview/{site_id}
```

**查询参数:**
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `compare_type`: 对比类型 (previous_period/same_period_last_year)

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "basic_stats": {
            "pv": 12345,
            "uv": 5678,
            "ip_count": 4567,
            "bounce_rate": 65.5,
            "avg_visit_duration": 180,
            "avg_page_views": 2.5,
            "new_visitor_rate": 45.2
        },
        "compare_stats": {
            "pv_change": 15.5,
            "uv_change": -5.2,
            "ip_change": 8.3
        },
        "top_pages": [
            {
                "page_url": "/index.html",
                "page_title": "首页",
                "pv": 2345,
                "uv": 1234,
                "percentage": 19.0
            }
        ],
        "top_sources": [
            {
                "source_name": "百度",
                "pv": 3456,
                "uv": 1789,
                "percentage": 28.0
            }
        ],
        "top_keywords": [
            {
                "keyword": "网站统计",
                "pv": 567,
                "uv": 234,
                "percentage": 4.6
            }
        ]
    }
}
```

## 5. 来源分析模块

### 5.1 全部来源
```
GET /stats/source/all/{site_id}
```

**查询参数:**
- `start_date`: 开始日期
- `end_date`: 结束日期
- `source_type`: 来源类型 (search_engine/direct/external/internal)

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "summary": {
            "total_visits": 12345,
            "total_pv": 23456,
            "total_uv": 5678
        },
        "source_categories": [
            {
                "category": "搜索引擎",
                "visits": 5678,
                "pv": 12345,
                "uv": 2345,
                "percentage": 46.0,
                "bounce_rate": 65.5
            },
            {
                "category": "直接访问",
                "visits": 3456,
                "pv": 7890,
                "uv": 1789,
                "percentage": 28.0,
                "bounce_rate": 45.2
            }
        ]
    }
}
```

### 5.2 搜索引擎分析
```
GET /stats/source/search-engines/{site_id}
```

### 5.3 搜索关键词
```
GET /stats/source/keywords/{site_id}
```

### 5.4 来路域名
```
GET /stats/source/domains/{site_id}
```

### 5.5 来路页面
```
GET /stats/source/pages/{site_id}
```

## 6. 受访分析模块

### 6.1 受访页面
```
GET /stats/pages/{site_id}
```

**查询参数:**
- `start_date`: 开始日期
- `end_date`: 结束日期
- `sort_by`: 排序字段 (pv/uv/avg_duration)
- `page`: 页码
- `limit`: 每页数量

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "summary": {
            "total_pages": 156,
            "total_pv": 23456,
            "avg_stay_duration": 180
        },
        "pages": [
            {
                "page_url": "/index.html",
                "page_title": "首页",
                "pv": 3456,
                "uv": 1789,
                "entry_count": 1234,
                "exit_count": 567,
                "avg_stay_duration": 240,
                "bounce_rate": 35.5,
                "percentage": 14.7
            }
        ],
        "pagination": {
            "total": 156,
            "page": 1,
            "limit": 20
        }
    }
}
```

### 6.2 受访域名
```
GET /stats/domains/{site_id}
```

### 6.3 入口页面
```
GET /stats/pages/entry/{site_id}
```

### 6.4 退出页面
```
GET /stats/pages/exit/{site_id}
```

## 7. 访客分析模块

### 7.1 地区分布
```
GET /stats/visitors/regions/{site_id}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "summary": {
            "total_regions": 34,
            "total_uv": 5678
        },
        "regions": [
            {
                "country": "中国",
                "province": "北京市",
                "city": "北京市",
                "pv": 2345,
                "uv": 1234,
                "ip_count": 1123,
                "percentage": 21.7,
                "bounce_rate": 55.5
            }
        ],
        "map_data": [
            {
                "province": "北京市",
                "value": 1234,
                "percentage": 21.7
            }
        ]
    }
}
```

### 7.2 系统环境
```
GET /stats/visitors/environment/{site_id}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "devices": [
            {
                "device_type": "desktop",
                "pv": 12345,
                "uv": 5678,
                "percentage": 65.5
            },
            {
                "device_type": "mobile",
                "pv": 8901,
                "uv": 3456,
                "percentage": 34.5
            }
        ],
        "browsers": [
            {
                "browser": "Chrome",
                "version": "120.0",
                "pv": 8901,
                "uv": 3456,
                "percentage": 45.2
            }
        ],
        "operating_systems": [
            {
                "os": "Windows",
                "version": "11",
                "pv": 6789,
                "uv": 2345,
                "percentage": 35.8
            }
        ]
    }
}
```

### 7.3 新老访客
```
GET /stats/visitors/new-returning/{site_id}
```

### 7.4 访客忠诚度
```
GET /stats/visitors/loyalty/{site_id}
```

### 7.5 访客活跃度
```
GET /stats/visitors/activity/{site_id}
```

## 8. 设置管理模块

### 8.1 获取站点配置
```
GET /settings/site/{site_id}
```

### 8.2 更新站点配置
```
PUT /settings/site/{site_id}
```

### 8.3 排除规则管理
```
GET /settings/exclude-rules/{site_id}
POST /settings/exclude-rules/{site_id}
PUT /settings/exclude-rules/{rule_id}
DELETE /settings/exclude-rules/{rule_id}
```

### 8.4 转化目标管理
```
GET /settings/goals/{site_id}
POST /settings/goals/{site_id}
PUT /settings/goals/{goal_id}
DELETE /settings/goals/{goal_id}
```

## 9. 数据导出模块

### 9.1 导出报表数据
```
POST /export/report
```

**请求参数:**
```json
{
    "site_id": 1,
    "report_type": "overview",
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "format": "excel",
    "email": "<EMAIL>"
}
```

### 9.2 获取导出任务状态
```
GET /export/task/{task_id}
```

## 10. 管理后台接口

### 10.1 用户管理
```
GET /admin/users
POST /admin/users
PUT /admin/users/{id}
DELETE /admin/users/{id}
```

### 10.2 站点管理
```
GET /admin/sites
PUT /admin/sites/{id}/status
```

### 10.3 系统统计
```
GET /admin/system/stats
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 限流规则

- 数据收集接口: 1000次/分钟/IP
- 查询接口: 100次/分钟/用户
- 导出接口: 10次/小时/用户
```

### 4.2 趋势分析
```
GET /stats/trend/{site_id}
```

**查询参数:**
- `start_date`: 开始日期
- `end_date`: 结束日期
- `granularity`: 粒度 (hour/day/week/month)
- `metrics`: 指标 (pv,uv,ip_count)

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "chart_data": [
            {
                "date": "2025-01-01",
                "pv": 1234,
                "uv": 567,
                "ip_count": 456
            },
            {
                "date": "2025-01-02",
                "pv": 2345,
                "uv": 678,
                "ip_count": 567
            }
        ],
        "summary": {
            "total_pv": 12345,
            "total_uv": 5678,
            "avg_pv": 1234,
            "peak_pv": 3456,
            "peak_date": "2025-01-05"
        }
    }
}
```

### 4.3 对比分析
```
GET /stats/compare/{site_id}
```

### 4.4 实时统计
```
GET /stats/realtime/{site_id}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "online_visitors": 156,
        "today_stats": {
            "pv": 12345,
            "uv": 5678,
            "ip_count": 4567
        },
        "recent_visitors": [
            {
                "visitor_id": "abc123",
                "current_page": "/product/123",
                "region": "北京",
                "visit_time": "2025-01-01T12:00:00Z",
                "source": "百度",
                "keyword": "产品介绍"
            }
        ],
        "hourly_trend": [
            {
                "hour": 0,
                "pv": 123,
                "uv": 67
            }
        ]
    }
}
```
