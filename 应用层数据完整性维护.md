# 应用层数据完整性维护指南

## 问题说明

由于MySQL不支持在分区表上使用外键约束，我们的 `visits` 表（核心大数据表）无法使用数据库级别的外键约束来维护数据完整性。因此需要在应用层实现数据完整性检查和维护。

## 解决方案

### 1. 数据插入时的完整性检查

#### Go代码示例 - 插入访问记录前的验证
```go
package models

import (
    "fmt"
    "gorm.io/gorm"
)

type VisitService struct {
    db *gorm.DB
}

// 插入访问记录前的完整性检查
func (s *VisitService) CreateVisit(visit *Visit) error {
    // 开启事务
    tx := s.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()

    // 1. 检查站点ID是否存在
    var siteExists bool
    err := tx.Model(&Site{}).Select("count(*) > 0").Where("id = ?", visit.SiteID).Find(&siteExists).Error
    if err != nil || !siteExists {
        tx.Rollback()
        return fmt.Errorf("invalid site_id: %d", visit.SiteID)
    }

    // 2. 检查访客ID是否存在
    var visitorExists bool
    err = tx.Model(&Visitor{}).Select("count(*) > 0").Where("id = ?", visit.VisitorID).Find(&visitorExists).Error
    if err != nil || !visitorExists {
        tx.Rollback()
        return fmt.Errorf("invalid visitor_id: %d", visit.VisitorID)
    }

    // 3. 检查会话ID是否存在，不存在则创建
    var sessionExists bool
    err = tx.Model(&Session{}).Select("count(*) > 0").Where("id = ?", visit.SessionID).Find(&sessionExists).Error
    if err != nil {
        tx.Rollback()
        return err
    }
    if !sessionExists {
        // 创建会话记录
        session := &Session{ID: visit.SessionID}
        if err := tx.Create(session).Error; err != nil {
            tx.Rollback()
            return fmt.Errorf("failed to create session: %v", err)
        }
    }

    // 4. 检查IP地址ID，不存在则创建
    if visit.IPID != 0 {
        var ipExists bool
        err = tx.Model(&IPAddress{}).Select("count(*) > 0").Where("id = ?", visit.IPID).Find(&ipExists).Error
        if err != nil {
            tx.Rollback()
            return err
        }
        if !ipExists {
            tx.Rollback()
            return fmt.Errorf("invalid ip_id: %d", visit.IPID)
        }
    }

    // 5. 检查页面URL ID，不存在则创建
    if visit.LandingPageID != 0 {
        var pageExists bool
        err = tx.Model(&PageURL{}).Select("count(*) > 0").Where("id = ?", visit.LandingPageID).Find(&pageExists).Error
        if err != nil {
            tx.Rollback()
            return err
        }
        if !pageExists {
            tx.Rollback()
            return fmt.Errorf("invalid landing_page_id: %d", visit.LandingPageID)
        }
    }

    // 6. 检查其他可选的外键字段
    if err := s.validateOptionalForeignKeys(tx, visit); err != nil {
        tx.Rollback()
        return err
    }

    // 7. 插入访问记录
    if err := tx.Create(visit).Error; err != nil {
        tx.Rollback()
        return err
    }

    return tx.Commit().Error
}

// 验证可选的外键字段
func (s *VisitService) validateOptionalForeignKeys(tx *gorm.DB, visit *Visit) error {
    // 检查用户代理ID
    if visit.UserAgentID != nil && *visit.UserAgentID != 0 {
        var exists bool
        err := tx.Model(&UserAgent{}).Select("count(*) > 0").Where("id = ?", *visit.UserAgentID).Find(&exists).Error
        if err != nil || !exists {
            visit.UserAgentID = nil // 设置为NULL而不是返回错误
        }
    }

    // 检查地区ID
    if visit.RegionID != nil && *visit.RegionID != 0 {
        var exists bool
        err := tx.Model(&Region{}).Select("count(*) > 0").Where("id = ?", *visit.RegionID).Find(&exists).Error
        if err != nil || !exists {
            visit.RegionID = nil
        }
    }

    // 检查浏览器ID
    if visit.BrowserID != nil && *visit.BrowserID != 0 {
        var exists bool
        err := tx.Model(&Browser{}).Select("count(*) > 0").Where("id = ?", *visit.BrowserID).Find(&exists).Error
        if err != nil || !exists {
            visit.BrowserID = nil
        }
    }

    // 检查操作系统ID
    if visit.OSID != nil && *visit.OSID != 0 {
        var exists bool
        err := tx.Model(&OperatingSystem{}).Select("count(*) > 0").Where("id = ?", *visit.OSID).Find(&exists).Error
        if err != nil || !exists {
            visit.OSID = nil
        }
    }

    // 检查搜索引擎ID
    if visit.SearchEngineID != nil && *visit.SearchEngineID != 0 {
        var exists bool
        err := tx.Model(&SearchEngine{}).Select("count(*) > 0").Where("id = ?", *visit.SearchEngineID).Find(&exists).Error
        if err != nil || !exists {
            visit.SearchEngineID = nil
        }
    }

    // 检查关键词ID
    if visit.SearchKeywordID != nil && *visit.SearchKeywordID != 0 {
        var exists bool
        err := tx.Model(&Keyword{}).Select("count(*) > 0").Where("id = ?", *visit.SearchKeywordID).Find(&exists).Error
        if err != nil || !exists {
            visit.SearchKeywordID = nil
        }
    }

    return nil
}
```

### 2. 字典表数据的获取或创建

#### 获取或创建关键词ID
```go
func (s *VisitService) GetOrCreateKeywordID(keyword string) (int64, error) {
    if keyword == "" {
        return 0, nil
    }

    // 计算哈希值
    hash := sha256.Sum256([]byte(keyword))
    keywordHash := hex.EncodeToString(hash[:])

    // 先尝试查找
    var keywordRecord Keyword
    err := s.db.Where("keyword_hash = ?", keywordHash).First(&keywordRecord).Error
    if err == nil {
        // 更新最后出现时间和计数
        s.db.Model(&keywordRecord).Updates(map[string]interface{}{
            "last_seen":    time.Now(),
            "total_count":  gorm.Expr("total_count + 1"),
        })
        return keywordRecord.ID, nil
    }

    if !errors.Is(err, gorm.ErrRecordNotFound) {
        return 0, err
    }

    // 不存在则创建
    keywordRecord = Keyword{
        Keyword:     keyword,
        KeywordHash: keywordHash,
        FirstSeen:   time.Now(),
        LastSeen:    time.Now(),
        TotalCount:  1,
    }

    if err := s.db.Create(&keywordRecord).Error; err != nil {
        return 0, err
    }

    return keywordRecord.ID, nil
}
```

#### 获取或创建页面URL ID
```go
func (s *VisitService) GetOrCreatePageURLID(url, title string) (int64, error) {
    if url == "" {
        return 0, nil
    }

    // 计算哈希值
    hash := sha256.Sum256([]byte(url))
    urlHash := hex.EncodeToString(hash[:])

    // 解析URL获取域名和路径
    parsedURL, err := neturl.Parse(url)
    if err != nil {
        return 0, err
    }

    domain := parsedURL.Host
    path := parsedURL.Path
    if parsedURL.RawQuery != "" {
        path += "?" + parsedURL.RawQuery
    }

    // 先尝试查找
    var pageURL PageURL
    err = s.db.Where("url_hash = ?", urlHash).First(&pageURL).Error
    if err == nil {
        // 更新最后出现时间和标题
        updates := map[string]interface{}{
            "last_seen": time.Now(),
        }
        if title != "" && pageURL.Title != title {
            updates["title"] = title
        }
        s.db.Model(&pageURL).Updates(updates)
        return pageURL.ID, nil
    }

    if !errors.Is(err, gorm.ErrRecordNotFound) {
        return 0, err
    }

    // 不存在则创建
    pageURL = PageURL{
        URL:       url,
        URLHash:   urlHash,
        Domain:    domain,
        Path:      path,
        Title:     title,
        FirstSeen: time.Now(),
        LastSeen:  time.Now(),
    }

    if err := s.db.Create(&pageURL).Error; err != nil {
        return 0, err
    }

    return pageURL.ID, nil
}
```

### 3. 定期数据完整性检查

#### 定时任务检查数据完整性
```go
func (s *VisitService) CheckDataIntegrity() error {
    // 执行数据完整性检查存储过程
    var result struct {
        Status        string `json:"status"`
        TotalIssues   int    `json:"total_issues"`
        Recommendation string `json:"recommendation"`
    }

    err := s.db.Raw("CALL sp_check_data_integrity()").Scan(&result).Error
    if err != nil {
        return err
    }

    if result.TotalIssues > 0 {
        // 记录日志并发送告警
        log.Printf("Data integrity issues found: %d", result.TotalIssues)
        
        // 可以选择自动清理或手动处理
        // return s.CleanupInvalidReferences()
    }

    return nil
}

// 清理无效的外键引用
func (s *VisitService) CleanupInvalidReferences() error {
    return s.db.Exec("CALL sp_cleanup_invalid_references()").Error
}
```

### 4. 批量数据处理的完整性保证

#### 批量插入时的优化策略
```go
func (s *VisitService) BatchCreateVisits(visits []*Visit) error {
    // 预先获取所有需要的字典表ID
    keywordIDs := make(map[string]int64)
    pageURLIDs := make(map[string]int64)
    
    // 收集所有唯一的关键词和URL
    keywords := make(map[string]bool)
    urls := make(map[string]bool)
    
    for _, visit := range visits {
        if visit.SearchKeyword != "" {
            keywords[visit.SearchKeyword] = true
        }
        if visit.LandingPageURL != "" {
            urls[visit.LandingPageURL] = true
        }
    }

    // 批量获取或创建关键词ID
    for keyword := range keywords {
        id, err := s.GetOrCreateKeywordID(keyword)
        if err != nil {
            return err
        }
        keywordIDs[keyword] = id
    }

    // 批量获取或创建页面URL ID
    for url := range urls {
        id, err := s.GetOrCreatePageURLID(url, "")
        if err != nil {
            return err
        }
        pageURLIDs[url] = id
    }

    // 设置ID并批量插入
    for _, visit := range visits {
        if visit.SearchKeyword != "" {
            keywordID := keywordIDs[visit.SearchKeyword]
            visit.SearchKeywordID = &keywordID
        }
        if visit.LandingPageURL != "" {
            pageURLID := pageURLIDs[visit.LandingPageURL]
            visit.LandingPageID = pageURLID
        }
    }

    // 批量插入（分批处理，避免单次插入过多数据）
    batchSize := 1000
    for i := 0; i < len(visits); i += batchSize {
        end := i + batchSize
        if end > len(visits) {
            end = len(visits)
        }
        
        if err := s.db.CreateInBatches(visits[i:end], batchSize).Error; err != nil {
            return err
        }
    }

    return nil
}
```

## 监控和维护

### 1. 定期执行数据完整性检查
```bash
# 添加到crontab，每天凌晨2点执行
0 2 * * * mysql -u stats_user -p'password' web_stats -e "CALL sp_check_data_integrity();"
```

### 2. 监控数据完整性视图
```sql
-- 查看数据完整性状态
SELECT * FROM v_data_integrity_check;

-- 如果发现问题，执行清理
CALL sp_cleanup_invalid_references();
```

### 3. 应用层监控
- 在应用启动时执行数据完整性检查
- 定期（如每小时）执行完整性检查
- 在数据插入失败时记录详细日志
- 设置告警机制，当发现数据完整性问题时及时通知

## 总结

虽然分区表无法使用数据库级别的外键约束，但通过以上应用层的数据完整性维护策略，我们可以：

1. **保证数据一致性** - 在插入前验证所有外键关系
2. **自动处理字典表** - 自动创建不存在的字典表记录
3. **定期检查和清理** - 通过存储过程和定时任务维护数据质量
4. **性能优化** - 批量处理时预先获取ID，减少数据库查询
5. **监控告警** - 及时发现和处理数据完整性问题

这种方案在保证数据完整性的同时，充分利用了分区表的性能优势。
