-- 网站统计系统数据库初始化脚本
-- 创建时间: 2025-01-02
-- 版本: v1.0 深度优化版

-- 创建数据库
CREATE DATABASE IF NOT EXISTS web_stats CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE web_stats;

-- 设置SQL模式
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- ============================================================================
-- 字典表设计（性能优化核心）
-- ============================================================================

-- 搜索引擎字典表
CREATE TABLE search_engines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '搜索引擎名称',
    domain VARCHAR(100) COMMENT '搜索引擎域名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎字典表';

-- 关键词字典表
CREATE TABLE keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(200) UNIQUE NOT NULL COMMENT '关键词',
    keyword_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '关键词哈希值',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_count INT DEFAULT 1 COMMENT '总出现次数',
    INDEX idx_keyword (keyword),
    INDEX idx_keyword_hash (keyword_hash),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词字典表';

-- 浏览器字典表
CREATE TABLE browsers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '浏览器名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_browser_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览器字典表';

-- 操作系统字典表
CREATE TABLE operating_systems (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '操作系统名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_os_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作系统字典表';

-- 地区字典表
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country VARCHAR(50) NOT NULL COMMENT '国家',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_region (country, province, city),
    INDEX idx_country (country),
    INDEX idx_province (province),
    INDEX idx_city (city)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区字典表';

-- 页面URL字典表
CREATE TABLE page_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(500) UNIQUE NOT NULL COMMENT '页面URL',
    url_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'URL哈希值',
    domain VARCHAR(100) COMMENT '域名',
    path VARCHAR(400) COMMENT '路径',
    title VARCHAR(200) COMMENT '页面标题',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    INDEX idx_url_hash (url_hash),
    INDEX idx_domain (domain),
    INDEX idx_path (path(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面URL字典表';

-- 来源域名字典表
CREATE TABLE referer_domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(100) UNIQUE NOT NULL COMMENT '来源域名',
    domain_type ENUM('search_engine', 'social_media', 'direct', 'external') DEFAULT 'external' COMMENT '域名类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_domain_type (domain_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名字典表';

-- 会话字典表
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '会话哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_hash (session_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话字典表';

-- IP地址字典表
CREATE TABLE ip_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_int INT UNSIGNED COMMENT 'IPv4地址的整数表示',
    ip_string VARCHAR(45) COMMENT '原始IP字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_int (ip_int),
    INDEX idx_ip_string (ip_string)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP地址字典表';

-- User Agent字典表
CREATE TABLE user_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ua_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'User Agent哈希值',
    user_agent TEXT COMMENT '完整User Agent字符串',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (browser_id) REFERENCES browsers(id),
    FOREIGN KEY (os_id) REFERENCES operating_systems(id),
    INDEX idx_ua_hash (ua_hash),
    INDEX idx_browser_os (browser_id, os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User Agent字典表';

-- ============================================================================
-- 用户管理相关表
-- ============================================================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 网站表
CREATE TABLE sites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    site_name VARCHAR(100) NOT NULL COMMENT '网站名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '网站URL',
    site_type VARCHAR(50) DEFAULT 'other' COMMENT '网站类型',
    region VARCHAR(50) COMMENT '网站地区',
    description TEXT COMMENT '网站描述',
    tracking_code VARCHAR(32) UNIQUE NOT NULL COMMENT '统计代码ID',
    view_password VARCHAR(50) COMMENT '查看密码',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_tracking_code (tracking_code),
    INDEX idx_domain (domain),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';

-- ============================================================================
-- 访客和会话相关表
-- ============================================================================

-- 访客表
CREATE TABLE visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_hash VARCHAR(64) NOT NULL COMMENT '访客唯一标识',
    first_visit TIMESTAMP NOT NULL COMMENT '首次访问时间',
    last_visit TIMESTAMP NOT NULL COMMENT '最后访问时间',
    total_visits INT DEFAULT 1 COMMENT '总访问次数',
    total_page_views INT DEFAULT 1 COMMENT '总页面浏览数',
    total_duration INT DEFAULT 0 COMMENT '总访问时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_visitor (site_id, visitor_hash),
    INDEX idx_site_id (site_id),
    INDEX idx_last_visit (last_visit)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访客表';

-- 访问记录表（深度优化版 - 修复分区表主键问题）
CREATE TABLE visits (
    id BIGINT AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID（关联sessions表）',
    ip_id INT NOT NULL COMMENT 'IP地址ID（关联ip_addresses表）',
    user_agent_id INT COMMENT '用户代理ID（关联user_agents表）',
    referer_domain_id INT COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    landing_page_id BIGINT NOT NULL COMMENT '入口页面ID',
    exit_page_id BIGINT COMMENT '退出页面ID',

    -- 时间字段优化（关键性能提升）
    visit_time TIMESTAMP NOT NULL COMMENT '访问时间',
    visit_date DATE NOT NULL COMMENT '访问日期（冗余字段，加速查询）',
    visit_hour TINYINT NOT NULL COMMENT '访问小时（0-23）',
    visit_year SMALLINT NOT NULL COMMENT '访问年份',
    visit_month TINYINT NOT NULL COMMENT '访问月份（1-12）',
    visit_week TINYINT NOT NULL COMMENT '访问周数（1-53）',
    visit_weekday TINYINT NOT NULL COMMENT '星期几（1-7）',

    visit_duration INT DEFAULT 0 COMMENT '访问时长(秒)',
    page_views INT DEFAULT 1 COMMENT '页面浏览数',
    is_new_visitor TINYINT DEFAULT 0 COMMENT '是否新访客：1-是，0-否',
    region_id INT COMMENT '地区ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    language VARCHAR(10) COMMENT '语言',
    search_engine_id INT COMMENT '搜索引擎ID',
    search_keyword_id BIGINT COMMENT '搜索关键词ID',
    utm_source VARCHAR(100) COMMENT 'UTM来源',
    utm_medium VARCHAR(100) COMMENT 'UTM媒介',
    utm_campaign VARCHAR(100) COMMENT 'UTM活动',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 复合主键（包含分区键）
    PRIMARY KEY (id, visit_year, visit_month),

    -- 优化后的索引策略
    INDEX idx_site_date (site_id, visit_date),
    INDEX idx_site_date_hour (site_id, visit_date, visit_hour),
    INDEX idx_site_year_month (site_id, visit_year, visit_month),
    INDEX idx_site_week (site_id, visit_year, visit_week),
    INDEX idx_visitor_date (visitor_id, visit_date),
    INDEX idx_session_date (session_id, visit_date),

    -- 分析查询专用索引
    INDEX idx_analytics_basic (site_id, visit_date, device_type, is_new_visitor),
    INDEX idx_analytics_source (site_id, visit_date, search_engine_id, referer_domain_id),
    INDEX idx_analytics_region (site_id, visit_date, region_id),
    INDEX idx_analytics_page (site_id, visit_date, landing_page_id),

    -- 覆盖索引（避免回表查询）
    INDEX idx_covering_basic (site_id, visit_date, visitor_id, page_views, visit_duration, is_new_visitor),
    INDEX idx_covering_source (site_id, visit_date, search_engine_id, search_keyword_id, visitor_id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问记录表-深度优化版'
PARTITION BY RANGE (visit_year * 100 + visit_month) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 页面浏览记录表（优化版）
CREATE TABLE page_views (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    view_time TIMESTAMP NOT NULL COMMENT '浏览时间',
    view_date DATE NOT NULL COMMENT '浏览日期（冗余字段）',
    view_hour TINYINT NOT NULL COMMENT '浏览小时',
    stay_duration INT DEFAULT 0 COMMENT '停留时长(秒)',
    scroll_depth INT DEFAULT 0 COMMENT '滚动深度百分比',
    is_bounce TINYINT DEFAULT 0 COMMENT '是否跳出：1-是，0-否',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id),
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id),
    INDEX idx_site_date (site_id, view_date),
    INDEX idx_site_date_hour (site_id, view_date, view_hour),
    INDEX idx_visitor_date (visitor_id, view_date),
    INDEX idx_session_date (session_id, view_date),
    INDEX idx_page_url (site_id, page_url_id),
    INDEX idx_referer_url (referer_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面浏览记录表-优化版';

-- 实时在线访客表
CREATE TABLE online_visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    current_page_id BIGINT COMMENT '当前页面ID',
    last_activity TIMESTAMP NOT NULL COMMENT '最后活动时间',
    ip_id INT NOT NULL COMMENT 'IP地址ID',
    user_agent_id INT COMMENT '用户代理ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (current_page_id) REFERENCES page_urls(id),
    FOREIGN KEY (ip_id) REFERENCES ip_addresses(id),
    FOREIGN KEY (user_agent_id) REFERENCES user_agents(id),
    UNIQUE KEY uk_site_session (site_id, session_id),
    INDEX idx_site_activity (site_id, last_activity),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时在线访客表';

-- ============================================================================
-- 预聚合统计表（极速查询）
-- ============================================================================

-- 小时级预聚合表
CREATE TABLE stats_hourly (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    stat_hour TINYINT NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0,
    uv INT DEFAULT 0,
    ip_count INT DEFAULT 0,
    session_count INT DEFAULT 0,
    new_visitors INT DEFAULT 0,
    bounce_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,

    -- 设备分布
    desktop_pv INT DEFAULT 0,
    mobile_pv INT DEFAULT 0,
    tablet_pv INT DEFAULT 0,

    -- 来源分布
    search_pv INT DEFAULT 0,
    direct_pv INT DEFAULT 0,
    external_pv INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date_hour (site_id, stat_date, stat_hour),
    INDEX idx_site_date (site_id, stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级预聚合表';

-- 日级预聚合表
CREATE TABLE stats_daily (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0,
    uv INT DEFAULT 0,
    ip_count INT DEFAULT 0,
    session_count INT DEFAULT 0,
    new_visitors INT DEFAULT 0,
    returning_visitors INT DEFAULT 0,
    bounce_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,
    avg_duration DECIMAL(8,2) DEFAULT 0,
    avg_page_views DECIMAL(5,2) DEFAULT 0,

    -- 设备分布
    desktop_pv INT DEFAULT 0,
    desktop_uv INT DEFAULT 0,
    mobile_pv INT DEFAULT 0,
    mobile_uv INT DEFAULT 0,
    tablet_pv INT DEFAULT 0,
    tablet_uv INT DEFAULT 0,

    -- 来源分布
    search_pv INT DEFAULT 0,
    search_uv INT DEFAULT 0,
    direct_pv INT DEFAULT 0,
    direct_uv INT DEFAULT 0,
    external_pv INT DEFAULT 0,
    external_uv INT DEFAULT 0,

    -- 地区TOP5
    top_regions JSON COMMENT 'TOP5地区分布',

    -- 页面TOP10
    top_pages JSON COMMENT 'TOP10页面',

    -- 关键词TOP10
    top_keywords JSON COMMENT 'TOP10关键词',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date (site_id, stat_date),
    INDEX idx_site_date (site_id, stat_date),
    INDEX idx_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日级预聚合表';

-- 月级预聚合表
CREATE TABLE stats_monthly (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_year SMALLINT NOT NULL,
    stat_month TINYINT NOT NULL,

    -- 完整的月度统计数据
    pv BIGINT DEFAULT 0,
    uv BIGINT DEFAULT 0,
    ip_count BIGINT DEFAULT 0,
    session_count BIGINT DEFAULT 0,
    new_visitors BIGINT DEFAULT 0,
    returning_visitors BIGINT DEFAULT 0,

    -- 详细分析数据
    device_stats JSON COMMENT '设备分布统计',
    source_stats JSON COMMENT '来源分布统计',
    region_stats JSON COMMENT '地区分布统计',
    page_stats JSON COMMENT '页面访问统计',
    keyword_stats JSON COMMENT '关键词统计',
    browser_stats JSON COMMENT '浏览器统计',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_year_month (site_id, stat_year, stat_month),
    INDEX idx_site_year (site_id, stat_year),
    INDEX idx_year_month (stat_year, stat_month)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月级预聚合表';

-- 实时统计表（滑动窗口）
CREATE TABLE realtime_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    time_slot TIMESTAMP NOT NULL COMMENT '时间槽（每5分钟一个槽）',

    -- 实时指标
    pv_5min INT DEFAULT 0 COMMENT '5分钟PV',
    uv_5min INT DEFAULT 0 COMMENT '5分钟UV',
    online_count INT DEFAULT 0 COMMENT '当前在线人数',

    -- 累计指标（当日）
    pv_today INT DEFAULT 0 COMMENT '今日累计PV',
    uv_today INT DEFAULT 0 COMMENT '今日累计UV',

    -- 设备分布（当前5分钟）
    desktop_pv SMALLINT DEFAULT 0,
    mobile_pv SMALLINT DEFAULT 0,
    tablet_pv SMALLINT DEFAULT 0,

    -- 来源分布（当前5分钟）
    search_pv SMALLINT DEFAULT 0,
    direct_pv SMALLINT DEFAULT 0,
    external_pv SMALLINT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_time (site_id, time_slot),
    INDEX idx_site_time (site_id, time_slot),
    INDEX idx_time_slot (time_slot)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时统计表';

-- ============================================================================
-- 详细统计表（使用字典表ID优化）
-- ============================================================================

-- 搜索引擎统计表
CREATE TABLE search_engine_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    search_engine_id INT NOT NULL COMMENT '搜索引擎ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    keywords_count INT DEFAULT 0 COMMENT '关键词数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id),
    UNIQUE KEY uk_site_date_engine (site_id, date, search_engine_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_search_engine (search_engine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎统计表';

-- 关键词统计表
CREATE TABLE keyword_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    keyword_id BIGINT NOT NULL COMMENT '关键词ID',
    search_engine_id INT COMMENT '搜索引擎ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (keyword_id) REFERENCES keywords(id),
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id),
    UNIQUE KEY uk_site_date_keyword (site_id, date, keyword_id, search_engine_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_keyword (keyword_id),
    INDEX idx_search_engine (search_engine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词统计表';

-- 地区统计表
CREATE TABLE region_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    region_id INT NOT NULL COMMENT '地区ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (region_id) REFERENCES regions(id),
    UNIQUE KEY uk_site_date_region (site_id, date, region_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_region (region_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区统计表';

-- 页面统计表
CREATE TABLE page_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    entry_count INT DEFAULT 0 COMMENT '入口次数',
    exit_count INT DEFAULT 0 COMMENT '退出次数',
    avg_stay_duration INT DEFAULT 0 COMMENT '平均停留时长(秒)',
    bounce_count INT DEFAULT 0 COMMENT '跳出次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id),
    UNIQUE KEY uk_site_date_page (site_id, date, page_url_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_page_url (page_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面统计表';

-- 来源域名统计表
CREATE TABLE referer_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    referer_domain_id INT NOT NULL COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源URL ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (referer_domain_id) REFERENCES referer_domains(id),
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id),
    UNIQUE KEY uk_site_date_referer (site_id, date, referer_domain_id, referer_url_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_referer_domain (referer_domain_id),
    INDEX idx_referer_url (referer_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名统计表';

-- 设备统计表
CREATE TABLE device_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    device_type ENUM('desktop', 'mobile', 'tablet') NOT NULL COMMENT '设备类型',
    browser_id INT NOT NULL COMMENT '浏览器ID',
    os_id INT NOT NULL COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (browser_id) REFERENCES browsers(id),
    FOREIGN KEY (os_id) REFERENCES operating_systems(id),
    UNIQUE KEY uk_site_date_device (site_id, date, device_type, browser_id, os_id, screen_resolution),
    INDEX idx_site_date (site_id, date),
    INDEX idx_device_type (device_type),
    INDEX idx_browser (browser_id),
    INDEX idx_os (os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备统计表';

-- ============================================================================
-- 配置和管理表
-- ============================================================================

-- 排除规则表
CREATE TABLE exclude_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    rule_type ENUM('ip', 'url', 'referer', 'keyword', 'user_agent') NOT NULL COMMENT '规则类型',
    rule_value VARCHAR(500) NOT NULL COMMENT '规则值',
    match_type ENUM('exact', 'contains', 'regex', 'range') DEFAULT 'exact' COMMENT '匹配方式',
    is_case_sensitive TINYINT DEFAULT 0 COMMENT '是否区分大小写',
    description VARCHAR(200) COMMENT '规则描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    INDEX idx_site_type (site_id, rule_type),
    INDEX idx_rule_value (rule_value(100)),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排除规则表';

-- 站点配置表
CREATE TABLE site_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_config (site_id, config_key),
    INDEX idx_site_id (site_id),
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站点配置表';

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ============================================================================
-- 事件和转化跟踪表
-- ============================================================================

-- 事件跟踪表
CREATE TABLE events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    event_category VARCHAR(50) NOT NULL COMMENT '事件分类',
    event_action VARCHAR(50) NOT NULL COMMENT '事件动作',
    event_label VARCHAR(100) COMMENT '事件标签',
    event_value DECIMAL(10,2) COMMENT '事件值',
    page_url_id BIGINT COMMENT '页面URL ID',
    event_time TIMESTAMP NOT NULL COMMENT '事件时间',
    event_date DATE NOT NULL COMMENT '事件日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id),
    INDEX idx_site_date (site_id, event_date),
    INDEX idx_visitor_date (visitor_id, event_date),
    INDEX idx_session_date (session_id, event_date),
    INDEX idx_category_action (event_category, event_action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事件跟踪表';

-- 转化目标表
CREATE TABLE conversion_goals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    goal_name VARCHAR(100) NOT NULL COMMENT '目标名称',
    goal_type ENUM('url', 'event', 'duration', 'pages') NOT NULL COMMENT '目标类型',
    goal_value VARCHAR(200) NOT NULL COMMENT '目标值',
    goal_funnel JSON COMMENT '转化漏斗配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    INDEX idx_site_id (site_id),
    INDEX idx_goal_type (goal_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转化目标表';

-- ============================================================================
-- 数据归档和清理表
-- ============================================================================

-- 数据归档记录表
CREATE TABLE archive_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    archive_date DATE NOT NULL COMMENT '归档日期',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    record_count INT NOT NULL COMMENT '记录数量',
    archive_file VARCHAR(255) COMMENT '归档文件路径',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_table_date (table_name, archive_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据归档记录表';

-- ============================================================================
-- 初始化数据
-- ============================================================================

-- 插入默认搜索引擎数据
INSERT INTO search_engines (name, domain) VALUES
('百度', 'baidu.com'),
('Google', 'google.com'),
('360搜索', '360.cn'),
('搜狗', 'sogou.com'),
('神马搜索', 'sm.cn'),
('夸克搜索', 'quark.cn'),
('头条搜索', 'toutiao.com'),
('Bing', 'bing.com'),
('Yahoo', 'yahoo.com'),
('DuckDuckGo', 'duckduckgo.com');

-- 插入默认浏览器数据
INSERT INTO browsers (name, version) VALUES
('Chrome', '120.0'),
('Firefox', '119.0'),
('Safari', '17.0'),
('Edge', '119.0'),
('Opera', '105.0'),
('IE', '11.0');

-- 插入默认操作系统数据
INSERT INTO operating_systems (name, version) VALUES
('Windows', '11'),
('Windows', '10'),
('macOS', '14.0'),
('macOS', '13.0'),
('Linux', 'Ubuntu 22.04'),
('iOS', '17.0'),
('Android', '14.0'),
('Android', '13.0');

-- 插入默认地区数据
INSERT INTO regions (country, province, city) VALUES
('中国', '北京市', '北京市'),
('中国', '上海市', '上海市'),
('中国', '广东省', '广州市'),
('中国', '广东省', '深圳市'),
('中国', '浙江省', '杭州市'),
('中国', '江苏省', '南京市'),
('中国', '四川省', '成都市'),
('中国', '湖北省', '武汉市'),
('中国', '陕西省', '西安市'),
('中国', '山东省', '青岛市'),
('美国', 'California', 'San Francisco'),
('美国', 'New York', 'New York'),
('日本', '东京都', '东京'),
('英国', 'England', 'London'),
('德国', 'Bavaria', 'Munich');

-- 插入默认来源域名数据
INSERT INTO referer_domains (domain, domain_type) VALUES
('baidu.com', 'search_engine'),
('google.com', 'search_engine'),
('360.cn', 'search_engine'),
('sogou.com', 'search_engine'),
('weibo.com', 'social_media'),
('qq.com', 'social_media'),
('wechat.com', 'social_media'),
('zhihu.com', 'social_media'),
('douyin.com', 'social_media'),
('bilibili.com', 'social_media');

-- ============================================================================
-- 自动化任务和事件
-- ============================================================================

-- 自动清理过期实时统计数据
DELIMITER $$
CREATE EVENT IF NOT EXISTS evt_cleanup_realtime_stats
ON SCHEDULE EVERY 1 HOUR
DO
BEGIN
    DELETE FROM realtime_stats
    WHERE time_slot < DATE_SUB(NOW(), INTERVAL 24 HOUR);
END$$
DELIMITER ;

-- 自动清理过期在线访客数据
DELIMITER $$
CREATE EVENT IF NOT EXISTS evt_cleanup_online_visitors
ON SCHEDULE EVERY 30 MINUTE
DO
BEGIN
    DELETE FROM online_visitors
    WHERE last_activity < DATE_SUB(NOW(), INTERVAL 30 MINUTE);
END$$
DELIMITER ;

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- ============================================================================
-- 创建索引优化（补充）
-- ============================================================================

-- 为大表创建额外的复合索引
ALTER TABLE visits ADD INDEX idx_analytics_comprehensive (site_id, visit_date, device_type, search_engine_id, region_id, is_new_visitor);
ALTER TABLE page_views ADD INDEX idx_analytics_page_comprehensive (site_id, view_date, page_url_id, stay_duration, is_bounce);

-- 创建函数索引（MySQL 8.0+）
-- ALTER TABLE visits ADD INDEX idx_visit_month_func ((YEAR(visit_date) * 100 + MONTH(visit_date)));

-- ============================================================================
-- 添加外键约束（分区表不支持外键，所以在应用层维护数据完整性）
-- ============================================================================

-- 注意：由于visits表使用了分区，MySQL不支持外键约束
-- 数据完整性需要在应用层维护，以下是参考的外键关系：

/*
visits表的外键关系（应用层维护）：
- site_id -> sites(id)
- visitor_id -> visitors(id)
- session_id -> sessions(id)
- ip_id -> ip_addresses(id)
- user_agent_id -> user_agents(id)
- referer_domain_id -> referer_domains(id)
- referer_url_id -> page_urls(id)
- landing_page_id -> page_urls(id)
- exit_page_id -> page_urls(id)
- region_id -> regions(id)
- browser_id -> browsers(id)
- os_id -> operating_systems(id)
- search_engine_id -> search_engines(id)
- search_keyword_id -> keywords(id)
*/

-- 为非分区表添加外键约束
ALTER TABLE page_views ADD CONSTRAINT fk_page_views_site FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE;
ALTER TABLE page_views ADD CONSTRAINT fk_page_views_visitor FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE;
ALTER TABLE page_views ADD CONSTRAINT fk_page_views_session FOREIGN KEY (session_id) REFERENCES sessions(id);
ALTER TABLE page_views ADD CONSTRAINT fk_page_views_page_url FOREIGN KEY (page_url_id) REFERENCES page_urls(id);
ALTER TABLE page_views ADD CONSTRAINT fk_page_views_referer_url FOREIGN KEY (referer_url_id) REFERENCES page_urls(id);

ALTER TABLE online_visitors ADD CONSTRAINT fk_online_visitors_site FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE;
ALTER TABLE online_visitors ADD CONSTRAINT fk_online_visitors_session FOREIGN KEY (session_id) REFERENCES sessions(id);
ALTER TABLE online_visitors ADD CONSTRAINT fk_online_visitors_page FOREIGN KEY (current_page_id) REFERENCES page_urls(id);
ALTER TABLE online_visitors ADD CONSTRAINT fk_online_visitors_ip FOREIGN KEY (ip_id) REFERENCES ip_addresses(id);
ALTER TABLE online_visitors ADD CONSTRAINT fk_online_visitors_ua FOREIGN KEY (user_agent_id) REFERENCES user_agents(id);

ALTER TABLE events ADD CONSTRAINT fk_events_site FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE;
ALTER TABLE events ADD CONSTRAINT fk_events_visitor FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE;
ALTER TABLE events ADD CONSTRAINT fk_events_session FOREIGN KEY (session_id) REFERENCES sessions(id);
ALTER TABLE events ADD CONSTRAINT fk_events_page_url FOREIGN KEY (page_url_id) REFERENCES page_urls(id);

-- ============================================================================
-- 完成初始化
-- ============================================================================

-- 显示创建的表
SHOW TABLES;

-- 显示数据库状态
SELECT
    'Database initialization completed successfully!' as status,
    COUNT(*) as total_tables
FROM information_schema.tables
WHERE table_schema = 'web_stats';

-- 输出优化建议
SELECT
    'Optimization Tips:' as tip_category,
    'Remember to configure MySQL settings for big data performance' as tip_1,
    'Set innodb_buffer_pool_size to 70-80% of available RAM' as tip_2,
    'Enable query cache and optimize slow queries regularly' as tip_3,
    'Monitor partition pruning effectiveness' as tip_4;
