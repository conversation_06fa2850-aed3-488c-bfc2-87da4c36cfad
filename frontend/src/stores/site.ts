import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Site, SiteForm, TrackingCodeInfo, PaginationParams, PaginationResponse } from '@/types'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

export const useSiteStore = defineStore('site', () => {
  // 状态
  const sites = ref<Site[]>([])
  const currentSite = ref<Site | null>(null)
  const isLoading = ref(false)
  const total = ref(0)
  const pagination = ref({
    page: 1,
    page_size: 10
  })

  // 计算属性
  const siteOptions = computed(() => 
    sites.value.map(site => ({
      label: site.site_name,
      value: site.id
    }))
  )

  // 获取网站列表
  const fetchSites = async (params?: PaginationParams): Promise<void> => {
    try {
      isLoading.value = true
      const queryParams = {
        ...pagination.value,
        ...params
      }
      
      const response = await request.get<PaginationResponse<Site>>('/sites', queryParams)
      
      if (response.success) {
        sites.value = response.data.data
        total.value = response.data.total
        pagination.value.page = response.data.page
        pagination.value.page_size = response.data.page_size
      }
    } catch (error) {
      console.error('获取网站列表失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 获取网站详情
  const fetchSiteDetail = async (id: number): Promise<Site | null> => {
    try {
      isLoading.value = true
      const response = await request.get<Site>(`/sites/${id}`)
      
      if (response.success) {
        currentSite.value = response.data
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取网站详情失败:', error)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 创建网站
  const createSite = async (siteForm: SiteForm): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.post<Site>('/sites', siteForm)
      
      if (response.success) {
        ElMessage.success('网站创建成功')
        // 重新获取网站列表
        await fetchSites()
        return true
      }
      
      return false
    } catch (error) {
      console.error('创建网站失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 更新网站
  const updateSite = async (id: number, siteForm: Partial<SiteForm>): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.put<Site>(`/sites/${id}`, siteForm)
      
      if (response.success) {
        ElMessage.success('网站更新成功')
        
        // 更新本地数据
        const index = sites.value.findIndex(site => site.id === id)
        if (index !== -1) {
          sites.value[index] = { ...sites.value[index], ...response.data }
        }
        
        if (currentSite.value && currentSite.value.id === id) {
          currentSite.value = { ...currentSite.value, ...response.data }
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('更新网站失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 删除网站
  const deleteSite = async (id: number): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.delete(`/sites/${id}`)
      
      if (response.success) {
        ElMessage.success('网站删除成功')
        
        // 从本地数据中移除
        sites.value = sites.value.filter(site => site.id !== id)
        
        if (currentSite.value && currentSite.value.id === id) {
          currentSite.value = null
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('删除网站失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 获取跟踪代码
  const getTrackingCode = async (id: number): Promise<TrackingCodeInfo | null> => {
    try {
      isLoading.value = true
      const response = await request.get<TrackingCodeInfo>(`/sites/${id}/code`)
      
      if (response.success) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取跟踪代码失败:', error)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 重新生成跟踪代码
  const regenerateTrackingCode = async (id: number): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.post(`/sites/${id}/regenerate-code`)
      
      if (response.success) {
        ElMessage.success('跟踪代码重新生成成功')
        
        // 更新本地数据
        const index = sites.value.findIndex(site => site.id === id)
        if (index !== -1 && response.data.tracking_code) {
          sites.value[index].tracking_code = response.data.tracking_code
        }
        
        if (currentSite.value && currentSite.value.id === id && response.data.tracking_code) {
          currentSite.value.tracking_code = response.data.tracking_code
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('重新生成跟踪代码失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 切换网站状态
  const toggleSiteStatus = async (id: number): Promise<boolean> => {
    try {
      const site = sites.value.find(s => s.id === id)
      if (!site) return false
      
      const newStatus = site.status === 1 ? 0 : 1
      const success = await updateSite(id, { status: newStatus })
      
      if (success) {
        ElMessage.success(`网站已${newStatus === 1 ? '启用' : '禁用'}`)
      }
      
      return success
    } catch (error) {
      console.error('切换网站状态失败:', error)
      return false
    }
  }

  // 根据ID获取网站
  const getSiteById = (id: number): Site | undefined => {
    return sites.value.find(site => site.id === id)
  }

  // 搜索网站
  const searchSites = async (keyword: string): Promise<void> => {
    await fetchSites({ search: keyword, page: 1 })
  }

  // 重置搜索
  const resetSearch = async (): Promise<void> => {
    await fetchSites({ page: 1 })
  }

  // 设置分页
  const setPagination = (page: number, pageSize?: number): void => {
    pagination.value.page = page
    if (pageSize) {
      pagination.value.page_size = pageSize
    }
  }

  // 清空当前网站
  const clearCurrentSite = (): void => {
    currentSite.value = null
  }

  // 验证域名是否可用
  const validateDomain = async (domain: string, excludeId?: number): Promise<boolean> => {
    try {
      const response = await request.get('/sites/validate-domain', {
        domain,
        exclude_id: excludeId
      })
      
      return response.success && response.data.available
    } catch (error) {
      console.error('验证域名失败:', error)
      return false
    }
  }

  return {
    // 状态
    sites,
    currentSite,
    isLoading,
    total,
    pagination,

    // 计算属性
    siteOptions,

    // 方法
    fetchSites,
    fetchSiteDetail,
    createSite,
    updateSite,
    deleteSite,
    getTrackingCode,
    regenerateTrackingCode,
    toggleSiteStatus,
    getSiteById,
    searchSites,
    resetSearch,
    setPagination,
    clearCurrentSite,
    validateDomain
  }
})

// 在组件外使用时的辅助函数
export const getSiteStore = () => {
  return useSiteStore()
}
