import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  ApiService, 
  type Site, 
  type SiteCreateRequest, 
  type SiteUpdateRequest,
  type SiteStats,
  type DailyStats
} from '@/services/api'

export const useSitesStore = defineStore('sites', () => {
  // 状态
  const sites = ref<Site[]>([])
  const currentSite = ref<Site | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 获取网站列表
  const fetchSites = async (params?: {
    page?: number
    limit?: number
    search?: string
  }) => {
    loading.value = true
    try {
      const response = await ApiService.getSites(params)
      sites.value = response.sites
      pagination.value = response.pagination
    } catch (error) {
      console.error('Failed to fetch sites:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取单个网站
  const fetchSite = async (id: number) => {
    loading.value = true
    try {
      const response = await ApiService.getSite(id)
      currentSite.value = response.site
      return response.site
    } catch (error) {
      console.error('Failed to fetch site:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建网站
  const createSite = async (data: SiteCreateRequest) => {
    loading.value = true
    try {
      const response = await ApiService.createSite(data)
      sites.value.unshift(response.site)
      return response.site
    } catch (error) {
      console.error('Failed to create site:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新网站
  const updateSite = async (id: number, data: SiteUpdateRequest) => {
    loading.value = true
    try {
      const response = await ApiService.updateSite(id, data)
      const index = sites.value.findIndex(site => site.id === id)
      if (index !== -1) {
        sites.value[index] = response.site
      }
      if (currentSite.value?.id === id) {
        currentSite.value = response.site
      }
      return response.site
    } catch (error) {
      console.error('Failed to update site:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除网站
  const deleteSite = async (id: number) => {
    loading.value = true
    try {
      await ApiService.deleteSite(id)
      sites.value = sites.value.filter(site => site.id !== id)
      if (currentSite.value?.id === id) {
        currentSite.value = null
      }
    } catch (error) {
      console.error('Failed to delete site:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取跟踪代码
  const getTrackingCode = async (id: number) => {
    loading.value = true
    try {
      const response = await ApiService.getTrackingCode(id)
      return response
    } catch (error) {
      console.error('Failed to get tracking code:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重新生成跟踪代码
  const regenerateTrackingCode = async (id: number) => {
    loading.value = true
    try {
      const response = await ApiService.regenerateTrackingCode(id)
      // 更新本地网站数据
      const index = sites.value.findIndex(site => site.id === id)
      if (index !== -1) {
        sites.value[index].tracking_code = response.tracking_code
      }
      if (currentSite.value?.id === id) {
        currentSite.value.tracking_code = response.tracking_code
      }
      return response
    } catch (error) {
      console.error('Failed to regenerate tracking code:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取网站统计数据
  const getSiteStats = async (
    id: number,
    params?: {
      start_date?: string
      end_date?: string
    }
  ) => {
    loading.value = true
    try {
      const response = await ApiService.getSiteStats(id, params)
      return response
    } catch (error) {
      console.error('Failed to get site stats:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取每日统计数据
  const getDailyStats = async (
    id: number,
    params?: {
      start_date?: string
      end_date?: string
    }
  ) => {
    loading.value = true
    try {
      const response = await ApiService.getDailyStats(id, params)
      return response
    } catch (error) {
      console.error('Failed to get daily stats:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 清除当前网站
  const clearCurrentSite = () => {
    currentSite.value = null
  }

  return {
    // 状态
    sites,
    currentSite,
    loading,
    pagination,
    
    // 方法
    fetchSites,
    fetchSite,
    createSite,
    updateSite,
    deleteSite,
    getTrackingCode,
    regenerateTrackingCode,
    getSiteStats,
    getDailyStats,
    clearCurrentSite,
  }
})
