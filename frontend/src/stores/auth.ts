import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService, type LoginRequest } from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const user = ref<any>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 初始化用户信息
  const initUser = () => {
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      try {
        user.value = JSON.parse(userInfo)
      } catch (error) {
        console.error('Failed to parse user info:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await ApiService.login(credentials)
      
      // 保存token和用户信息
      token.value = response.token
      user.value = response.user
      
      localStorage.setItem('auth_token', response.token)
      localStorage.setItem('user_info', JSON.stringify(response.user))
      
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    loading.value = true
    try {
      await ApiService.logout()
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // 清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      loading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!token.value) return
    
    loading.value = true
    try {
      const response = await ApiService.getCurrentUser()
      user.value = response.user
      localStorage.setItem('user_info', JSON.stringify(response.user))
    } catch (error) {
      console.error('Failed to fetch current user:', error)
      // 如果获取用户信息失败，可能token已过期
      await logout()
    } finally {
      loading.value = false
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (token.value && !user.value) {
      await fetchCurrentUser()
    }
  }

  // 初始化
  initUser()

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout,
    fetchCurrentUser,
    checkAuth,
  }
})
