import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm, LoginResponse, UserPermissions } from '@/types'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)
  const permissions = ref<UserPermissions | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userRoles = computed(() => permissions.value?.roles || [])
  const userPermissions = computed(() => permissions.value?.permissions || [])

  // 登录
  const login = async (loginForm: LoginForm): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.post<LoginResponse>('/auth/login', loginForm)
      
      if (response.success) {
        const { token: newToken, user: userInfo } = response.data

        // 保存token和用户信息
        token.value = newToken
        user.value = userInfo
        localStorage.setItem('token', newToken)

        // 立即获取用户权限，确保状态完整
        try {
          await getUserPermissions()
        } catch (error) {
          console.warn('获取用户权限失败，但登录成功:', error)
        }

        ElMessage.success('登录成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.post('/auth/register', registerForm)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
        return true
      }
      
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      // 调用登出接口
      await request.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      user.value = null
      permissions.value = null
      localStorage.removeItem('token')
      ElMessage.success('已退出登录')
    }
  }

  // 获取用户信息
  const getUserInfo = async (): Promise<boolean> => {
    try {
      if (!token.value) return false

      const response = await request.get<User>('/auth/me')

      if (response.success) {
        user.value = response.data
        return true
      }

      return false
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 不要立即logout，可能只是网络问题
      return false
    }
  }

  // 获取用户权限
  const getUserPermissions = async (): Promise<void> => {
    try {
      if (!user.value) return
      
      const response = await request.get<UserPermissions>(`/users/${user.value.id}/permissions`)
      
      if (response.success) {
        permissions.value = response.data
      }
    } catch (error) {
      console.error('获取用户权限失败:', error)
    }
  }

  // 更新用户信息
  const updateUserInfo = async (userInfo: Partial<User>): Promise<boolean> => {
    try {
      if (!user.value) return false
      
      isLoading.value = true
      const response = await request.put<User>(`/users/${user.value.id}`, userInfo)
      
      if (response.success) {
        user.value = { ...user.value, ...response.data }
        ElMessage.success('用户信息更新成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await request.post('/auth/change-password', {
        old_password: oldPassword,
        new_password: newPassword
      })
      
      if (response.success) {
        ElMessage.success('密码修改成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!permissions.value) return false

    // 检查是否为管理员
    const isAdmin = userRoles.value.some(role => role === 'admin' || role.name === 'admin')
    if (isAdmin) return true

    // 检查具体权限 - 适配后端返回的字符串数组格式
    if (Array.isArray(permissions.value.permissions)) {
      return permissions.value.permissions.includes(permission)
    }

    // 兼容对象数组格式
    return userPermissions.value.some(p => p.name === permission)
  }

  // 检查角色
  const hasRole = (roleName: string): boolean => {
    // 适配后端返回的字符串数组格式
    if (Array.isArray(permissions.value?.roles)) {
      return permissions.value.roles.includes(roleName)
    }

    // 兼容对象数组格式
    return userRoles.value.some(role => role === roleName || role.name === roleName)
  }

  // 检查多个权限（任一满足）
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  // 检查多个权限（全部满足）
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  // 刷新token
  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await request.post<{ token: string }>('/auth/refresh')
      
      if (response.success) {
        token.value = response.data.token
        localStorage.setItem('token', response.data.token)
        return true
      }
      
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      return false
    }
  }

  // 初始化用户状态
  const initUserState = async (): Promise<boolean> => {
    if (!token.value) return false

    try {
      // 获取用户信息
      const userInfoSuccess = await getUserInfo()
      if (!userInfoSuccess) {
        console.warn('获取用户信息失败，但保持登录状态')
        return false
      }

      // 获取用户权限（不阻塞）
      getUserPermissions().catch(error => {
        console.warn('获取用户权限失败:', error)
      })

      return true
    } catch (error) {
      console.error('初始化用户状态失败:', error)
      return false
    }
  }

  return {
    // 状态
    token,
    user,
    permissions,
    isLoading,

    // 计算属性
    isLoggedIn,
    userRoles,
    userPermissions,

    // 方法
    login,
    register,
    logout,
    getUserInfo,
    getUserPermissions,
    updateUserInfo,
    changePassword,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    refreshToken,
    initUserState
  }
})

// 在组件外使用时的辅助函数
export const getUserStore = () => {
  return useUserStore()
}
