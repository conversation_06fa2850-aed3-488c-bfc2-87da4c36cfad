// 通用类型定义

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  code: number
}

// 分页响应类型
export interface PaginationResponse<T = any> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  page_size?: number
  search?: string
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  status: number
  created_at: string
  updated_at: string
}

export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirm_password: string
  nickname?: string
}

export interface LoginResponse {
  token: string
  user: User
  expires_in: number
}

// 网站相关类型
export interface Site {
  id: number
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region?: string
  description?: string
  tracking_code: string
  is_public: number
  status: number
  created_at: string
  updated_at: string
  total_visits?: number
  total_page_views?: number
  last_visit?: string
}

export interface SiteForm {
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region?: string
  description?: string
  view_password?: string
  is_public?: number
  status?: number
}

export interface TrackingCodeInfo {
  tracking_code: string
  js_code: string
  install_guide: {
    step1: string
    step2: string
    step3: string
    step4: string
    notes: string[]
    examples: {
      html: string
      wordpress: string
    }
  }
}

// 统计数据类型
export interface StatsOverview {
  site_id: number
  start_date: string
  end_date: string
  pv: number
  uv: number
  ip_count: number
  session_count: number
  new_visitors: number
  returning_visitors: number
  bounce_count: number
  bounce_rate: number
  avg_duration: number
  avg_page_views: number
  desktop_pv: number
  mobile_pv: number
  tablet_pv: number
  search_pv: number
  direct_pv: number
  external_pv: number
}

export interface TrendData {
  date: string
  pv: number
  uv: number
  session_count: number
  bounce_rate: number
  avg_duration: number
}

export interface RealtimeStats {
  site_id: number
  online_count: number
  pv_5min: number
  uv_5min: number
  last_update: string
  recent_visitors: RecentVisitor[]
  top_pages: TopPage[]
}

export interface RecentVisitor {
  visitor_id: string
  page_url: string
  page_title: string
  referrer: string
  location: string
  device_type: string
  browser: string
  visit_time: string
}

export interface TopPage {
  page_url: string
  page_title: string
  pv_5min: number
  uv_5min: number
}

// 权限相关类型
export interface Role {
  id: number
  name: string
  display_name: string
  description: string
  is_system: boolean
  status: number
  created_at: string
  updated_at: string
  permissions?: Permission[]
  user_count?: number
}

export interface Permission {
  id: number
  name: string
  display_name: string
  description: string
  resource: string
  action: string
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface UserPermissions {
  user_id: number
  username: string
  roles: Role[]
  permissions: Permission[]
}

// 菜单类型
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permission?: string
  hidden?: boolean
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
    fill?: boolean
  }[]
}

// 表格列定义
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 表单验证规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 操作日志类型
export interface OperationLog {
  id: number
  user_id: number
  username: string
  action: string
  resource: string
  resource_id?: number
  method: string
  path: string
  ip: string
  user_agent: string
  request?: string
  response?: string
  status: number
  duration: number
  created_at: string
}

// 系统配置类型
export interface SystemConfig {
  app_name: string
  app_version: string
  api_version: string
  timezone: string
  language: string
  theme: 'light' | 'dark' | 'auto'
}

// 导出数据类型
export interface ExportParams {
  site_id: number
  start_date: string
  end_date: string
  format: 'csv' | 'excel' | 'json'
  fields?: string[]
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}

// 路由元信息
export interface RouteMeta {
  title?: string
  icon?: string
  permission?: string
  hidden?: boolean
  keepAlive?: boolean
  breadcrumb?: boolean
}
