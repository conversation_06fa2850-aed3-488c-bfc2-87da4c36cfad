<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            :icon="isCollapsed ? 'Expand' : 'Fold'"
            @click="toggleSidebar"
            text
            class="sidebar-toggle"
          />
          <h1 class="app-title">WebStats</h1>
        </div>

        <!-- 中间的网站选择器 -->
        <div class="header-center">
          <el-select
            v-model="currentSiteId"
            placeholder="选择网站"
            class="site-selector"
            @change="handleSiteChange"
            size="default"
          >
            <el-option
              v-for="site in sites"
              :key="site.id"
              :label="site.site_name"
              :value="site.id"
            />
          </el-select>
        </div>

        <div class="header-right">
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userStore.user?.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userStore.user?.nickname || userStore.user?.username }}</span>
              <el-icon class="arrow-down"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-container class="layout-main">
      <!-- 侧边栏 -->
      <el-aside :width="isCollapsed ? '64px' : '200px'" class="layout-sidebar">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="false"
          router
          class="sidebar-menu"
        >
          <!-- 用户首页 -->
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <template #title>用户首页</template>
          </el-menu-item>

          <!-- 网站管理 -->
          <el-sub-menu index="sites">
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>网站管理</span>
            </template>
            <el-menu-item index="/sites">网站列表</el-menu-item>
            <el-menu-item index="/sites/create">添加网站</el-menu-item>
          </el-sub-menu>

          <!-- 网站概况 -->
          <el-menu-item index="/overview">
            <el-icon><DataBoard /></el-icon>
            <template #title>网站概况</template>
          </el-menu-item>

          <!-- 流量分析 -->
          <el-sub-menu index="traffic">
            <template #title>
              <el-icon><TrendCharts /></el-icon>
              <span>流量分析</span>
            </template>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/trend` : '/stats/trend'">趋势分析</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/compare` : '/stats/compare'">对比分析</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/realtime` : '/stats/realtime'">当前在线</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visits` : '/stats/visits'">访问明细</el-menu-item>
          </el-sub-menu>

          <!-- 来源分析 -->
          <el-sub-menu index="source">
            <template #title>
              <el-icon><Link /></el-icon>
              <span>来源分析</span>
            </template>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/all` : '/stats/source/all'">全部来源</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/search` : '/stats/source/search'">搜索分析</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/keywords` : '/stats/source/keywords'">搜索词</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/recent` : '/stats/source/recent'">最近搜索</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/domains` : '/stats/source/domains'">来路域名</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/pages` : '/stats/source/pages'">来路页面</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/source/ranking` : '/stats/source/ranking'">来源升降榜</el-menu-item>
          </el-sub-menu>

          <!-- 受访分析 -->
          <el-sub-menu index="pages">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>受访分析</span>
            </template>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/pages/domains` : '/stats/pages/domains'">受访域名</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/pages/list` : '/stats/pages/list'">受访页面</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/pages/ranking` : '/stats/pages/ranking'">受访升降榜</el-menu-item>
          </el-sub-menu>

          <!-- 访客分析 -->
          <el-sub-menu index="visitors">
            <template #title>
              <el-icon><UserFilled /></el-icon>
              <span>访客分析</span>
            </template>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visitors/regions` : '/stats/visitors/regions'">地区分布</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visitors/system` : '/stats/visitors/system'">系统环境</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visitors/new-old` : '/stats/visitors/new-old'">新老访客</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visitors/loyalty` : '/stats/visitors/loyalty'">忠诚度</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/stats/${currentSiteId}/visitors/activity` : '/stats/visitors/activity'">活跃度</el-menu-item>
          </el-sub-menu>

          <!-- 设置 -->
          <el-sub-menu index="settings">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>设置</span>
            </template>
            <el-menu-item :index="currentSiteId ? `/settings/${currentSiteId}/profile` : '/settings/profile'">站点资料</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/settings/${currentSiteId}/code` : '/settings/code'">获取代码</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/settings/${currentSiteId}/exclude` : '/settings/exclude'">排除设置</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/settings/${currentSiteId}/password` : '/settings/password'">查看密码</el-menu-item>
            <el-menu-item :index="currentSiteId ? `/settings/${currentSiteId}/status` : '/settings/status'">关闭统计</el-menu-item>
          </el-sub-menu>

          <!-- 个人设置 -->
          <el-menu-item index="/profile">
            <el-icon><User /></el-icon>
            <template #title>个人设置</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="layout-content" :class="{ 'full-width-layout': route.meta?.fullWidth }">
        <!-- 面包屑导航 -->
        <el-breadcrumb class="breadcrumb" v-if="showBreadcrumb && !route.meta?.fullWidth">
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            :key="item.path"
            :to="item.path"
          >
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 页面内容 -->
        <div class="page-content" :class="{ 'full-width': route.meta?.fullWidth }">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSiteStore } from '@/stores/site'
import { ElMessageBox } from 'element-plus'
import {
  Odometer,
  Monitor,
  TrendCharts,
  User,
  Setting,
  ArrowDown,
  DataBoard,
  Link,
  Document,
  UserFilled
} from '@element-plus/icons-vue'
import type { Site } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const siteStore = useSiteStore()

// 响应式数据
const isCollapsed = ref(false)

// 计算属性
const activeMenu = computed(() => route.path)
const sites = computed(() => siteStore.sites)
const currentSiteId = computed({
  get: () => siteStore.currentSiteId,
  set: (value) => siteStore.setCurrentSiteId(value)
})
const showBreadcrumb = computed(() => route.meta.breadcrumb !== false)

const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbs = matched.map(item => ({
    title: item.meta.title as string,
    path: item.path
  }))
  
  // 如果是统计页面，添加网站名称
  if (route.path.includes('/stats/') && currentSiteId.value) {
    const site = siteStore.sites.find(s => s.id === currentSiteId.value)
    if (site) {
      breadcrumbs.splice(1, 0, {
        title: site.site_name,
        path: `/sites`
      })
    }
  }
  
  return breadcrumbs
})

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleSiteChange = (siteId: number) => {
  console.log('🔄 网站选择变化:', siteId)
  siteStore.setCurrentSite(siteId)

  // 如果当前在统计页面，更新路由
  if (route.path.includes('/stats/')) {
    const newPath = route.path.replace(/\/stats\/\d+/, `/stats/${siteId}`)
    router.push(newPath)
  }

  // 保存到本地存储
  localStorage.setItem('currentSiteId', siteId.toString())
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      // TODO: 实现设置页面
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 从路由中提取网站ID
    const match = newPath.match(/\/stats\/(\d+)/)
    if (match) {
      const siteId = parseInt(match[1])
      if (siteId !== currentSiteId.value) {
        currentSiteId.value = siteId
      }
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(async () => {
  // 加载网站列表
  await siteStore.fetchSites()
  console.log('Loaded sites:', sites.value)

  // 恢复上次选择的网站
  const savedSiteId = localStorage.getItem('currentSiteId')
  if (savedSiteId && sites.value.some(s => s.id === parseInt(savedSiteId))) {
    siteStore.setCurrentSite(parseInt(savedSiteId))
  } else if (sites.value.length > 0) {
    siteStore.setCurrentSite(sites.value[0].id)
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  max-width: none;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  font-size: 18px;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.site-selector {
  width: 200px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}

.arrow-down {
  font-size: 12px;
  color: #909399;
}

.layout-main {
  flex: 1;
  overflow: hidden;
  width: 100%;
  max-width: none;
}

.layout-sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.layout-content {
  background: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
}

.layout-content.full-width-layout {
  padding: 0;
  background: #f5f5f5;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-content {
  background: #fff;
  border-radius: 4px;
  min-height: calc(100vh - 140px);
}

/* 全宽页面样式 */
.page-content.full-width {
  background: transparent;
  border-radius: 0;
  padding: 0;
  margin: 0;
  width: 100%;
  max-width: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .header-left .app-title {
    display: none;
  }

  .site-selector {
    width: 150px;
  }

  .username {
    display: none;
  }
  
  .layout-content {
    padding: 16px;
  }
}
</style>
