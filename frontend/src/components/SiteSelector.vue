<template>
  <div class="site-selector">
    <el-select
      v-model="selectedSiteId"
      placeholder="选择网站"
      filterable
      clearable
      @change="handleSiteChange"
      style="width: 300px"
    >
      <el-option
        v-for="site in sites"
        :key="site.id"
        :label="`${site.site_name} (${site.domain})`"
        :value="site.id"
      >
        <div class="site-option">
          <div class="site-name">{{ site.site_name }}</div>
          <div class="site-domain">{{ site.domain }}</div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSiteStore } from '@/stores/site'

const route = useRoute()
const router = useRouter()
const siteStore = useSiteStore()

// 响应式数据
const selectedSiteId = ref<number | null>(null)

// 计算属性
const sites = computed(() => siteStore.sites)

// 方法
const handleSiteChange = (siteId: number | null) => {
  if (siteId) {
    // 保存选中的网站ID到store
    siteStore.setCurrentSite(siteId)
    
    // 如果当前在需要网站ID的路由上，更新路由参数
    const currentPath = route.path
    if (currentPath.includes('/stats/') || currentPath.includes('/settings/')) {
      const newPath = currentPath.replace(/\/\d+/, `/${siteId}`)
      router.push(newPath)
    } else if (currentPath === '/overview') {
      // 如果在概况页面，保持当前页面
      return
    }
  } else {
    // 清除选中的网站
    siteStore.setCurrentSite(null)
    // 跳转到网站列表
    router.push('/sites')
  }
}

const loadSites = async () => {
  try {
    await siteStore.fetchSites()
    
    // 如果路由中有网站ID，设置为选中状态
    const routeSiteId = route.params.siteId as string
    if (routeSiteId && !isNaN(Number(routeSiteId))) {
      selectedSiteId.value = Number(routeSiteId)
      siteStore.setCurrentSite(Number(routeSiteId))
    } else if (siteStore.currentSiteId) {
      selectedSiteId.value = siteStore.currentSiteId
    }
  } catch (error) {
    console.error('加载网站列表失败:', error)
  }
}

// 监听路由变化
watch(() => route.params.siteId, (newSiteId) => {
  if (newSiteId && !isNaN(Number(newSiteId))) {
    selectedSiteId.value = Number(newSiteId)
    siteStore.setCurrentSite(Number(newSiteId))
  }
})

// 监听store中的当前网站变化
watch(() => siteStore.currentSiteId, (newSiteId) => {
  selectedSiteId.value = newSiteId
})

// 组件挂载时加载数据
onMounted(() => {
  loadSites()
})
</script>

<style scoped>
.site-selector {
  margin-bottom: 20px;
}

.site-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.site-name {
  font-weight: 500;
  color: #303133;
}

.site-domain {
  font-size: 12px;
  color: #909399;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
}
</style>
