<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑网站' : '添加网站'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="网站名称" prop="site_name">
        <el-input
          v-model="form.site_name"
          placeholder="请输入网站名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="域名" prop="domain">
        <el-input
          v-model="form.domain"
          placeholder="例如：example.com"
          maxlength="100"
        />
      </el-form-item>
      
      <el-form-item label="网站URL" prop="site_url">
        <el-input
          v-model="form.site_url"
          placeholder="例如：https://example.com"
          maxlength="200"
        />
      </el-form-item>
      
      <el-form-item label="网站类型" prop="site_type">
        <el-select v-model="form.site_type" placeholder="请选择网站类型" style="width: 100%">
          <el-option label="博客" value="blog" />
          <el-option label="企业官网" value="corporate" />
          <el-option label="电商网站" value="ecommerce" />
          <el-option label="新闻网站" value="news" />
          <el-option label="论坛" value="forum" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="地区" prop="region">
        <el-select v-model="form.region" placeholder="请选择地区" style="width: 100%">
          <el-option label="中国大陆" value="cn" />
          <el-option label="香港" value="hk" />
          <el-option label="台湾" value="tw" />
          <el-option label="美国" value="us" />
          <el-option label="欧洲" value="eu" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入网站描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="查看密码">
        <el-input
          v-model="form.view_password"
          type="password"
          placeholder="设置查看统计数据的密码（可选）"
          maxlength="50"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="公开统计">
        <el-switch
          v-model="form.is_public"
          active-text="是"
          inactive-text="否"
        />
        <div class="form-tip">
          开启后，其他人可以通过链接查看统计数据
        </div>
      </el-form-item>
      
      <el-form-item v-if="isEdit" label="状态">
        <el-switch
          v-model="statusActive"
          active-text="正常"
          inactive-text="停用"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="sitesStore.loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useSitesStore } from '@/stores/sites'
import type { Site, SiteCreateRequest, SiteUpdateRequest } from '@/services/api'

// Props
interface Props {
  modelValue: boolean
  site?: Site | null
}

const props = withDefaults(defineProps<Props>(), {
  site: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const sitesStore = useSitesStore()

// 响应式数据
const formRef = ref<FormInstance>()
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.site)
const statusActive = ref(true)

// 表单数据
const form = reactive<SiteCreateRequest & { status?: number }>({
  site_name: '',
  domain: '',
  site_url: '',
  site_type: '',
  region: '',
  description: '',
  view_password: '',
  is_public: false,
  status: 1
})

// 表单验证规则
const rules: FormRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 100, message: '网站名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请输入域名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      message: '请输入有效的域名',
      trigger: 'blur'
    }
  ],
  site_url: [
    { required: true, message: '请输入网站URL', trigger: 'blur' },
    {
      pattern: /^https?:\/\/.+/,
      message: '请输入有效的URL（以http://或https://开头）',
      trigger: 'blur'
    }
  ],
  site_type: [
    { required: true, message: '请选择网站类型', trigger: 'change' }
  ]
}

// 监听site变化，填充表单
watch(() => props.site, (site) => {
  if (site) {
    Object.assign(form, {
      site_name: site.site_name,
      domain: site.domain,
      site_url: site.site_url,
      site_type: site.site_type,
      region: site.region,
      description: site.description,
      view_password: site.view_password,
      is_public: site.is_public,
      status: site.status
    })
    statusActive.value = site.status === 1
  }
}, { immediate: true })

// 监听状态开关变化
watch(statusActive, (value) => {
  form.status = value ? 1 : 0
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    site_name: '',
    domain: '',
    site_url: '',
    site_type: '',
    region: '',
    description: '',
    view_password: '',
    is_public: false,
    status: 1
  })
  statusActive.value = true
  formRef.value?.resetFields()
}

// 处理关闭
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (isEdit.value && props.site) {
      // 更新网站
      const updateData: SiteUpdateRequest = { ...form }
      await sitesStore.updateSite(props.site.id, updateData)
      ElMessage.success('网站更新成功')
    } else {
      // 创建网站
      const createData: SiteCreateRequest = { ...form }
      delete createData.status // 创建时不需要status字段
      await sitesStore.createSite(createData)
      ElMessage.success('网站创建成功')
    }
    
    emit('success')
  } catch (error: any) {
    console.error('Form submit error:', error)
    
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}
</style>
