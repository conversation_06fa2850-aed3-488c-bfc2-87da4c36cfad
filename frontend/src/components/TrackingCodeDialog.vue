<template>
  <el-dialog
    v-model="dialogVisible"
    title="跟踪代码"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="site" class="tracking-content">
      <!-- 网站信息 -->
      <div class="site-info">
        <h3>{{ site.site_name }}</h3>
        <p>{{ site.domain }}</p>
      </div>
      
      <!-- 跟踪代码信息 -->
      <div class="tracking-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="跟踪代码">
            <el-tag type="primary">{{ site.tracking_code }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作">
            <el-button
              type="warning"
              size="small"
              :loading="regenerating"
              @click="regenerateCode"
            >
              重新生成
            </el-button>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 安装说明 -->
      <div class="install-guide">
        <h4>安装说明</h4>
        <el-steps :active="1" direction="vertical">
          <el-step title="复制跟踪代码" description="复制下方的JavaScript代码" />
          <el-step title="添加到网站" description="将代码粘贴到网站的 </head> 标签之前" />
          <el-step title="验证安装" description="访问网站页面，检查统计数据是否正常收集" />
        </el-steps>
      </div>
      
      <!-- JavaScript跟踪代码 -->
      <div class="code-section">
        <div class="code-header">
          <h4>JavaScript跟踪代码</h4>
          <el-button
            type="primary"
            size="small"
            :icon="DocumentCopy"
            @click="copyCode"
          >
            复制代码
          </el-button>
        </div>
        
        <div class="code-container">
          <pre ref="codeRef" class="code-block">{{ trackingScript }}</pre>
        </div>
      </div>
      
      <!-- 注意事项 -->
      <div class="notice">
        <el-alert
          title="注意事项"
          type="info"
          :closable="false"
        >
          <ul>
            <li>请将跟踪代码添加到网站的每个页面中</li>
            <li>建议将代码放在 &lt;/head&gt; 标签之前，以确保正确加载</li>
            <li>跟踪代码安装后，通常需要几分钟才能看到统计数据</li>
            <li>如果需要重新生成跟踪代码，旧代码将失效</li>
          </ul>
        </el-alert>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { useSitesStore } from '@/stores/sites'
import type { Site } from '@/services/api'

// Props
interface Props {
  modelValue: boolean
  site?: Site | null
}

const props = withDefaults(defineProps<Props>(), {
  site: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const sitesStore = useSitesStore()

// 响应式数据
const codeRef = ref<HTMLElement>()
const regenerating = ref(false)
const trackingScript = ref('')

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 生成跟踪脚本
const generateTrackingScript = (trackingCode: string) => {
  return `<!-- 网站统计代码 -->
<script>
(function() {
  var script = document.createElement('script');
  script.src = 'http://localhost:9090/js/stats.js';
  script.async = true;
  script.setAttribute('data-site-id', '${trackingCode}');
  var firstScript = document.getElementsByTagName('script')[0];
  firstScript.parentNode.insertBefore(script, firstScript);
})();
</script>
<!-- 网站统计代码结束 -->`
}

// 监听site变化
watch(() => props.site, (site) => {
  if (site) {
    trackingScript.value = generateTrackingScript(site.tracking_code)
  }
}, { immediate: true })

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(trackingScript.value)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    // 降级方案
    try {
      const textArea = document.createElement('textarea')
      textArea.value = trackingScript.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('代码已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制代码')
    }
  }
}

// 重新生成跟踪代码
const regenerateCode = async () => {
  if (!props.site) return
  
  regenerating.value = true
  try {
    const response = await sitesStore.regenerateTrackingCode(props.site.id)
    trackingScript.value = generateTrackingScript(response.tracking_code)
    ElMessage.success('跟踪代码已重新生成')
  } catch (error) {
    ElMessage.error('重新生成失败')
  } finally {
    regenerating.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.tracking-content {
  max-height: 70vh;
  overflow-y: auto;
}

.site-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.site-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.tracking-info {
  margin-bottom: 24px;
}

.install-guide {
  margin-bottom: 24px;
}

.install-guide h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.code-section {
  margin-bottom: 24px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.code-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.code-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.code-block {
  margin: 0;
  padding: 16px;
  background: #f8f9fa;
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  overflow-x: auto;
}

.notice {
  margin-bottom: 16px;
}

.notice ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.notice li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-step__description) {
  font-size: 12px;
}

:deep(.el-alert__content) {
  padding-right: 0;
}
</style>
