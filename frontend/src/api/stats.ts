import request from '@/utils/request'

// 统计数据相关接口

// 获取网站概况数据
export interface OverviewData {
  pv: number
  pv_change: number
  uv: number
  uv_change: number
  ip: number
  ip_change: number
  online: number
  bounce_rate: number
  avg_duration: number
  new_visitors: number
  returning_visitors: number
}

export const getOverviewData = (siteId: number, params?: any) => {
  return request.get<OverviewData>(`/api/v1/stats/${siteId}/overview`, { params })
}

// 获取趋势数据
export interface TrendDataItem {
  date: string
  pv: number
  uv: number
  ip: number
  bounce_rate: number
  avg_duration: number
  new_visitors: number
  returning_visitors: number
}

export interface TrendDataParams {
  start_date: string
  end_date: string
  data_type?: 'pv' | 'uv' | 'ip' | 'bounce_rate' | 'avg_duration'
  granularity?: 'hour' | 'day' | 'week' | 'month'
  page?: number
  page_size?: number
}

export const getTrendData = (siteId: number, params: TrendDataParams) => {
  return request.get<{
    data: TrendDataItem[]
    total: number
    page: number
    page_size: number
  }>(`/api/v1/stats/${siteId}/trend`, { params })
}

// 获取实时在线数据
export interface RealtimeData {
  online: number
  recent_30min: {
    time: string
    pv: number
    uv: number
  }[]
  current_pages: {
    url: string
    title: string
    visitors: number
  }[]
}

export const getRealtimeData = (siteId: number) => {
  return request.get<RealtimeData>(`/api/v1/stats/${siteId}/realtime`)
}

// 获取来源分析数据
export interface SourceDataItem {
  source: string
  source_type: 'search' | 'direct' | 'referral' | 'social'
  pv: number
  uv: number
  percentage: number
}

export const getSourceData = (siteId: number, params?: any) => {
  return request.get<{
    data: SourceDataItem[]
    total: number
  }>(`/api/v1/stats/${siteId}/source`, { params })
}

// 获取搜索词数据
export interface KeywordDataItem {
  keyword: string
  search_engine: string
  pv: number
  uv: number
  rank_change: number
}

export const getKeywordData = (siteId: number, params?: any) => {
  return request.get<{
    data: KeywordDataItem[]
    total: number
  }>(`/api/v1/stats/${siteId}/keywords`, { params })
}

// 获取受访页面数据
export interface PageDataItem {
  url: string
  title: string
  pv: number
  uv: number
  avg_duration: number
  bounce_rate: number
  exit_rate: number
}

export const getPageData = (siteId: number, params?: any) => {
  return request.get<{
    data: PageDataItem[]
    total: number
  }>(`/api/v1/stats/${siteId}/pages`, { params })
}

// 获取访客地区分布数据
export interface RegionDataItem {
  region: string
  pv: number
  uv: number
  percentage: number
}

export const getRegionData = (siteId: number, params?: any) => {
  return request.get<{
    data: RegionDataItem[]
    total: number
  }>(`/api/v1/stats/${siteId}/regions`, { params })
}

// 获取系统环境数据
export interface SystemDataItem {
  type: 'os' | 'browser' | 'device'
  name: string
  version?: string
  pv: number
  uv: number
  percentage: number
}

export const getSystemData = (siteId: number, params?: any) => {
  return request.get<{
    data: SystemDataItem[]
    total: number
  }>(`/api/v1/stats/${siteId}/system`, { params })
}

// 获取新老访客数据
export interface VisitorTypeData {
  new_visitors: {
    count: number
    percentage: number
    avg_duration: number
    bounce_rate: number
  }
  returning_visitors: {
    count: number
    percentage: number
    avg_duration: number
    bounce_rate: number
  }
}

export const getVisitorTypeData = (siteId: number, params?: any) => {
  return request.get<VisitorTypeData>(`/api/v1/stats/${siteId}/visitor-type`, { params })
}

// 获取访客忠诚度数据
export interface LoyaltyDataItem {
  visit_count: string // "1", "2-5", "6-10", "11-20", "21+"
  visitors: number
  percentage: number
}

export const getLoyaltyData = (siteId: number, params?: any) => {
  return request.get<{
    data: LoyaltyDataItem[]
  }>(`/api/v1/stats/${siteId}/loyalty`, { params })
}

// 获取访客活跃度数据
export interface ActivityDataItem {
  duration_range: string // "0-10s", "11-30s", "31-60s", "1-3min", "3-10min", "10min+"
  visitors: number
  percentage: number
}

export const getActivityData = (siteId: number, params?: any) => {
  return request.get<{
    data: ActivityDataItem[]
  }>(`/api/v1/stats/${siteId}/activity`, { params })
}

// 导出数据
export const exportStatsData = (siteId: number, type: string, params?: any) => {
  return request.get(`/api/v1/stats/${siteId}/export/${type}`, { 
    params,
    responseType: 'blob'
  })
}
