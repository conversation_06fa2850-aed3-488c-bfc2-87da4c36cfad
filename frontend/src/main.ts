import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import '@/assets/styles/index.css'
import { useUserStore } from '@/stores/user'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 初始化用户状态
const initApp = async () => {
  const userStore = useUserStore()

  // 如果有token，尝试获取用户信息
  if (userStore.token) {
    console.log('应用启动时发现token，初始化用户状态')
    await userStore.initUserState()
  }

  app.mount('#app')
}

initApp()
