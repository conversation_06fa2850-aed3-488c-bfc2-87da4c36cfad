import axios, { type AxiosInstance, type AxiosResponse } from 'axios'

// API基础配置
const API_BASE_URL = 'http://localhost:9090/api'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 类型定义
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: {
    id: number
    username: string
    email: string
  }
}

export interface Site {
  id: number
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region: string
  description: string
  tracking_code: string
  view_password: string
  is_public: boolean
  status: number
  created_at: string
  updated_at: string
}

export interface SiteCreateRequest {
  site_name: string
  domain: string
  site_url: string
  site_type: string
  region?: string
  description?: string
  view_password?: string
  is_public?: boolean
}

export interface SiteUpdateRequest {
  site_name?: string
  domain?: string
  site_url?: string
  site_type?: string
  region?: string
  description?: string
  view_password?: string
  is_public?: boolean
  status?: number
}

export interface SiteStats {
  pv: number
  uv: number
  ip_count: number
  new_visitors: number
  return_visitors: number
  bounce_rate: number
  avg_duration: number
}

export interface DailyStats {
  date: string
  pv: number
  uv: number
  ip_count: number
  new_visitors: number
  returning_visitors: number
  avg_duration: number
  bounce_rate: number
}

export interface PaginationResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// API服务类
export class ApiService {
  // 认证相关
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', data)
    return response.data
  }

  static async logout(): Promise<void> {
    await apiClient.post('/auth/logout')
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }

  static async getCurrentUser(): Promise<any> {
    const response = await apiClient.get('/auth/me')
    return response.data
  }

  // 网站管理
  static async getSites(params?: {
    page?: number
    limit?: number
    search?: string
  }): Promise<{ sites: Site[]; pagination: any }> {
    const response = await apiClient.get('/sites', { params })
    return response.data
  }

  static async getSite(id: number): Promise<{ site: Site }> {
    const response = await apiClient.get(`/sites/${id}`)
    return response.data
  }

  static async createSite(data: SiteCreateRequest): Promise<{ site: Site }> {
    const response = await apiClient.post('/sites', data)
    return response.data
  }

  static async updateSite(id: number, data: SiteUpdateRequest): Promise<{ site: Site }> {
    const response = await apiClient.put(`/sites/${id}`, data)
    return response.data
  }

  static async deleteSite(id: number): Promise<void> {
    await apiClient.delete(`/sites/${id}`)
  }

  static async getTrackingCode(id: number): Promise<{
    site_id: number
    site_name: string
    tracking_code: string
    tracking_script: string
  }> {
    const response = await apiClient.get(`/sites/${id}/tracking-code`)
    return response.data
  }

  static async regenerateTrackingCode(id: number): Promise<{
    message: string
    tracking_code: string
  }> {
    const response = await apiClient.post(`/sites/${id}/regenerate-code`)
    return response.data
  }

  // 统计数据
  static async getSiteStats(
    id: number,
    params?: {
      start_date?: string
      end_date?: string
    }
  ): Promise<{
    site_id: number
    start_date: string
    end_date: string
    stats: SiteStats
  }> {
    const response = await apiClient.get(`/analytics/sites/${id}/stats`, { params })
    return response.data
  }

  static async getDailyStats(
    id: number,
    params?: {
      start_date?: string
      end_date?: string
    }
  ): Promise<{
    site_id: number
    start_date: string
    end_date: string
    daily_stats: DailyStats[]
  }> {
    const response = await apiClient.get(`/analytics/sites/${id}/daily`, { params })
    return response.data
  }
}

export default ApiService
