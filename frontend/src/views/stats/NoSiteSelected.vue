<template>
  <div class="no-site-selected">
    <div class="empty-state">
      <el-icon class="empty-icon"><Monitor /></el-icon>
      <h3>请先选择一个网站</h3>
      <p>您需要先选择一个网站才能查看统计数据</p>
      
      <div class="actions">
        <el-button type="primary" @click="goToSites">
          <el-icon><Plus /></el-icon>
          管理网站
        </el-button>
        
        <div v-if="sites.length > 0" class="site-selector-wrapper">
          <p>或者直接选择一个网站：</p>
          <el-select
            v-model="selectedSiteId"
            placeholder="选择网站"
            @change="handleSiteSelect"
            style="width: 300px"
          >
            <el-option
              v-for="site in sites"
              :key="site.id"
              :label="`${site.site_name} (${site.domain})`"
              :value="site.id"
            >
              <div class="site-option">
                <div class="site-name">{{ site.site_name }}</div>
                <div class="site-domain">{{ site.domain }}</div>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { Monitor, Plus } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const siteStore = useSiteStore()

const selectedSiteId = ref<number | null>(null)

// 计算属性
const sites = computed(() => siteStore.sites)

// 方法
const goToSites = () => {
  router.push('/sites')
}

const handleSiteSelect = (siteId: number) => {
  if (siteId) {
    siteStore.setCurrentSite(siteId)
    
    // 根据当前路径构建新的路径
    const currentPath = route.path
    let newPath = currentPath
    
    // 将不带网站ID的路径转换为带网站ID的路径
    if (currentPath.startsWith('/stats/')) {
      const pathParts = currentPath.split('/')
      if (pathParts.length >= 3) {
        // /stats/trend -> /stats/1/trend
        newPath = `/stats/${siteId}/${pathParts.slice(2).join('/')}`
      }
    }
    
    router.push(newPath)
  }
}

const loadSites = async () => {
  try {
    await siteStore.fetchSites()
  } catch (error) {
    console.error('加载网站列表失败:', error)
  }
}

// 组件挂载时加载网站列表
onMounted(() => {
  loadSites()
})
</script>

<style scoped>
.no-site-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px;
}

.empty-state {
  text-align: center;
  max-width: 500px;
}

.empty-icon {
  font-size: 80px;
  color: #dcdfe6;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.empty-state p {
  margin: 0 0 30px 0;
  color: #909399;
  font-size: 14px;
}

.actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.site-selector-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.site-selector-wrapper p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.site-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.site-name {
  font-weight: 500;
  color: #303133;
}

.site-domain {
  font-size: 12px;
  color: #909399;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
}
</style>
