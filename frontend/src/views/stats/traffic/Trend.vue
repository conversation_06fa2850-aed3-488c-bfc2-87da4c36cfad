<template>
  <div class="trend-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>趋势分析</h2>
      <p>查看网站访问量的时间趋势变化</p>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="loadTrendData"
            />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="filterForm.dataType" @change="loadTrendData">
              <el-option label="页面浏览量(PV)" value="pv" />
              <el-option label="独立访客(UV)" value="uv" />
              <el-option label="独立IP" value="ip" />
              <el-option label="跳出率" value="bounce_rate" />
              <el-option label="平均访问时长" value="avg_duration" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间粒度">
            <el-select v-model="filterForm.granularity" @change="loadTrendData">
              <el-option label="按小时" value="hour" />
              <el-option label="按天" value="day" />
              <el-option label="按周" value="week" />
              <el-option label="按月" value="month" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadTrendData">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 趋势图表 -->
    <div class="chart-section">
      <el-card>
        <template #header>
          <div class="chart-header">
            <span>{{ getDataTypeLabel(filterForm.dataType) }}趋势图</span>
            <div class="chart-tools">
              <el-button-group>
                <el-button size="small" @click="toggleChartType('line')" :type="chartType === 'line' ? 'primary' : ''">
                  <el-icon><TrendCharts /></el-icon>
                  折线图
                </el-button>
                <el-button size="small" @click="toggleChartType('bar')" :type="chartType === 'bar' ? 'primary' : ''">
                  <el-icon><Histogram /></el-icon>
                  柱状图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>
        <div class="chart-container" ref="chartContainer">
          <!-- 图表组件将在这里渲染 -->
          <div class="chart-placeholder" v-if="isLoading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>数据加载中...</p>
          </div>
          <div class="chart-placeholder" v-else-if="!trendData.length">
            <el-icon><TrendCharts /></el-icon>
            <p>暂无数据</p>
          </div>
          <div v-else class="chart-content">
            <!-- 这里将集成图表库 -->
            <p>图表组件开发中...</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据统计表格 -->
    <div class="data-table-section">
      <el-card>
        <template #header>
          <span>详细数据</span>
        </template>
        <el-table
          :data="trendData"
          v-loading="isLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="date" label="时间" width="150" />
          <el-table-column prop="pv" label="页面浏览量(PV)" align="right" />
          <el-table-column prop="uv" label="独立访客(UV)" align="right" />
          <el-table-column prop="ip" label="独立IP" align="right" />
          <el-table-column prop="bounce_rate" label="跳出率" align="right">
            <template #default="{ row }">
              {{ row.bounce_rate }}%
            </template>
          </el-table-column>
          <el-table-column prop="avg_duration" label="平均访问时长" align="right">
            <template #default="{ row }">
              {{ formatDuration(row.avg_duration) }}
            </template>
          </el-table-column>
          <el-table-column prop="new_visitors" label="新访客" align="right" />
          <el-table-column prop="returning_visitors" label="回访客" align="right" />
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { Search, Download, TrendCharts, Histogram, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const siteStore = useSiteStore()
const siteId = route.params.siteId as string

// 计算属性
const currentSite = computed(() => siteStore.currentSite)
const currentSiteId = computed(() => siteStore.currentSiteId)

// 响应式数据
const isLoading = ref(false)
const chartType = ref('line')
const chartContainer = ref()

const filterForm = reactive({
  dateRange: [
    dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ],
  dataType: 'pv',
  granularity: 'day'
})

const trendData = ref([
  {
    date: '2025-01-01',
    pv: 1250,
    uv: 890,
    ip: 756,
    bounce_rate: 45.2,
    avg_duration: 180,
    new_visitors: 234,
    returning_visitors: 656
  },
  {
    date: '2025-01-02',
    pv: 1380,
    uv: 920,
    ip: 812,
    bounce_rate: 42.8,
    avg_duration: 195,
    new_visitors: 267,
    returning_visitors: 653
  }
  // 更多模拟数据...
])

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 100
})

// 方法
const getDataTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    pv: '页面浏览量',
    uv: '独立访客',
    ip: '独立IP',
    bounce_rate: '跳出率',
    avg_duration: '平均访问时长'
  }
  return labels[type] || type
}

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const toggleChartType = (type: string) => {
  chartType.value = type
  // TODO: 重新渲染图表
}

const loadTrendData = async () => {
  if (!currentSiteId.value) {
    ElMessage.warning('请先选择一个网站')
    return
  }

  isLoading.value = true
  try {
    // TODO: 调用API获取趋势数据
    console.log('加载趋势数据:', {
      siteId: currentSiteId.value,
      ...filterForm
    })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成模拟数据
    const mockData = []
    const startDate = dayjs(filterForm.dateRange[0])
    const endDate = dayjs(filterForm.dateRange[1])
    let currentDate = startDate

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate)) {
      mockData.push({
        date: currentDate.format('YYYY-MM-DD'),
        pv: Math.floor(Math.random() * 2000) + 500,
        uv: Math.floor(Math.random() * 800) + 200,
        ip: Math.floor(Math.random() * 600) + 150,
        bounce_rate: Math.floor(Math.random() * 30) + 30,
        avg_duration: Math.floor(Math.random() * 300) + 60,
        new_visitors: Math.floor(Math.random() * 400) + 100,
        returning_visitors: Math.floor(Math.random() * 400) + 100
      })
      currentDate = currentDate.add(1, 'day')
    }

    trendData.value = mockData
    pagination.total = mockData.length

    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('加载趋势数据失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    isLoading.value = false
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  ElMessage.info('导出功能开发中')
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadTrendData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTrendData()
}

// 监听当前网站变化
watch(currentSiteId, (newSiteId) => {
  if (newSiteId) {
    // 更新路由参数中的siteId
    if (route.params.siteId !== newSiteId.toString()) {
      // 这里可以选择是否更新路由，或者直接加载数据
    }
    loadTrendData()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  // 如果路由中有siteId，设置为当前网站
  if (siteId && !isNaN(Number(siteId))) {
    siteStore.setCurrentSite(Number(siteId))
  }

  if (currentSiteId.value) {
    loadTrendData()
  }
})
</script>

<style scoped>
.trend-analysis {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.data-table-section {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trend-analysis {
    padding: 16px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}
</style>
