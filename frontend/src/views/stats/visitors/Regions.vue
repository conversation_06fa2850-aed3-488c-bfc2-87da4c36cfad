<template>
  <div class="visitors-regions">
    <div class="page-header">
      <h2>地区分布</h2>
      <p>中国地图热力图显示</p>
    </div>
    
    <div class="content-placeholder">
      <el-icon><Location /></el-icon>
      <h3>地区分布页面</h3>
      <p>功能开发中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Location } from '@element-plus/icons-vue'
</script>

<style scoped>
.visitors-regions {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0 0 40px 0;
  color: #909399;
  font-size: 14px;
}

.content-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
}

.content-placeholder .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.content-placeholder h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
}

.content-placeholder p {
  margin: 0;
  font-size: 14px;
}
</style>
