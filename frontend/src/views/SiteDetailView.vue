<template>
  <div class="site-detail-container">
    <div v-if="sitesStore.loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="site" class="site-detail">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
          <div class="site-info">
            <h1>{{ site.site_name }}</h1>
            <p>{{ site.domain }}</p>
          </div>
        </div>
        <div class="header-right">
          <el-button :icon="View" @click="viewTrackingCode">跟踪代码</el-button>
          <el-button type="primary" :icon="Edit" @click="editSite">编辑网站</el-button>
        </div>
      </div>
      
      <!-- 统计概览 -->
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card">
              <div class="stat-icon pv">
                <el-icon><View /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats?.pv || 0 }}</div>
                <div class="stat-label">页面浏览量</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card">
              <div class="stat-icon uv">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats?.uv || 0 }}</div>
                <div class="stat-label">独立访客</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card">
              <div class="stat-icon ip">
                <el-icon><Location /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats?.ip_count || 0 }}</div>
                <div class="stat-label">独立IP</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card">
              <div class="stat-icon bounce">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatPercent(stats?.bounce_rate) }}</div>
                <div class="stat-label">跳出率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 暂时显示基础信息 -->
      <div class="site-info-card">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>网站信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="网站名称">{{ site.site_name }}</el-descriptions-item>
            <el-descriptions-item label="域名">{{ site.domain }}</el-descriptions-item>
            <el-descriptions-item label="网站URL">
              <el-link :href="site.site_url" target="_blank" type="primary">
                {{ site.site_url }}
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item label="网站类型">{{ site.site_type }}</el-descriptions-item>
            <el-descriptions-item label="地区">{{ site.region || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="site.status === 1 ? 'success' : 'danger'">
                {{ site.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="公开统计">
              <el-tag :type="site.is_public ? 'warning' : 'info'">
                {{ site.is_public ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(site.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
          <div v-if="site.description" class="site-description">
            <h4>描述</h4>
            <p>{{ site.description }}</p>
          </div>
        </el-card>
      </div>
    </div>
    
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="网站不存在"
        sub-title="请检查网站ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </template>
      </el-result>
    </div>

    <!-- 编辑网站对话框 -->
    <SiteFormDialog
      v-model="showEditDialog"
      :site="site"
      @success="handleEditSuccess"
    />

    <!-- 跟踪代码对话框 -->
    <TrackingCodeDialog
      v-model="showTrackingDialog"
      :site="site"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  View, 
  Edit, 
  User, 
  Location, 
  TrendCharts 
} from '@element-plus/icons-vue'
import { useSitesStore } from '@/stores/sites'
import type { Site, SiteStats } from '@/services/api'
import SiteFormDialog from '@/components/SiteFormDialog.vue'
import TrackingCodeDialog from '@/components/TrackingCodeDialog.vue'

const route = useRoute()
const router = useRouter()
const sitesStore = useSitesStore()

// 响应式数据
const showEditDialog = ref(false)
const showTrackingDialog = ref(false)
const stats = ref<SiteStats | null>(null)

// 计算属性
const siteId = computed(() => parseInt(route.params.id as string))
const site = computed(() => sitesStore.currentSite)

// 获取网站详情
const fetchSiteDetail = async () => {
  try {
    await sitesStore.fetchSite(siteId.value)
    // 同时获取统计数据
    await fetchStats()
  } catch (error) {
    console.error('Failed to fetch site detail:', error)
    ElMessage.error('获取网站详情失败')
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await sitesStore.getSiteStats(siteId.value)
    stats.value = response.stats
  } catch (error) {
    console.error('Failed to fetch stats:', error)
    // 不显示错误消息，因为可能是没有数据
  }
}

// 返回列表
const goBack = () => {
  router.push('/')
}

// 编辑网站
const editSite = () => {
  showEditDialog.value = true
}

// 查看跟踪代码
const viewTrackingCode = () => {
  showTrackingDialog.value = true
}

// 编辑成功处理
const handleEditSuccess = () => {
  showEditDialog.value = false
  fetchSiteDetail()
}

// 格式化百分比
const formatPercent = (value?: number) => {
  if (value === undefined || value === null) return '0%'
  return `${(value * 100).toFixed(1)}%`
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  fetchSiteDetail()
})
</script>

<style scoped>
.site-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.site-info h1 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.pv {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.uv {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.ip {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.bounce {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.site-info-card {
  background: white;
  border-radius: 8px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.site-description {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.site-description h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.site-description p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.error-container {
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-detail-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 12px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}
</style>
