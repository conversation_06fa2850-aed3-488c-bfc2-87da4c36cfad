<template>
  <div class="overview-container" v-loading="loading">
    <!-- 网站信息 -->
    <div v-if="currentSite" class="site-info">
      <h2>{{ currentSite.site_name }}</h2>
      <p>{{ currentSite.domain }} | {{ currentSite.site_url }}</p>
    </div>

    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon pv">
                <el-icon><View /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ formatNumber(overviewData.pv) }}</div>
                <div class="stats-label">今日PV</div>
                <div class="stats-change" :class="{ positive: overviewData.pvChange > 0, negative: overviewData.pvChange < 0 }">
                  {{ overviewData.pvChange > 0 ? '+' : '' }}{{ overviewData.pvChange }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon uv">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ formatNumber(overviewData.uv) }}</div>
                <div class="stats-label">今日UV</div>
                <div class="stats-change" :class="{ positive: overviewData.uvChange > 0, negative: overviewData.uvChange < 0 }">
                  {{ overviewData.uvChange > 0 ? '+' : '' }}{{ overviewData.uvChange }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon ip">
                <el-icon><Location /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ formatNumber(overviewData.ip) }}</div>
                <div class="stats-label">今日IP</div>
                <div class="stats-change" :class="{ positive: overviewData.ipChange > 0, negative: overviewData.ipChange < 0 }">
                  {{ overviewData.ipChange > 0 ? '+' : '' }}{{ overviewData.ipChange }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon online">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ overviewData.online }}</div>
                <div class="stats-label">当前在线</div>
                <div class="stats-change">
                  实时数据
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图表 -->
    <div class="chart-section">
      <el-card>
        <template #header>
          <div class="chart-header">
            <span>访问趋势</span>
            <el-radio-group v-model="chartPeriod" @change="loadChartData">
              <el-radio-button label="today">今天</el-radio-button>
              <el-radio-button label="yesterday">昨天</el-radio-button>
              <el-radio-button label="7days">最近7天</el-radio-button>
              <el-radio-button label="30days">最近30天</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div class="chart-container" ref="chartContainer">
          <!-- 这里将放置图表组件 -->
          <div class="chart-placeholder">
            <el-icon><TrendCharts /></el-icon>
            <p>图表组件开发中...</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>Top 10 搜索词</span>
            </template>
            <div class="top-list">
              <div v-for="(item, index) in topKeywords" :key="index" class="top-item">
                <span class="rank">{{ index + 1 }}</span>
                <span class="keyword">{{ item.keyword }}</span>
                <span class="count">{{ item.count }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>Top 10 来源网站</span>
            </template>
            <div class="top-list">
              <div v-for="(item, index) in topSources" :key="index" class="top-item">
                <span class="rank">{{ index + 1 }}</span>
                <span class="source">{{ item.source }}</span>
                <span class="count">{{ item.count }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 地域分布和新老访客 -->
    <div class="additional-stats">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>地域分布</span>
            </template>
            <div class="region-stats">
              <div v-for="(item, index) in topRegions" :key="index" class="region-item">
                <span class="region-name">{{ item.region }}</span>
                <div class="region-bar">
                  <div class="region-progress" :style="{ width: item.percentage + '%' }"></div>
                </div>
                <span class="region-count">{{ item.count }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>新老访客分析</span>
            </template>
            <div class="visitor-analysis">
              <div class="visitor-chart">
                <!-- 这里将放置饼图组件 -->
                <div class="chart-placeholder">
                  <el-icon><PieChart /></el-icon>
                  <p>饼图组件开发中...</p>
                </div>
              </div>
              <div class="visitor-legend">
                <div class="legend-item">
                  <span class="legend-color new"></span>
                  <span>新访客: {{ visitorData.newVisitors }}</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color old"></span>
                  <span>老访客: {{ visitorData.oldVisitors }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { getOverviewData, getSourceData, getRegionData, getVisitorTypeData } from '@/api/stats'
import { View, User, Location, Connection, TrendCharts, PieChart } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const route = useRoute()
const siteStore = useSiteStore()

// 响应式数据
const chartPeriod = ref('today')
const chartContainer = ref()
const loading = ref(false)

const overviewData = reactive({
  pv: 12580,
  pvChange: 15.2,
  uv: 3420,
  uvChange: 8.7,
  ip: 2890,
  ipChange: -2.1,
  online: 45
})

const topKeywords = ref([
  { keyword: 'Vue3教程', count: 1250 },
  { keyword: 'JavaScript', count: 980 },
  { keyword: 'TypeScript', count: 756 },
  { keyword: 'Element Plus', count: 642 },
  { keyword: 'Vite', count: 523 }
])

const topSources = ref([
  { source: 'baidu.com', count: 2340 },
  { source: 'google.com', count: 1890 },
  { source: 'github.com', count: 1456 },
  { source: 'juejin.cn', count: 1234 },
  { source: 'csdn.net', count: 987 }
])

const topRegions = ref([
  { region: '北京', count: 2340, percentage: 100 },
  { region: '上海', count: 1890, percentage: 80.7 },
  { region: '广东', count: 1456, percentage: 62.2 },
  { region: '浙江', count: 1234, percentage: 52.7 },
  { region: '江苏', count: 987, percentage: 42.2 }
])

const visitorData = reactive({
  newVisitors: 2340,
  oldVisitors: 1080
})

// 计算属性
const currentSite = computed(() => siteStore.currentSite)
const currentSiteId = computed(() => siteStore.currentSiteId)

// 方法
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const loadOverviewData = async () => {
  if (!currentSiteId.value) {
    ElMessage.warning('请先选择一个网站')
    return
  }

  loading.value = true
  try {
    console.log('加载网站概况数据:', currentSiteId.value)

    // 并行加载多个数据
    const [overviewRes, sourceRes, regionRes, visitorRes] = await Promise.allSettled([
      getOverviewData(currentSiteId.value),
      getSourceData(currentSiteId.value, { limit: 5 }),
      getRegionData(currentSiteId.value, { limit: 5 }),
      getVisitorTypeData(currentSiteId.value)
    ])

    // 处理概况数据
    if (overviewRes.status === 'fulfilled') {
      const data = overviewRes.value.data
      console.log('🎯 Overview API 返回的数据:', data)
      Object.assign(overviewData, {
        pv: data.pv,
        pvChange: data.pv_change,
        uv: data.uv,
        uvChange: data.uv_change,
        ip: data.ip,
        ipChange: data.ip_change,
        online: data.online
      })
      console.log('🎯 处理后的概况数据:', overviewData)
    } else {
      console.warn('概况数据加载失败，使用模拟数据:', overviewRes.reason)
      // 使用模拟数据作为降级
      Object.assign(overviewData, {
        pv: Math.floor(Math.random() * 50000) + 10000,
        pvChange: (Math.random() - 0.5) * 40,
        uv: Math.floor(Math.random() * 20000) + 5000,
        uvChange: (Math.random() - 0.5) * 30,
        ip: Math.floor(Math.random() * 15000) + 3000,
        ipChange: (Math.random() - 0.5) * 25,
        online: Math.floor(Math.random() * 100) + 20
      })
    }

    // 处理来源数据
    if (sourceRes.status === 'fulfilled') {
      topSources.value = sourceRes.value.data.data.map(item => ({
        source: item.source,
        count: item.pv
      }))
    } else {
      console.warn('来源数据加载失败，使用模拟数据:', sourceRes.reason)
      topSources.value = [
        { source: '百度', count: Math.floor(Math.random() * 5000) + 1000 },
        { source: '谷歌', count: Math.floor(Math.random() * 3000) + 800 },
        { source: '直接访问', count: Math.floor(Math.random() * 2000) + 500 },
        { source: '微信', count: Math.floor(Math.random() * 1500) + 300 },
        { source: '其他', count: Math.floor(Math.random() * 1000) + 200 }
      ]
    }

    // 处理地区数据
    if (regionRes.status === 'fulfilled') {
      topRegions.value = regionRes.value.data.data.map(item => ({
        region: item.region,
        count: item.pv,
        percentage: item.percentage
      }))
    } else {
      console.warn('地区数据加载失败，使用模拟数据:', regionRes.reason)
      const regions = ['北京', '上海', '广东', '浙江', '江苏']
      topRegions.value = regions.map(region => {
        const count = Math.floor(Math.random() * 3000) + 500
        return {
          region,
          count,
          percentage: Math.floor(Math.random() * 80) + 20
        }
      })
    }

    // 处理访客类型数据
    if (visitorRes.status === 'fulfilled') {
      const data = visitorRes.value.data
      Object.assign(visitorData, {
        newVisitors: data.new_visitors.count,
        oldVisitors: data.returning_visitors.count
      })
    } else {
      console.warn('访客类型数据加载失败，使用模拟数据:', visitorRes.reason)
      const newVisitors = Math.floor(Math.random() * 8000) + 2000
      const oldVisitors = Math.floor(Math.random() * 5000) + 1000
      Object.assign(visitorData, {
        newVisitors,
        oldVisitors
      })
    }

  } catch (error) {
    console.error('加载概况数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadChartData = () => {
  // TODO: 根据选择的时间段加载图表数据
  console.log('加载图表数据:', chartPeriod.value)
}

// 监听当前网站变化
watch(currentSiteId, (newSiteId) => {
  if (newSiteId) {
    loadOverviewData()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (currentSiteId.value) {
    loadOverviewData()
  }
  loadChartData()
})
</script>

<style scoped>
.overview-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.site-info {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.site-info h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.stats-icon.pv {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.uv {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.ip {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin: 4px 0;
}

.stats-change {
  font-size: 12px;
  color: #909399;
}

.stats-change.positive {
  color: #67c23a;
}

.stats-change.negative {
  color: #f56c6c;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.data-overview {
  margin-bottom: 20px;
}

.top-list {
  max-height: 300px;
  overflow-y: auto;
}

.top-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.top-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  background: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.keyword, .source {
  flex: 1;
  color: #303133;
}

.count {
  color: #909399;
  font-size: 14px;
}

.region-stats {
  padding: 16px 0;
}

.region-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.region-name {
  width: 60px;
  font-size: 14px;
  color: #303133;
}

.region-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin: 0 12px;
  overflow: hidden;
}

.region-progress {
  height: 100%;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 4px;
  transition: width 0.3s;
}

.region-count {
  width: 60px;
  text-align: right;
  font-size: 14px;
  color: #909399;
}

.visitor-analysis {
  display: flex;
  align-items: center;
  padding: 16px 0;
}

.visitor-chart {
  flex: 1;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visitor-legend {
  width: 120px;
  padding-left: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #303133;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-color.new {
  background: #409eff;
}

.legend-color.old {
  background: #67c23a;
}
</style>
