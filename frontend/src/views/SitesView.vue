<template>
  <div class="sites-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>我的网站</h1>
        <p>管理您的网站统计</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          添加网站
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索网站名称或域名"
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
        style="max-width: 400px"
      />
    </div>

    <!-- 网站列表 -->
    <div class="sites-grid" v-loading="sitesStore.loading">
      <div
        v-for="site in sitesStore.sites"
        :key="site.id"
        class="site-card"
        @click="goToSiteDetail(site.id)"
      >
        <div class="site-header">
          <div class="site-info">
            <h3>{{ site.site_name }}</h3>
            <p>{{ site.domain }}</p>
          </div>
          <div class="site-actions" @click.stop>
            <el-dropdown trigger="click">
              <el-button :icon="MoreFilled" circle size="small" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editSite(site)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item @click="viewTrackingCode(site)">
                    <el-icon><View /></el-icon>
                    跟踪代码
                  </el-dropdown-item>
                  <el-dropdown-item @click="deleteSite(site)" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div class="site-meta">
          <el-tag :type="site.status === 1 ? 'success' : 'danger'" size="small">
            {{ site.status === 1 ? '正常' : '停用' }}
          </el-tag>
          <el-tag type="info" size="small">{{ site.site_type }}</el-tag>
          <el-tag v-if="site.is_public" type="warning" size="small">公开</el-tag>
        </div>
        
        <div class="site-description">
          <p>{{ site.description || '暂无描述' }}</p>
        </div>
        
        <div class="site-footer">
          <span class="site-url">{{ site.site_url }}</span>
          <span class="site-date">{{ formatDate(site.created_at) }}</span>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!sitesStore.loading && sitesStore.sites.length === 0" class="empty-state">
        <el-empty description="暂无网站数据">
          <el-button type="primary" @click="showCreateDialog = true">添加第一个网站</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="sitesStore.sites.length > 0" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="sitesStore.pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑网站对话框 -->
    <SiteFormDialog
      v-model="showCreateDialog"
      :site="editingSite"
      @success="handleFormSuccess"
    />

    <!-- 跟踪代码对话框 -->
    <TrackingCodeDialog
      v-model="showTrackingDialog"
      :site="selectedSite"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  MoreFilled, 
  Edit, 
  View, 
  Delete 
} from '@element-plus/icons-vue'
import { useSitesStore } from '@/stores/sites'
import type { Site } from '@/services/api'
import SiteFormDialog from '@/components/SiteFormDialog.vue'
import TrackingCodeDialog from '@/components/TrackingCodeDialog.vue'

const router = useRouter()
const sitesStore = useSitesStore()

// 响应式数据
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const showCreateDialog = ref(false)
const showTrackingDialog = ref(false)
const editingSite = ref<Site | null>(null)
const selectedSite = ref<Site | null>(null)

// 搜索防抖
let searchTimeout: NodeJS.Timeout
const handleSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    fetchSites()
  }, 500)
}

// 获取网站列表
const fetchSites = async () => {
  try {
    await sitesStore.fetchSites({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value || undefined
    })
  } catch (error) {
    ElMessage.error('获取网站列表失败')
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchSites()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchSites()
}

// 跳转到网站详情
const goToSiteDetail = (siteId: number) => {
  router.push(`/sites/${siteId}`)
}

// 编辑网站
const editSite = (site: Site) => {
  editingSite.value = site
  showCreateDialog.value = true
}

// 查看跟踪代码
const viewTrackingCode = (site: Site) => {
  selectedSite.value = site
  showTrackingDialog.value = true
}

// 删除网站
const deleteSite = async (site: Site) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除网站 "${site.site_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await sitesStore.deleteSite(site.id)
    ElMessage.success('删除成功')
    
    // 如果当前页没有数据了，回到上一页
    if (sitesStore.sites.length === 0 && currentPage.value > 1) {
      currentPage.value--
    }
    fetchSites()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 表单成功处理
const handleFormSuccess = () => {
  showCreateDialog.value = false
  editingSite.value = null
  fetchSites()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  fetchSites()
})
</script>

<style scoped>
.sites-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-bar {
  margin-bottom: 24px;
}

.sites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.site-card {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.site-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.site-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.site-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.site-meta {
  margin-bottom: 12px;
}

.site-meta .el-tag {
  margin-right: 8px;
}

.site-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.site-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f5f7fa;
}

.site-url {
  color: #409eff;
  font-size: 12px;
  text-decoration: none;
}

.site-date {
  color: #c0c4cc;
  font-size: 12px;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .sites-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .sites-grid {
    grid-template-columns: 1fr;
  }
}
</style>
