<template>
  <div class="login-test">
    <div class="login-box">
      <h2>测试登录</h2>
      
      <div class="form-group">
        <input 
          v-model="username" 
          type="text" 
          placeholder="用户名"
          @keyup.enter="testLogin"
        />
      </div>
      
      <div class="form-group">
        <input 
          v-model="password" 
          type="password" 
          placeholder="密码"
          @keyup.enter="testLogin"
        />
      </div>
      
      <button @click="testLogin" :disabled="loading">
        {{ loading ? '登录中...' : '登录' }}
      </button>
      
      <div class="debug-info">
        <h3>调试信息:</h3>
        <p>用户名: {{ username }}</p>
        <p>密码: {{ password }}</p>
        <p>加载状态: {{ loading }}</p>
        <p>登录状态: {{ isLoggedIn }}</p>
        <p>Token: {{ token ? token.substring(0, 20) + '...' : '无' }}</p>
        <p>用户: {{ user ? user.username : '无' }}</p>
      </div>
      
      <div class="logs">
        <h3>操作日志:</h3>
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const username = ref('testuser')
const password = ref('123456')
const loading = ref(false)
const logs = ref<string[]>([])

const isLoggedIn = computed(() => userStore.isLoggedIn)
const token = computed(() => userStore.token)
const user = computed(() => userStore.user)

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  console.log(`[${timestamp}] ${message}`)
}

const testLogin = async () => {
  addLog('开始登录测试')
  
  if (!username.value || !password.value) {
    addLog('用户名或密码为空')
    return
  }
  
  loading.value = true
  addLog(`尝试登录: ${username.value}`)
  
  try {
    const loginData = {
      username: username.value,
      password: password.value,
      remember: false
    }
    
    addLog('调用userStore.login')
    const success = await userStore.login(loginData)
    addLog(`登录结果: ${success}`)
    
    if (success) {
      addLog('登录成功，准备跳转')
      addLog(`当前状态 - isLoggedIn: ${userStore.isLoggedIn}, token: ${!!userStore.token}, user: ${!!userStore.user}`)
      
      // 延迟跳转，确保状态更新
      setTimeout(() => {
        addLog('执行跳转到仪表板')
        window.location.href = '/dashboard'
      }, 1000)
    } else {
      addLog('登录失败')
    }
  } catch (error) {
    addLog(`登录异常: ${error}`)
    console.error('登录异常:', error)
  } finally {
    loading.value = false
    addLog('登录流程结束')
  }
}

// 页面加载时的初始化
addLog('页面加载完成')
addLog(`初始状态 - isLoggedIn: ${userStore.isLoggedIn}, token: ${!!userStore.token}, user: ${!!userStore.user}`)
</script>

<style scoped>
.login-test {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  padding: 20px;
}

.login-box {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
}

h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  box-sizing: border-box;
}

button {
  width: 100%;
  padding: 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 20px;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.debug-info, .logs {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.debug-info h3, .logs h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
  font-size: 12px;
  color: #333;
}

.log-item {
  font-size: 12px;
  color: #666;
  margin: 2px 0;
  font-family: monospace;
}

.logs {
  max-height: 200px;
  overflow-y: auto;
}
</style>
