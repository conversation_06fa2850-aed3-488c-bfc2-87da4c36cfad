<template>
  <div class="site-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" text>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1 class="page-title">编辑网站</h1>
        <p class="page-subtitle">修改网站配置信息</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 表单内容 -->
    <div v-else-if="siteData" class="form-container">
      <el-form
        ref="siteFormRef"
        :model="siteForm"
        :rules="siteRules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-form-item label="网站名称" prop="site_name">
            <el-input
              v-model="siteForm.site_name"
              placeholder="请输入网站名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="网站域名" prop="domain">
            <el-input
              v-model="siteForm.domain"
              placeholder="example.com"
              @blur="validateDomain"
            >
              <template #prepend>https://</template>
            </el-input>
            <div class="form-tip">
              请输入不带协议的域名，如：example.com
            </div>
          </el-form-item>
          
          <el-form-item label="网站地址" prop="site_url">
            <el-input
              v-model="siteForm.site_url"
              placeholder="https://example.com"
            />
            <div class="form-tip">
              完整的网站访问地址
            </div>
          </el-form-item>
          
          <el-form-item label="网站类型" prop="site_type">
            <el-select v-model="siteForm.site_type" placeholder="请选择网站类型">
              <el-option label="企业官网" value="corporate" />
              <el-option label="电商网站" value="ecommerce" />
              <el-option label="博客网站" value="blog" />
              <el-option label="新闻网站" value="news" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="地区" prop="region">
            <el-select v-model="siteForm.region" placeholder="请选择地区" clearable>
              <el-option label="中国大陆" value="cn" />
              <el-option label="香港" value="hk" />
              <el-option label="台湾" value="tw" />
              <el-option label="美国" value="us" />
              <el-option label="欧洲" value="eu" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="网站描述" prop="description">
            <el-input
              v-model="siteForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入网站描述（可选）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>访问设置</span>
            </div>
          </template>
          
          <el-form-item label="公开访问">
            <el-switch
              v-model="siteForm.is_public"
              :active-value="1"
              :inactive-value="0"
              active-text="允许"
              inactive-text="禁止"
            />
            <div class="form-tip">
              开启后，其他人可以通过链接查看统计数据
            </div>
          </el-form-item>
          
          <el-form-item label="访问密码" prop="view_password" v-if="siteForm.is_public === 1">
            <el-input
              v-model="siteForm.view_password"
              type="password"
              placeholder="设置访问密码（可选）"
              show-password
              clearable
            />
            <div class="form-tip">
              设置密码后，访客需要输入密码才能查看统计数据
            </div>
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>网站信息</span>
            </div>
          </template>
          
          <el-form-item label="跟踪代码">
            <el-input
              :value="siteData.tracking_code"
              readonly
              class="tracking-code-input"
            >
              <template #append>
                <el-button @click="copyTrackingCode">复制</el-button>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="创建时间">
            <span class="info-text">{{ formatDate(siteData.created_at) }}</span>
          </el-form-item>
          
          <el-form-item label="更新时间">
            <span class="info-text">{{ formatDate(siteData.updated_at) }}</span>
          </el-form-item>
          
          <el-form-item label="统计数据">
            <div class="stats-info">
              <div class="stat-item">
                <span class="stat-label">总访问量:</span>
                <span class="stat-value">{{ formatNumber(siteData.total_visits || 0) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">总页面浏览:</span>
                <span class="stat-value">{{ formatNumber(siteData.total_page_views || 0) }}</span>
              </div>
            </div>
          </el-form-item>
        </el-card>

        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button @click="showTrackingCode">查看代码</el-button>
          <el-button type="primary" :loading="siteStore.isLoading" @click="handleSubmit">
            {{ siteStore.isLoading ? '保存中...' : '保存修改' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="网站不存在或已被删除">
        <el-button @click="$router.push('/sites')">返回网站列表</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'
import type { Site, SiteForm } from '@/types'

const route = useRoute()
const router = useRouter()
const siteStore = useSiteStore()

// 响应式数据
const loading = ref(true)
const siteData = ref<Site | null>(null)
const siteFormRef = ref<FormInstance>()

// 表单数据
const siteForm = reactive<Partial<SiteForm>>({
  site_name: '',
  domain: '',
  site_url: '',
  site_type: '',
  region: '',
  description: '',
  view_password: '',
  is_public: 0
})

// 域名验证器
const validateDomainUnique = async (rule: any, value: string, callback: any) => {
  if (!value || !siteData.value) {
    callback()
    return
  }
  
  // 如果域名没有变化，不需要验证
  if (value === siteData.value.domain) {
    callback()
    return
  }
  
  try {
    const isAvailable = await siteStore.validateDomain(value, siteData.value.id)
    if (!isAvailable) {
      callback(new Error('该域名已被使用'))
    } else {
      callback()
    }
  } catch (error) {
    callback(new Error('域名验证失败'))
  }
}

// 表单验证规则
const siteRules: FormRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 100, message: '网站名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请输入网站域名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/, 
      message: '请输入有效的域名格式', 
      trigger: 'blur' 
    },
    { validator: validateDomainUnique, trigger: 'blur' }
  ],
  site_url: [
    { required: true, message: '请输入网站地址', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的网站地址（包含http://或https://）', 
      trigger: 'blur' 
    }
  ],
  site_type: [
    { required: true, message: '请选择网站类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  view_password: [
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const formatDate = (date: string): string => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const validateDomain = async () => {
  if (siteFormRef.value) {
    await siteFormRef.value.validateField('domain')
  }
}

const copyTrackingCode = async () => {
  if (!siteData.value) return
  
  try {
    await navigator.clipboard.writeText(siteData.value.tracking_code)
    ElMessage.success('跟踪代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const showTrackingCode = () => {
  if (siteData.value) {
    router.push(`/sites/${siteData.value.id}/code`)
  }
}

const handleSubmit = async () => {
  if (!siteFormRef.value || !siteData.value) return
  
  try {
    const valid = await siteFormRef.value.validate()
    if (!valid) return
    
    const success = await siteStore.updateSite(siteData.value.id, siteForm)
    
    if (success) {
      ElMessage.success('网站信息更新成功')
      // 重新加载数据
      await loadSiteData()
    }
  } catch (error) {
    console.error('更新网站失败:', error)
  }
}

const loadSiteData = async () => {
  const siteId = parseInt(route.params.id as string)
  if (!siteId) {
    router.push('/sites')
    return
  }
  
  try {
    loading.value = true
    const site = await siteStore.fetchSiteDetail(siteId)
    
    if (site) {
      siteData.value = site
      
      // 填充表单数据
      Object.assign(siteForm, {
        site_name: site.site_name,
        domain: site.domain,
        site_url: site.site_url,
        site_type: site.site_type,
        region: site.region || '',
        description: site.description || '',
        is_public: site.is_public
      })
    } else {
      ElMessage.error('网站不存在')
      router.push('/sites')
    }
  } catch (error) {
    console.error('加载网站数据失败:', error)
    ElMessage.error('加载网站数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSiteData()
})
</script>

<style scoped>
.site-edit {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 60px;
  color: #909399;
}

.error-container {
  padding: 60px;
}

.form-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.form-card {
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-card:last-of-type {
  margin-bottom: 0;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.tracking-code-input :deep(.el-input__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.info-text {
  color: #606266;
  font-size: 14px;
}

.stats-info {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-group__prepend) {
  background-color: #f5f7fa;
  color: #909399;
  border-color: #dcdfe6;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-switch__text) {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-edit {
    padding: 16px;
  }
  
  .form-container {
    margin: 0 -16px;
    border-radius: 0;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .stats-info {
    flex-direction: column;
    gap: 8px;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}
</style>
