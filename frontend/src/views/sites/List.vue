<template>
  <div class="sites-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">网站管理</h1>
        <p class="page-subtitle">管理您的所有网站，查看统计数据和配置信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/sites/create')" v-if="userStore.hasPermission('site.create')">
          <el-icon><Plus /></el-icon>
          添加网站
        </el-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索网站名称或域名"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="运行中" :value="1" />
            <el-option label="已停用" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.site_type" placeholder="网站类型" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="企业官网" value="corporate" />
            <el-option label="电商网站" value="ecommerce" />
            <el-option label="博客网站" value="blog" />
            <el-option label="新闻网站" value="news" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <div class="filter-actions">
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="danger" :disabled="!selectedSites.length" @click="handleBatchDelete">
              批量删除
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 网站列表 -->
    <div class="sites-table">
      <el-table
        v-loading="siteStore.isLoading"
        :data="siteStore.sites"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="site_name" label="网站名称" min-width="150">
          <template #default="{ row }">
            <div class="site-info">
              <div class="site-name">{{ row.site_name }}</div>
              <div class="site-url">{{ row.site_url }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="domain" label="域名" min-width="120" />
        
        <el-table-column prop="site_type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getSiteTypeTagType(row.site_type)" size="small">
              {{ getSiteTypeLabel(row.site_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="统计数据" min-width="180">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <span class="stat-label">访问量:</span>
                <span class="stat-value">{{ formatNumber(row.total_visits || 0) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">页面浏览:</span>
                <span class="stat-value">{{ formatNumber(row.total_page_views || 0) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              :disabled="!userStore.hasPermission('site.update')"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="goToStats(row.id)"
                v-if="userStore.hasPermission('stats.view')"
              >
                统计
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="showTrackingCode(row.id)"
                v-if="userStore.hasPermission('site.view')"
              >
                代码
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit" v-if="userStore.hasPermission('site.update')">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="copy">
                      <el-icon><CopyDocument /></el-icon>复制
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided v-if="userStore.hasPermission('site.delete')">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.page_size"
        :page-sizes="[10, 20, 50, 100]"
        :total="siteStore.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 跟踪代码对话框 -->
    <el-dialog
      v-model="trackingCodeDialog.visible"
      title="跟踪代码"
      width="600px"
      :before-close="closeTrackingCodeDialog"
    >
      <div v-if="trackingCodeDialog.loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-else-if="trackingCodeDialog.data" class="tracking-code-content">
        <el-alert
          title="请将以下代码添加到您网站的 </head> 标签之前"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        />
        <el-input
          v-model="trackingCodeDialog.data.js_code"
          type="textarea"
          :rows="8"
          readonly
          class="code-textarea"
        />
        <div class="code-actions">
          <el-button @click="copyTrackingCode">
            <el-icon><CopyDocument /></el-icon>
            复制代码
          </el-button>
          <el-button @click="regenerateCode" type="warning">
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSiteStore } from '@/stores/site'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import type { Site, TrackingCodeInfo } from '@/types'

const router = useRouter()
const userStore = useUserStore()
const siteStore = useSiteStore()

// 响应式数据
const selectedSites = ref<Site[]>([])
const searchForm = reactive({
  search: '',
  status: '',
  site_type: ''
})

const pagination = reactive({
  page: 1,
  page_size: 20
})

const trackingCodeDialog = reactive({
  visible: false,
  loading: false,
  data: null as TrackingCodeInfo | null,
  siteId: 0
})

// 计算属性
const hasSelected = computed(() => selectedSites.value.length > 0)

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (date: string): string => {
  return dayjs(date).format('MM-DD')
}

const getSiteTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    corporate: '企业官网',
    ecommerce: '电商网站',
    blog: '博客网站',
    news: '新闻网站',
    other: '其他'
  }
  return typeMap[type] || type
}

const getSiteTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    corporate: 'primary',
    ecommerce: 'success',
    blog: 'info',
    news: 'warning',
    other: 'default'
  }
  return typeMap[type] || 'default'
}

const handleSearch = async () => {
  pagination.page = 1
  await loadSites()
}

const resetSearch = async () => {
  searchForm.search = ''
  searchForm.status = ''
  searchForm.site_type = ''
  pagination.page = 1
  await loadSites()
}

const handleSelectionChange = (selection: Site[]) => {
  selectedSites.value = selection
}

const handleStatusChange = async (site: Site) => {
  try {
    const success = await siteStore.toggleSiteStatus(site.id)
    if (!success) {
      // 如果失败，恢复原状态
      site.status = site.status === 1 ? 0 : 1
    }
  } catch (error) {
    // 恢复原状态
    site.status = site.status === 1 ? 0 : 1
    console.error('切换状态失败:', error)
  }
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadSites()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadSites()
}

const goToStats = (siteId: number) => {
  router.push(`/stats/${siteId}/overview`)
}

const showTrackingCode = async (siteId: number) => {
  trackingCodeDialog.visible = true
  trackingCodeDialog.loading = true
  trackingCodeDialog.siteId = siteId
  
  try {
    const data = await siteStore.getTrackingCode(siteId)
    trackingCodeDialog.data = data
  } catch (error) {
    ElMessage.error('获取跟踪代码失败')
  } finally {
    trackingCodeDialog.loading = false
  }
}

const closeTrackingCodeDialog = () => {
  trackingCodeDialog.visible = false
  trackingCodeDialog.data = null
  trackingCodeDialog.siteId = 0
}

const copyTrackingCode = async () => {
  if (!trackingCodeDialog.data) return
  
  try {
    await navigator.clipboard.writeText(trackingCodeDialog.data.js_code)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const regenerateCode = async () => {
  try {
    await ElMessageBox.confirm('重新生成代码后，旧代码将失效，确定继续吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const success = await siteStore.regenerateTrackingCode(trackingCodeDialog.siteId)
    if (success) {
      // 重新获取代码
      await showTrackingCode(trackingCodeDialog.siteId)
    }
  } catch {
    // 用户取消
  }
}

const handleAction = async (command: string, site: Site) => {
  switch (command) {
    case 'edit':
      router.push(`/sites/${site.id}/edit`)
      break
    case 'copy':
      // TODO: 实现复制网站功能
      ElMessage.info('复制功能开发中')
      break
    case 'delete':
      await handleDelete(site)
      break
  }
}

const handleDelete = async (site: Site) => {
  try {
    await ElMessageBox.confirm(`确定要删除网站 "${site.site_name}" 吗？此操作不可恢复。`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const success = await siteStore.deleteSite(site.id)
    if (success) {
      await loadSites()
    }
  } catch {
    // 用户取消
  }
}

const handleBatchDelete = async () => {
  if (!selectedSites.value.length) return
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedSites.value.length} 个网站吗？此操作不可恢复。`, '确认批量删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 实现批量删除API
    ElMessage.info('批量删除功能开发中')
  } catch {
    // 用户取消
  }
}

const loadSites = async () => {
  const params: any = {
    page: pagination.page,
    page_size: pagination.page_size,
    ...searchForm
  }

  // 过滤空值
  Object.keys(params).forEach(key => {
    if (params[key] === '') {
      delete params[key]
    }
  })

  await siteStore.fetchSites(params)
}

// 组件挂载时加载数据
onMounted(() => {
  loadSites()
})
</script>

<style scoped>
.sites-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.sites-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.site-info {
  display: flex;
  flex-direction: column;
}

.site-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.site-url {
  font-size: 12px;
  color: #909399;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #409eff;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.tracking-code-content {
  padding: 16px 0;
}

.code-textarea {
  margin-bottom: 16px;
}

.code-textarea :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.code-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sites-list {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-section {
    padding: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
