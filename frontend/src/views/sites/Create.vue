<template>
  <div class="site-create">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" text>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1 class="page-title">添加网站</h1>
        <p class="page-subtitle">添加新网站开始统计分析</p>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-form
        ref="siteFormRef"
        :model="siteForm"
        :rules="siteRules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-form-item label="网站名称" prop="site_name">
            <el-input
              v-model="siteForm.site_name"
              placeholder="请输入网站名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="网站域名" prop="domain">
            <el-input
              v-model="siteForm.domain"
              placeholder="example.com"
              @blur="validateDomain"
            >
              <template #prepend>https://</template>
            </el-input>
            <div class="form-tip">
              请输入不带协议的域名，如：example.com
            </div>
          </el-form-item>
          
          <el-form-item label="网站地址" prop="site_url">
            <el-input
              v-model="siteForm.site_url"
              placeholder="https://example.com"
            />
            <div class="form-tip">
              完整的网站访问地址
            </div>
          </el-form-item>
          
          <el-form-item label="网站类型" prop="site_type">
            <el-select v-model="siteForm.site_type" placeholder="请选择网站类型">
              <el-option label="企业官网" value="corporate" />
              <el-option label="电商网站" value="ecommerce" />
              <el-option label="博客网站" value="blog" />
              <el-option label="新闻网站" value="news" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="地区" prop="region">
            <el-select v-model="siteForm.region" placeholder="请选择地区" clearable>
              <el-option label="中国大陆" value="cn" />
              <el-option label="香港" value="hk" />
              <el-option label="台湾" value="tw" />
              <el-option label="美国" value="us" />
              <el-option label="欧洲" value="eu" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="网站描述" prop="description">
            <el-input
              v-model="siteForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入网站描述（可选）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>访问设置</span>
            </div>
          </template>
          
          <el-form-item label="公开访问">
            <el-switch
              v-model="siteForm.is_public"
              :active-value="1"
              :inactive-value="0"
              active-text="允许"
              inactive-text="禁止"
            />
            <div class="form-tip">
              开启后，其他人可以通过链接查看统计数据
            </div>
          </el-form-item>
          
          <el-form-item label="访问密码" prop="view_password" v-if="siteForm.is_public === 1">
            <el-input
              v-model="siteForm.view_password"
              type="password"
              placeholder="设置访问密码（可选）"
              show-password
              clearable
            />
            <div class="form-tip">
              设置密码后，访客需要输入密码才能查看统计数据
            </div>
          </el-form-item>
        </el-card>

        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button type="primary" :loading="siteStore.isLoading" @click="handleSubmit">
            {{ siteStore.isLoading ? '创建中...' : '创建网站' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { SiteForm } from '@/types'

const router = useRouter()
const siteStore = useSiteStore()

// 表单引用
const siteFormRef = ref<FormInstance>()

// 表单数据
const siteForm = reactive<SiteForm>({
  site_name: '',
  domain: '',
  site_url: '',
  site_type: '',
  region: '',
  description: '',
  view_password: '',
  is_public: 0
})

// 域名验证器
const validateDomainUnique = async (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }
  
  try {
    const isAvailable = await siteStore.validateDomain(value)
    if (!isAvailable) {
      callback(new Error('该域名已被使用'))
    } else {
      callback()
    }
  } catch (error) {
    callback(new Error('域名验证失败'))
  }
}

// 表单验证规则
const siteRules: FormRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 100, message: '网站名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请输入网站域名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/, 
      message: '请输入有效的域名格式', 
      trigger: 'blur' 
    },
    { validator: validateDomainUnique, trigger: 'blur' }
  ],
  site_url: [
    { required: true, message: '请输入网站地址', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的网站地址（包含http://或https://）', 
      trigger: 'blur' 
    }
  ],
  site_type: [
    { required: true, message: '请选择网站类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  view_password: [
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const validateDomain = async () => {
  if (siteFormRef.value) {
    await siteFormRef.value.validateField('domain')
  }
}

const handleSubmit = async () => {
  if (!siteFormRef.value) return
  
  try {
    const valid = await siteFormRef.value.validate()
    if (!valid) return
    
    const success = await siteStore.createSite(siteForm)
    
    if (success) {
      ElMessage.success('网站创建成功')
      router.push('/sites')
    }
  } catch (error) {
    console.error('创建网站失败:', error)
  }
}

// 监听域名变化，自动填充网站地址
const watchDomain = () => {
  if (siteForm.domain && !siteForm.site_url) {
    siteForm.site_url = `https://${siteForm.domain}`
  }
}

// 监听网站名称变化
const watchSiteName = () => {
  // 可以在这里添加一些自动填充逻辑
}
</script>

<style scoped>
.site-create {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.form-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.form-card {
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-card:last-of-type {
  margin-bottom: 0;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-group__prepend) {
  background-color: #f5f7fa;
  color: #909399;
  border-color: #dcdfe6;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-switch__text) {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-create {
    padding: 16px;
  }
  
  .form-container {
    margin: 0 -16px;
    border-radius: 0;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}
</style>
