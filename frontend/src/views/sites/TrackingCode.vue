<template>
  <div class="tracking-code">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" text>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1 class="page-title">跟踪代码</h1>
        <p class="page-subtitle">将以下代码添加到您的网站中开始统计</p>
      </div>
      <div class="header-right">
        <el-button @click="regenerateCode" type="warning" :loading="regenerating">
          <el-icon><Refresh /></el-icon>
          重新生成
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 跟踪代码内容 -->
    <div v-else-if="trackingData" class="code-container">
      <!-- 网站信息 -->
      <el-card class="site-info-card">
        <template #header>
          <div class="card-header">
            <span>网站信息</span>
          </div>
        </template>
        <div class="site-details">
          <div class="detail-item">
            <span class="label">网站名称:</span>
            <span class="value">{{ siteData?.site_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">网站域名:</span>
            <span class="value">{{ siteData?.domain }}</span>
          </div>
          <div class="detail-item">
            <span class="label">跟踪ID:</span>
            <span class="value tracking-id">{{ trackingData.tracking_code }}</span>
            <el-button size="small" @click="copyTrackingId">复制</el-button>
          </div>
        </div>
      </el-card>

      <!-- 安装说明 -->
      <el-card class="install-guide-card">
        <template #header>
          <div class="card-header">
            <span>安装说明</span>
          </div>
        </template>
        <div class="install-steps">
          <el-steps direction="vertical" :active="4">
            <el-step title="复制跟踪代码" description="复制下方的JavaScript代码" />
            <el-step title="打开网站文件" description="找到您网站的HTML模板文件" />
            <el-step title="粘贴代码" description="将代码粘贴到 </head> 标签之前" />
            <el-step title="保存并发布" description="保存文件并发布到您的网站" />
          </el-steps>
        </div>
      </el-card>

      <!-- JavaScript代码 -->
      <el-card class="code-card">
        <template #header>
          <div class="card-header">
            <span>JavaScript 跟踪代码</span>
            <el-button @click="copyJSCode" size="small">
              <el-icon><CopyDocument /></el-icon>
              复制代码
            </el-button>
          </div>
        </template>
        <div class="code-content">
          <el-alert
            title="请将此代码添加到您网站每个页面的 </head> 标签之前"
            type="warning"
            :closable="false"
            style="margin-bottom: 16px"
          />
          <el-input
            v-model="trackingData.js_code"
            type="textarea"
            :rows="12"
            readonly
            class="code-textarea"
          />
        </div>
      </el-card>

      <!-- 安装示例 -->
      <el-card class="examples-card">
        <template #header>
          <div class="card-header">
            <span>安装示例</span>
          </div>
        </template>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="HTML" name="html">
            <div class="example-content">
              <p class="example-description">
                在您的HTML文件中，将跟踪代码添加到 &lt;/head&gt; 标签之前：
              </p>
              <el-input
                :value="trackingData.install_guide.examples.html"
                type="textarea"
                :rows="8"
                readonly
                class="example-code"
              />
              <el-button @click="copyExample('html')" size="small" style="margin-top: 8px">
                复制示例
              </el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane label="WordPress" name="wordpress">
            <div class="example-content">
              <p class="example-description">
                在WordPress中，您可以通过以下方式添加跟踪代码：
              </p>
              <el-input
                :value="trackingData.install_guide.examples.wordpress"
                type="textarea"
                :rows="8"
                readonly
                class="example-code"
              />
              <el-button @click="copyExample('wordpress')" size="small" style="margin-top: 8px">
                复制示例
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 注意事项 -->
      <el-card class="notes-card">
        <template #header>
          <div class="card-header">
            <span>注意事项</span>
          </div>
        </template>
        <div class="notes-content">
          <ul class="notes-list">
            <li v-for="note in trackingData.install_guide.notes" :key="note">
              {{ note }}
            </li>
          </ul>
        </div>
      </el-card>

      <!-- 验证安装 -->
      <el-card class="verify-card">
        <template #header>
          <div class="card-header">
            <span>验证安装</span>
          </div>
        </template>
        <div class="verify-content">
          <p>安装完成后，您可以通过以下方式验证跟踪代码是否正常工作：</p>
          <ol>
            <li>访问您的网站</li>
            <li>等待几分钟让数据传输</li>
            <li>回到统计后台查看是否有新的访问数据</li>
          </ol>
          <div class="verify-actions">
            <el-button type="primary" @click="goToStats">
              <el-icon><TrendCharts /></el-icon>
              查看统计数据
            </el-button>
            <el-button @click="testInstallation">
              <el-icon><Monitor /></el-icon>
              测试安装
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="无法获取跟踪代码">
        <el-button @click="loadTrackingCode">重新加载</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSiteStore } from '@/stores/site'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Site, TrackingCodeInfo } from '@/types'

const route = useRoute()
const router = useRouter()
const siteStore = useSiteStore()

// 响应式数据
const loading = ref(true)
const regenerating = ref(false)
const siteData = ref<Site | null>(null)
const trackingData = ref<TrackingCodeInfo | null>(null)
const activeTab = ref('html')

// 方法
const copyTrackingId = async () => {
  if (!trackingData.value) return
  
  try {
    await navigator.clipboard.writeText(trackingData.value.tracking_code)
    ElMessage.success('跟踪ID已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const copyJSCode = async () => {
  if (!trackingData.value) return
  
  try {
    await navigator.clipboard.writeText(trackingData.value.js_code)
    ElMessage.success('JavaScript代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const copyExample = async (type: 'html' | 'wordpress') => {
  if (!trackingData.value) return
  
  try {
    const code = trackingData.value.install_guide.examples[type]
    await navigator.clipboard.writeText(code)
    ElMessage.success('示例代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const regenerateCode = async () => {
  if (!siteData.value) return
  
  try {
    await ElMessageBox.confirm(
      '重新生成跟踪代码后，旧代码将失效，您需要更新网站中的代码。确定继续吗？',
      '确认重新生成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    regenerating.value = true
    const success = await siteStore.regenerateTrackingCode(siteData.value.id)
    
    if (success) {
      // 重新加载跟踪代码
      await loadTrackingCode()
      ElMessage.success('跟踪代码已重新生成')
    }
  } catch {
    // 用户取消
  } finally {
    regenerating.value = false
  }
}

const goToStats = () => {
  if (siteData.value) {
    router.push(`/stats/${siteData.value.id}/overview`)
  }
}

const testInstallation = () => {
  if (siteData.value) {
    // 打开网站进行测试
    window.open(siteData.value.site_url, '_blank')
    ElMessage.info('请在新窗口中访问您的网站，然后回来查看统计数据')
  }
}

const loadTrackingCode = async () => {
  const siteId = parseInt(route.params.id as string)
  if (!siteId) {
    router.push('/sites')
    return
  }
  
  try {
    loading.value = true
    
    // 加载网站信息
    const site = await siteStore.fetchSiteDetail(siteId)
    if (!site) {
      ElMessage.error('网站不存在')
      router.push('/sites')
      return
    }
    siteData.value = site
    
    // 加载跟踪代码
    const tracking = await siteStore.getTrackingCode(siteId)
    if (!tracking) {
      ElMessage.error('无法获取跟踪代码')
      return
    }
    trackingData.value = tracking
    
  } catch (error) {
    console.error('加载跟踪代码失败:', error)
    ElMessage.error('加载跟踪代码失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTrackingCode()
})
</script>

<style scoped>
.tracking-code {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 60px;
  color: #909399;
}

.error-container {
  padding: 60px;
}

.code-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.site-info-card,
.install-guide-card,
.code-card,
.examples-card,
.notes-card,
.verify-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.site-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.value {
  color: #303133;
}

.tracking-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.install-steps {
  padding: 16px 0;
}

.code-content {
  padding: 16px 0;
}

.code-textarea :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
}

.example-content {
  padding: 16px 0;
}

.example-description {
  margin-bottom: 12px;
  color: #606266;
  line-height: 1.5;
}

.example-code :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
}

.notes-content {
  padding: 16px 0;
}

.notes-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.notes-list li {
  margin-bottom: 8px;
}

.verify-content {
  padding: 16px 0;
}

.verify-content p {
  margin-bottom: 12px;
  color: #606266;
  line-height: 1.5;
}

.verify-content ol {
  margin: 12px 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.verify-content li {
  margin-bottom: 4px;
}

.verify-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-steps--vertical .el-step__main) {
  padding-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tracking-code {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .verify-actions {
    flex-direction: column;
  }
}
</style>
