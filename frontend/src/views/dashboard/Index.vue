<template>
  <div class="dashboard">
    <!-- 调试信息 -->
    <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
      <h3>调试信息:</h3>
      <p>用户: {{ userStore.user?.username || '未加载' }}</p>
      <p>权限数量: {{ userStore.permissions?.permissions?.length || 0 }}</p>
      <p>网站数量: {{ siteStore.sites.length }}</p>
      <p>加载状态: {{ siteStore.isLoading ? '加载中' : '已完成' }}</p>
      <p>总统计: {{ JSON.stringify(totalStats) }}</p>
    </div>

    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎回来，{{ userStore.user?.nickname || userStore.user?.username }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，让我们来看看您的网站数据表现如何。
        </p>
      </div>
      <div class="welcome-actions">
        <el-button type="primary" @click="$router.push('/sites/create')" v-if="userStore.hasPermission('site.create')">
          <el-icon><Plus /></el-icon>
          添加网站
        </el-button>
      </div>
    </div>

    <!-- 网站概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <div class="overview-card">
            <div class="card-icon sites">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ siteStore.total }}</div>
              <div class="card-label">网站总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="overview-card">
            <div class="card-icon visits">
              <el-icon><View /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(totalStats.total_visits) }}</div>
              <div class="card-label">总访问量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="overview-card">
            <div class="card-icon pageviews">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(totalStats.total_pageviews) }}</div>
              <div class="card-label">总页面浏览量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="overview-card">
            <div class="card-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(totalStats.total_users) }}</div>
              <div class="card-label">总用户数</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 网站列表 -->
    <div class="sites-section">
      <div class="section-header">
        <h2 class="section-title">我的网站</h2>
        <el-button type="text" @click="$router.push('/sites')">
          查看全部
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <div class="sites-grid" v-loading="siteStore.isLoading">
        <div
          v-for="site in recentSites"
          :key="site.id"
          class="site-card"
          @click="goToSiteStats(site.id)"
        >
          <div class="site-header">
            <div class="site-info">
              <h3 class="site-name">{{ site.site_name }}</h3>
              <p class="site-domain">{{ site.domain }}</p>
            </div>
            <div class="site-status">
              <el-tag :type="site.status === 1 ? 'success' : 'danger'" size="small">
                {{ site.status === 1 ? '运行中' : '已停用' }}
              </el-tag>
            </div>
          </div>
          
          <div class="site-stats">
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(site.total_visits || 0) }}</span>
              <span class="stat-label">访问量</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(site.total_page_views || 0) }}</span>
              <span class="stat-label">页面浏览量</span>
            </div>
          </div>
          
          <div class="site-footer">
            <span class="last-visit">
              最后访问：{{ site.last_visit ? formatDate(site.last_visit) : '暂无数据' }}
            </span>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!siteStore.isLoading && recentSites.length === 0" class="empty-sites">
          <el-empty description="还没有网站">
            <el-button type="primary" @click="$router.push('/sites/create')" v-if="userStore.hasPermission('site.create')">
              添加第一个网站
            </el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h2 class="section-title">快速操作</h2>
      </div>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-if="userStore.hasPermission('site.create')">
          <div class="action-card" @click="$router.push('/sites/create')">
            <el-icon class="action-icon"><Plus /></el-icon>
            <h3>添加网站</h3>
            <p>添加新的网站开始统计分析</p>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" v-if="userStore.hasPermission('stats.view')">
          <div class="action-card" @click="$router.push('/stats')">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <h3>查看统计</h3>
            <p>查看详细的网站统计数据</p>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="action-card" @click="$router.push('/profile')">
            <el-icon class="action-icon"><User /></el-icon>
            <h3>个人设置</h3>
            <p>管理您的个人信息和偏好</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSiteStore } from '@/stores/site'
import dayjs from 'dayjs'
import type { Site } from '@/types'

const router = useRouter()
const userStore = useUserStore()
const siteStore = useSiteStore()

// 响应式数据
const totalStats = ref({
  total_visits: 0,
  total_pageviews: 0,
  total_users: 0
})

// 计算属性
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日')
})

const recentSites = computed(() => {
  return siteStore.sites.slice(0, 6) // 显示最近的6个网站
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (date: string): string => {
  return dayjs(date).format('MM-DD HH:mm')
}

const goToSiteStats = (siteId: number) => {
  router.push(`/stats/${siteId}/overview`)
}

const loadDashboardData = async () => {
  try {
    // 加载网站列表
    await siteStore.fetchSites({ page: 1, page_size: 10 })

    // 计算总统计数据
    const sites = siteStore.sites
    totalStats.value = {
      total_visits: sites.reduce((sum, site) => sum + (site.total_visits || 0), 0),
      total_pageviews: sites.reduce((sum, site) => sum + (site.total_page_views || 0), 0),
      total_users: sites.length
    }

    console.log('仪表盘数据加载完成:', {
      sitesCount: sites.length,
      totalStats: totalStats.value,
      userInfo: userStore.user
    })
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 140px);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.sites { background: #409eff; }
.card-icon.visits { background: #67c23a; }
.card-icon.pageviews { background: #e6a23c; }
.card-icon.users { background: #f56c6c; }

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #909399;
}

.sites-section, .quick-actions {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.sites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  min-height: 200px;
}

.site-card {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.site-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.site-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.site-domain {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.site-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.site-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.last-visit {
  font-size: 12px;
  color: #c0c4cc;
}

.empty-sites {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.action-card {
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 12px;
}

.action-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.action-card p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .sites-grid {
    grid-template-columns: 1fr;
  }
  
  .site-stats {
    gap: 16px;
  }
}
</style>
