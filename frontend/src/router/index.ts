import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hidden: true,
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      hidden: true,
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard',
          breadcrumb: true,
          fullWidth: true
        }
      },
      {
        path: 'sites',
        name: 'Sites',
        meta: {
          title: '网站管理',
          icon: 'Monitor',
          permission: 'site.view'
        },
        children: [
          {
            path: '',
            name: 'SiteList',
            component: () => import('@/views/sites/List.vue'),
            meta: {
              title: '网站列表',
              breadcrumb: true,
              fullWidth: true
            }
          },
          {
            path: 'create',
            name: 'SiteCreate',
            component: () => import('@/views/sites/Create.vue'),
            meta: {
              title: '添加网站',
              breadcrumb: true,
              permission: 'site.create'
            }
          },
          {
            path: ':id/edit',
            name: 'SiteEdit',
            component: () => import('@/views/sites/Edit.vue'),
            meta: {
              title: '编辑网站',
              breadcrumb: true,
              permission: 'site.update'
            }
          },
          {
            path: ':id/code',
            name: 'SiteCode',
            component: () => import('@/views/sites/TrackingCode.vue'),
            meta: {
              title: '跟踪代码',
              breadcrumb: true,
              permission: 'site.view'
            }
          }
        ]
      },
      // 统计分析
      {
        path: 'stats',
        name: 'Stats',
        meta: {
          title: '统计分析',
          icon: 'TrendCharts',
          permission: 'stats.view'
        },
        children: [
          {
            path: '',
            name: 'StatsOverview',
            component: () => import('@/views/stats/Overview.vue'),
            meta: {
              title: '数据概览',
              breadcrumb: true
            }
          },
          {
            path: 'realtime',
            name: 'StatsRealtime',
            component: () => import('@/views/stats/Realtime.vue'),
            meta: {
              title: '实时统计',
              breadcrumb: true
            }
          },
          {
            path: 'reports',
            name: 'StatsReports',
            component: () => import('@/views/stats/Reports.vue'),
            meta: {
              title: '统计报告',
              breadcrumb: true
            }
          }
        ]
      },
      // 个人设置
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/Index.vue'),
        meta: {
          title: '个人设置',
          icon: 'User',
          breadcrumb: true
        }
      },
      // 系统管理
      {
        path: 'admin',
        name: 'Admin',
        meta: {
          title: '系统管理',
          icon: 'Setting',
          permission: 'admin.view'
        },
        children: [
          {
            path: 'users',
            name: 'AdminUsers',
            component: () => import('@/views/admin/Users.vue'),
            meta: {
              title: '用户管理',
              breadcrumb: true
            }
          },
          {
            path: 'settings',
            name: 'AdminSettings',
            component: () => import('@/views/admin/Settings.vue'),
            meta: {
              title: '系统设置',
              breadcrumb: true
            }
          },
          {
            path: 'logs',
            name: 'AdminLogs',
            component: () => import('@/views/admin/Logs.vue'),
            meta: {
              title: '操作日志',
              breadcrumb: true
            }
          }
        ]
      }
    ]
  },
  // 错误页面 - 暂时注释，待实现
  // {
  //   path: '/403',
  //   name: 'Forbidden',
  //   component: () => import('@/views/error/403.vue'),
  //   meta: {
  //     title: '权限不足',
  //     hidden: true
  //   }
  // },
  // {
  //   path: '/404',
  //   name: 'NotFound',
  //   component: () => import('@/views/error/404.vue'),
  //   meta: {
  //     title: '页面不存在',
  //     hidden: true
  //   }
  // },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/dashboard'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const userStore = useUserStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)

  console.log('路由守卫:', {
    to: to.path,
    from: from.path,
    requiresAuth,
    isLoggedIn: userStore.isLoggedIn,
    hasUser: !!userStore.user
  })
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - WebStats`
  }
  
  // 检查是否需要认证
  if (requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 如果用户信息不存在，尝试获取（但不强制要求成功）
    if (!userStore.user) {
      await userStore.initUserState()
      // 即使获取用户信息失败，只要有token就允许访问
      if (!userStore.token) {
        next('/login')
        return
      }
    }
    
    // 检查权限
    if (to.meta.permission && !userStore.hasPermission(to.meta.permission as string)) {
      ElMessage.error('权限不足')
      next('/403')
      return
    }
  } else {
    // 不需要认证的页面，允许访问
    // 注释掉自动跳转逻辑，避免与登录页面的手动跳转冲突
    // if (userStore.isLoggedIn && (to.path === '/login' || to.path === '/register')) {
    //   console.log('用户已登录，从登录页跳转到仪表板')
    //   next('/dashboard')
    //   return
    // }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

// 错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
