import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hidden: true,
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      hidden: true,
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard',
          breadcrumb: true,
          fullWidth: true
        }
      },
      {
        path: 'sites',
        name: 'Sites',
        meta: {
          title: '网站管理',
          icon: 'Monitor',
          permission: 'site.view'
        },
        children: [
          {
            path: '',
            name: 'SiteList',
            component: () => import('@/views/sites/List.vue'),
            meta: {
              title: '网站列表',
              breadcrumb: true,
              fullWidth: true
            }
          },
          {
            path: 'create',
            name: 'SiteCreate',
            component: () => import('@/views/sites/Create.vue'),
            meta: {
              title: '添加网站',
              breadcrumb: true,
              permission: 'site.create'
            }
          },
          {
            path: ':id/edit',
            name: 'SiteEdit',
            component: () => import('@/views/sites/Edit.vue'),
            meta: {
              title: '编辑网站',
              breadcrumb: true,
              permission: 'site.update'
            }
          },
          {
            path: ':id/code',
            name: 'SiteCode',
            component: () => import('@/views/sites/TrackingCode.vue'),
            meta: {
              title: '跟踪代码',
              breadcrumb: true,
              permission: 'site.view'
            }
          }
        ]
      },
      // 统计分析 - 暂时注释，待实现
      // {
      //   path: 'stats',
      //   name: 'Stats',
      //   meta: {
      //     title: '统计分析',
      //     icon: 'TrendCharts',
      //     permission: 'stats.view'
      //   },
      //   children: []
      // },
      // 系统管理 - 暂时注释，待实现
      // {
      //   path: 'system',
      //   name: 'System',
      //   meta: {
      //     title: '系统管理',
      //     icon: 'Setting',
      //     permission: 'system.manage'
      //   },
      //   children: []
      // },
      // 个人中心 - 暂时注释，待实现
      // {
      //   path: 'profile',
      //   name: 'Profile',
      //   component: () => import('@/views/profile/Index.vue'),
      //   meta: {
      //     title: '个人中心',
      //     icon: 'User',
      //     breadcrumb: true,
      //     hidden: true
      //   }
      // }
    ]
  },
  // 错误页面 - 暂时注释，待实现
  // {
  //   path: '/403',
  //   name: 'Forbidden',
  //   component: () => import('@/views/error/403.vue'),
  //   meta: {
  //     title: '权限不足',
  //     hidden: true
  //   }
  // },
  // {
  //   path: '/404',
  //   name: 'NotFound',
  //   component: () => import('@/views/error/404.vue'),
  //   meta: {
  //     title: '页面不存在',
  //     hidden: true
  //   }
  // },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/dashboard'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - WebStats`
  }
  
  // 检查是否需要认证
  if (requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 如果用户信息不存在，尝试获取
    if (!userStore.user) {
      const success = await userStore.initUserState()
      if (!success) {
        next('/login')
        return
      }
    }
    
    // 检查权限
    if (to.meta.permission && !userStore.hasPermission(to.meta.permission as string)) {
      ElMessage.error('权限不足')
      next('/403')
      return
    }
  } else {
    // 不需要认证的页面，如果已登录则跳转到首页
    if (userStore.isLoggedIn && (to.path === '/login' || to.path === '/register')) {
      next('/dashboard')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

// 错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
