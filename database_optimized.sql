-- 网站统计系统数据库优化版本
-- 基于database_simple.sql的性能优化和功能完善
-- 创建时间: 2025-01-02
-- 优化重点: 分区策略、索引优化、查询性能提升

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS web_stats DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE web_stats;

-- ============================================================================
-- 字典表设计（性能优化核心）
-- ============================================================================

-- 搜索引擎字典表（优化版）
CREATE TABLE search_engines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '搜索引擎名称',
    domain VARCHAR(100) COMMENT '搜索引擎域名',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_domain (domain),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索引擎字典表';

-- 关键词字典表（优化版）
CREATE TABLE keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(200) UNIQUE NOT NULL COMMENT '关键词',
    keyword_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '关键词哈希值',
    keyword_length TINYINT NOT NULL COMMENT '关键词长度',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_count INT DEFAULT 1 COMMENT '总出现次数',
    INDEX idx_keyword (keyword),
    INDEX idx_keyword_hash (keyword_hash),
    INDEX idx_last_seen (last_seen),
    INDEX idx_total_count (total_count DESC),
    INDEX idx_length (keyword_length)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词字典表';

-- 浏览器字典表（优化版）
CREATE TABLE browsers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '浏览器名称',
    version VARCHAR(20) COMMENT '版本号',
    major_version INT COMMENT '主版本号',
    is_mobile TINYINT DEFAULT 0 COMMENT '是否移动浏览器',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_browser_version (name, version),
    INDEX idx_name (name),
    INDEX idx_mobile (is_mobile),
    INDEX idx_major_version (major_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='浏览器字典表';

-- 操作系统字典表（优化版）
CREATE TABLE operating_systems (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '操作系统名称',
    version VARCHAR(20) COMMENT '版本号',
    platform ENUM('desktop', 'mobile', 'tablet', 'other') DEFAULT 'other' COMMENT '平台类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_os_version (name, version),
    INDEX idx_name (name),
    INDEX idx_platform (platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作系统字典表';

-- 地区字典表（优化版）
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country VARCHAR(50) NOT NULL COMMENT '国家',
    country_code VARCHAR(2) COMMENT '国家代码',
    province VARCHAR(50) COMMENT '省份',
    province_code VARCHAR(10) COMMENT '省份代码',
    city VARCHAR(50) COMMENT '城市',
    city_code VARCHAR(10) COMMENT '城市代码',
    latitude DECIMAL(10,8) COMMENT '纬度',
    longitude DECIMAL(11,8) COMMENT '经度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_region (country, province, city),
    INDEX idx_country (country),
    INDEX idx_country_code (country_code),
    INDEX idx_province (province),
    INDEX idx_city (city),
    INDEX idx_location (latitude, longitude)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区字典表';

-- 页面URL字典表（优化版）
CREATE TABLE page_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(500) UNIQUE NOT NULL COMMENT '页面URL',
    url_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'URL哈希值',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    path VARCHAR(400) COMMENT '路径',
    query_params VARCHAR(500) COMMENT '查询参数',
    title VARCHAR(200) COMMENT '页面标题',
    page_type ENUM('page', 'api', 'resource', 'other') DEFAULT 'page' COMMENT '页面类型',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_views BIGINT DEFAULT 0 COMMENT '总浏览次数',
    INDEX idx_url_hash (url_hash),
    INDEX idx_domain (domain),
    INDEX idx_path (path(100)),
    INDEX idx_page_type (page_type),
    INDEX idx_total_views (total_views DESC),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面URL字典表';

-- 来源域名字典表（优化版）
CREATE TABLE referer_domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(100) UNIQUE NOT NULL COMMENT '来源域名',
    domain_type ENUM('search_engine', 'social_media', 'direct', 'external', 'internal') DEFAULT 'external' COMMENT '域名类型',
    is_trusted TINYINT DEFAULT 0 COMMENT '是否可信来源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_domain_type (domain_type),
    INDEX idx_trusted (is_trusted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='来源域名字典表';

-- 会话字典表（优化版）
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '会话哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    INDEX idx_session_hash (session_hash),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话字典表';

-- IP地址字典表（优化版）
CREATE TABLE ip_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_int INT UNSIGNED COMMENT 'IPv4地址的整数表示',
    ip_string VARCHAR(45) COMMENT '原始IP字符串（支持IPv6）',
    ip_version TINYINT DEFAULT 4 COMMENT 'IP版本（4或6）',
    is_internal TINYINT DEFAULT 0 COMMENT '是否内网IP',
    is_bot TINYINT DEFAULT 0 COMMENT '是否机器人IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_int (ip_int),
    INDEX idx_ip_string (ip_string),
    INDEX idx_ip_version (ip_version),
    INDEX idx_is_internal (is_internal),
    INDEX idx_is_bot (is_bot)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP地址字典表';

-- User Agent字典表（优化版）
CREATE TABLE user_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ua_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'User Agent哈希值',
    user_agent TEXT COMMENT '完整User Agent字符串',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    device_type ENUM('desktop', 'mobile', 'tablet', 'bot', 'other') DEFAULT 'other' COMMENT '设备类型',
    is_bot TINYINT DEFAULT 0 COMMENT '是否机器人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ua_hash (ua_hash),
    INDEX idx_browser_os (browser_id, os_id),
    INDEX idx_device_type (device_type),
    INDEX idx_is_bot (is_bot),
    FOREIGN KEY (browser_id) REFERENCES browsers(id) ON DELETE SET NULL,
    FOREIGN KEY (os_id) REFERENCES operating_systems(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User Agent字典表';

-- ============================================================================
-- 用户管理相关表（优化版）
-- ============================================================================

-- 用户表（优化版）
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('user', 'admin', 'super_admin') DEFAULT 'user' COMMENT '用户角色',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    email_verified TINYINT DEFAULT 0 COMMENT '邮箱是否验证',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role),
    INDEX idx_last_login (last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 网站表（优化版）
CREATE TABLE sites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    site_name VARCHAR(100) NOT NULL COMMENT '网站名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '网站URL',
    site_type VARCHAR(50) DEFAULT 'other' COMMENT '网站类型',
    category ENUM('blog', 'ecommerce', 'news', 'corporate', 'entertainment', 'education', 'other') DEFAULT 'other' COMMENT '网站分类',
    region VARCHAR(50) COMMENT '网站地区',
    description TEXT COMMENT '网站描述',
    tracking_code VARCHAR(32) UNIQUE NOT NULL COMMENT '统计代码ID',
    view_password VARCHAR(50) COMMENT '查看密码',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_tracking_code (tracking_code),
    INDEX idx_domain (domain),
    INDEX idx_status (status),
    INDEX idx_category (category),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站表';

-- 访客表（优化版）
CREATE TABLE visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_hash VARCHAR(64) NOT NULL COMMENT '访客唯一标识',
    first_visit TIMESTAMP NOT NULL COMMENT '首次访问时间',
    last_visit TIMESTAMP NOT NULL COMMENT '最后访问时间',
    total_visits INT DEFAULT 1 COMMENT '总访问次数',
    total_page_views INT DEFAULT 1 COMMENT '总页面浏览数',
    total_duration INT DEFAULT 0 COMMENT '总访问时长(秒)',
    avg_session_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均会话时长',
    bounce_count INT DEFAULT 0 COMMENT '跳出次数',
    conversion_count INT DEFAULT 0 COMMENT '转化次数',
    last_region_id INT COMMENT '最后访问地区ID',
    last_device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '最后使用设备类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_site_visitor (site_id, visitor_hash),
    INDEX idx_site_id (site_id),
    INDEX idx_last_visit (last_visit),
    INDEX idx_total_visits (total_visits DESC),
    INDEX idx_device_type (last_device_type),
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (last_region_id) REFERENCES regions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客表';

-- ============================================================================
-- 核心访问记录表（优化分区表）
-- ============================================================================

-- 访问记录表（高度优化版）
CREATE TABLE visits (
    id BIGINT AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    ip_id INT NOT NULL COMMENT 'IP地址ID',
    user_agent_id INT COMMENT '用户代理ID',
    referer_domain_id INT COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    landing_page_id BIGINT NOT NULL COMMENT '入口页面ID',
    exit_page_id BIGINT COMMENT '退出页面ID',

    -- 时间字段优化（支持更精确的时间分析）
    visit_time TIMESTAMP NOT NULL COMMENT '访问时间',
    visit_date DATE NOT NULL COMMENT '访问日期',
    visit_hour TINYINT NOT NULL COMMENT '访问小时（0-23）',
    visit_minute TINYINT NOT NULL COMMENT '访问分钟（0-59）',
    visit_year SMALLINT NOT NULL COMMENT '访问年份',
    visit_month TINYINT NOT NULL COMMENT '访问月份（1-12）',
    visit_week TINYINT NOT NULL COMMENT '访问周数（1-53）',
    visit_weekday TINYINT NOT NULL COMMENT '星期几（1-7）',
    visit_quarter TINYINT NOT NULL COMMENT '季度（1-4）',

    -- 访问行为数据
    visit_duration INT DEFAULT 0 COMMENT '访问时长(秒)',
    page_views INT DEFAULT 1 COMMENT '页面浏览数',
    is_new_visitor TINYINT DEFAULT 0 COMMENT '是否新访客',
    is_bounce TINYINT DEFAULT 0 COMMENT '是否跳出',
    is_conversion TINYINT DEFAULT 0 COMMENT '是否转化',

    -- 访客环境信息
    region_id INT COMMENT '地区ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    screen_width SMALLINT COMMENT '屏幕宽度',
    screen_height SMALLINT COMMENT '屏幕高度',
    viewport_width SMALLINT COMMENT '视口宽度',
    viewport_height SMALLINT COMMENT '视口高度',
    color_depth TINYINT COMMENT '颜色深度',
    language VARCHAR(10) COMMENT '语言',
    timezone VARCHAR(50) COMMENT '时区',

    -- 来源分析
    search_engine_id INT COMMENT '搜索引擎ID',
    search_keyword_id BIGINT COMMENT '搜索关键词ID',
    utm_source VARCHAR(100) COMMENT 'UTM来源',
    utm_medium VARCHAR(100) COMMENT 'UTM媒介',
    utm_campaign VARCHAR(100) COMMENT 'UTM活动',
    utm_term VARCHAR(100) COMMENT 'UTM关键词',
    utm_content VARCHAR(100) COMMENT 'UTM内容',

    -- 技术信息
    connection_type ENUM('unknown', '2g', '3g', '4g', '5g', 'wifi', 'ethernet') DEFAULT 'unknown' COMMENT '连接类型',
    is_mobile TINYINT DEFAULT 0 COMMENT '是否移动设备',
    is_tablet TINYINT DEFAULT 0 COMMENT '是否平板设备',
    is_bot TINYINT DEFAULT 0 COMMENT '是否机器人',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 复合主键（包含分区键）
    PRIMARY KEY (id, visit_year, visit_month),

    -- 高性能索引设计
    INDEX idx_site_date (site_id, visit_date),
    INDEX idx_site_date_hour (site_id, visit_date, visit_hour),
    INDEX idx_site_time_range (site_id, visit_time),
    INDEX idx_visitor_date (visitor_id, visit_date),
    INDEX idx_session_date (session_id, visit_date),

    -- 分析专用索引
    INDEX idx_analytics_basic (site_id, visit_date, device_type, is_new_visitor),
    INDEX idx_analytics_source (site_id, visit_date, search_engine_id),
    INDEX idx_analytics_behavior (site_id, visit_date, is_bounce, is_conversion),
    INDEX idx_analytics_device (site_id, visit_date, is_mobile, is_tablet),

    -- 覆盖索引（减少回表查询）
    INDEX idx_covering_basic (site_id, visit_date, visitor_id, page_views, visit_duration, is_new_visitor),
    INDEX idx_covering_source (site_id, visit_date, search_engine_id, search_keyword_id, referer_domain_id),
    INDEX idx_covering_geo (site_id, visit_date, region_id, device_type),

    -- 外键约束
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (ip_id) REFERENCES ip_addresses(id) ON DELETE RESTRICT,
    FOREIGN KEY (user_agent_id) REFERENCES user_agents(id) ON DELETE SET NULL,
    FOREIGN KEY (referer_domain_id) REFERENCES referer_domains(id) ON DELETE SET NULL,
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id) ON DELETE SET NULL,
    FOREIGN KEY (landing_page_id) REFERENCES page_urls(id) ON DELETE RESTRICT,
    FOREIGN KEY (exit_page_id) REFERENCES page_urls(id) ON DELETE SET NULL,
    FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
    FOREIGN KEY (browser_id) REFERENCES browsers(id) ON DELETE SET NULL,
    FOREIGN KEY (os_id) REFERENCES operating_systems(id) ON DELETE SET NULL,
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id) ON DELETE SET NULL,
    FOREIGN KEY (search_keyword_id) REFERENCES keywords(id) ON DELETE SET NULL

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访问记录表（优化分区版）'
-- 按月分区，支持自动分区管理
PARTITION BY RANGE (visit_year * 100 + visit_month) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 页面浏览记录表（优化版）
CREATE TABLE page_views (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    visit_id BIGINT NOT NULL COMMENT '访问记录ID',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',

    -- 时间信息
    view_time TIMESTAMP NOT NULL COMMENT '浏览时间',
    view_date DATE NOT NULL COMMENT '浏览日期',
    view_hour TINYINT NOT NULL COMMENT '浏览小时',
    view_minute TINYINT NOT NULL COMMENT '浏览分钟',

    -- 页面行为数据
    stay_duration INT DEFAULT 0 COMMENT '停留时长(秒)',
    scroll_depth INT DEFAULT 0 COMMENT '滚动深度百分比',
    max_scroll_depth INT DEFAULT 0 COMMENT '最大滚动深度',
    click_count INT DEFAULT 0 COMMENT '点击次数',
    is_bounce TINYINT DEFAULT 0 COMMENT '是否跳出',
    is_exit TINYINT DEFAULT 0 COMMENT '是否退出页面',
    is_entrance TINYINT DEFAULT 0 COMMENT '是否入口页面',

    -- 页面性能数据
    load_time INT COMMENT '页面加载时间(毫秒)',
    dom_ready_time INT COMMENT 'DOM就绪时间(毫秒)',
    first_paint_time INT COMMENT '首次绘制时间(毫秒)',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_site_date (site_id, view_date),
    INDEX idx_site_time (site_id, view_time),
    INDEX idx_visitor_date (visitor_id, view_date),
    INDEX idx_session_time (session_id, view_time),
    INDEX idx_page_url (site_id, page_url_id),
    INDEX idx_page_performance (site_id, view_date, load_time),
    INDEX idx_behavior (site_id, view_date, is_bounce, is_exit),

    -- 覆盖索引
    INDEX idx_covering_page_stats (site_id, view_date, page_url_id, stay_duration, scroll_depth),

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id) ON DELETE CASCADE,
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面浏览记录表';

-- ============================================================================
-- 预聚合统计表（高性能查询支持）
-- ============================================================================

-- 小时级预聚合表（实时统计支持）
CREATE TABLE stats_hourly (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    stat_hour TINYINT NOT NULL,
    stat_datetime TIMESTAMP NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数量',
    session_count INT DEFAULT 0 COMMENT '会话数',
    new_visitors INT DEFAULT 0 COMMENT '新访客数',
    returning_visitors INT DEFAULT 0 COMMENT '回访访客数',

    -- 行为指标
    bounce_count INT DEFAULT 0 COMMENT '跳出次数',
    bounce_rate DECIMAL(5,2) DEFAULT 0 COMMENT '跳出率',
    total_duration BIGINT DEFAULT 0 COMMENT '总访问时长',
    avg_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均访问时长',
    avg_page_views DECIMAL(5,2) DEFAULT 0 COMMENT '平均页面浏览数',

    -- 设备分布
    desktop_pv INT DEFAULT 0 COMMENT '桌面端PV',
    mobile_pv INT DEFAULT 0 COMMENT '移动端PV',
    tablet_pv INT DEFAULT 0 COMMENT '平板端PV',

    -- 来源分布
    search_pv INT DEFAULT 0 COMMENT '搜索引擎PV',
    direct_pv INT DEFAULT 0 COMMENT '直接访问PV',
    external_pv INT DEFAULT 0 COMMENT '外部链接PV',
    social_pv INT DEFAULT 0 COMMENT '社交媒体PV',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_site_datetime (site_id, stat_datetime),
    INDEX idx_site_date_hour (site_id, stat_date, stat_hour),
    INDEX idx_datetime (stat_datetime),

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小时级预聚合表';

-- 日级预聚合表（优化版）
CREATE TABLE stats_daily (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数量',
    session_count INT DEFAULT 0 COMMENT '会话数',
    new_visitors INT DEFAULT 0 COMMENT '新访客数',
    returning_visitors INT DEFAULT 0 COMMENT '回访访客数',

    -- 行为指标
    bounce_count INT DEFAULT 0 COMMENT '跳出次数',
    bounce_rate DECIMAL(5,2) DEFAULT 0 COMMENT '跳出率',
    total_duration BIGINT DEFAULT 0 COMMENT '总访问时长',
    avg_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均访问时长',
    avg_page_views DECIMAL(5,2) DEFAULT 0 COMMENT '平均页面浏览数',
    conversion_count INT DEFAULT 0 COMMENT '转化次数',
    conversion_rate DECIMAL(5,2) DEFAULT 0 COMMENT '转化率',

    -- 设备分布
    desktop_pv INT DEFAULT 0 COMMENT '桌面端PV',
    desktop_uv INT DEFAULT 0 COMMENT '桌面端UV',
    mobile_pv INT DEFAULT 0 COMMENT '移动端PV',
    mobile_uv INT DEFAULT 0 COMMENT '移动端UV',
    tablet_pv INT DEFAULT 0 COMMENT '平板端PV',
    tablet_uv INT DEFAULT 0 COMMENT '平板端UV',

    -- 来源分布
    search_pv INT DEFAULT 0 COMMENT '搜索引擎PV',
    search_uv INT DEFAULT 0 COMMENT '搜索引擎UV',
    direct_pv INT DEFAULT 0 COMMENT '直接访问PV',
    direct_uv INT DEFAULT 0 COMMENT '直接访问UV',
    external_pv INT DEFAULT 0 COMMENT '外部链接PV',
    external_uv INT DEFAULT 0 COMMENT '外部链接UV',
    social_pv INT DEFAULT 0 COMMENT '社交媒体PV',
    social_uv INT DEFAULT 0 COMMENT '社交媒体UV',

    -- 地理分布（Top5）
    top_country VARCHAR(100) COMMENT '主要国家',
    top_province VARCHAR(100) COMMENT '主要省份',
    top_city VARCHAR(100) COMMENT '主要城市',

    -- 页面性能
    avg_load_time DECIMAL(8,2) DEFAULT 0 COMMENT '平均加载时间',
    avg_scroll_depth DECIMAL(5,2) DEFAULT 0 COMMENT '平均滚动深度',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_site_date (site_id, stat_date),
    INDEX idx_site_date (site_id, stat_date),
    INDEX idx_date (stat_date),

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日级预聚合表';

-- 实时统计表（优化版）
CREATE TABLE realtime_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    time_slot TIMESTAMP NOT NULL COMMENT '时间槽（每5分钟）',

    -- 实时指标
    pv_5min INT DEFAULT 0 COMMENT '5分钟PV',
    uv_5min INT DEFAULT 0 COMMENT '5分钟UV',
    online_count INT DEFAULT 0 COMMENT '当前在线人数',
    active_sessions INT DEFAULT 0 COMMENT '活跃会话数',

    -- 实时行为
    new_visitors_5min INT DEFAULT 0 COMMENT '5分钟新访客',
    bounce_count_5min INT DEFAULT 0 COMMENT '5分钟跳出数',
    conversion_count_5min INT DEFAULT 0 COMMENT '5分钟转化数',

    -- 实时来源
    search_pv_5min INT DEFAULT 0 COMMENT '5分钟搜索PV',
    direct_pv_5min INT DEFAULT 0 COMMENT '5分钟直接PV',
    external_pv_5min INT DEFAULT 0 COMMENT '5分钟外部PV',

    -- 实时设备
    mobile_pv_5min INT DEFAULT 0 COMMENT '5分钟移动端PV',
    desktop_pv_5min INT DEFAULT 0 COMMENT '5分钟桌面端PV',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_site_time (site_id, time_slot),
    INDEX idx_time_slot (time_slot),
    INDEX idx_site_time (site_id, time_slot),

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时统计表';

-- 热门内容统计表
CREATE TABLE popular_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    page_url_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,

    -- 内容指标
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    avg_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均停留时长',
    bounce_rate DECIMAL(5,2) DEFAULT 0 COMMENT '跳出率',
    exit_rate DECIMAL(5,2) DEFAULT 0 COMMENT '退出率',
    scroll_depth DECIMAL(5,2) DEFAULT 0 COMMENT '平均滚动深度',

    -- 排名信息
    pv_rank INT DEFAULT 0 COMMENT 'PV排名',
    uv_rank INT DEFAULT 0 COMMENT 'UV排名',
    duration_rank INT DEFAULT 0 COMMENT '停留时长排名',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_site_page_date (site_id, page_url_id, stat_date),
    INDEX idx_site_date (site_id, stat_date),
    INDEX idx_pv_rank (site_id, stat_date, pv_rank),
    INDEX idx_uv_rank (site_id, stat_date, uv_rank),

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='热门内容统计表';

-- ============================================================================
-- 初始化数据（优化版）
-- ============================================================================

-- 插入默认搜索引擎数据
INSERT INTO search_engines (name, domain, is_active) VALUES
('百度', 'baidu.com', 1),
('Google', 'google.com', 1),
('360搜索', '360.cn', 1),
('搜狗', 'sogou.com', 1),
('神马搜索', 'sm.cn', 1),
('夸克搜索', 'quark.cn', 1),
('头条搜索', 'toutiao.com', 1),
('Bing', 'bing.com', 1),
('Yahoo', 'yahoo.com', 1),
('DuckDuckGo', 'duckduckgo.com', 1),
('Yandex', 'yandex.com', 1),
('Naver', 'naver.com', 1);

-- 插入默认浏览器数据
INSERT INTO browsers (name, version, major_version, is_mobile) VALUES
('Chrome', '120.0', 120, 0),
('Chrome Mobile', '120.0', 120, 1),
('Firefox', '119.0', 119, 0),
('Firefox Mobile', '119.0', 119, 1),
('Safari', '17.0', 17, 0),
('Safari Mobile', '17.0', 17, 1),
('Edge', '119.0', 119, 0),
('Edge Mobile', '119.0', 119, 1),
('Opera', '105.0', 105, 0),
('Opera Mobile', '105.0', 105, 1),
('IE', '11.0', 11, 0),
('UC Browser', '15.0', 15, 1),
('QQ Browser', '12.0', 12, 1),
('WeChat', '8.0', 8, 1);

-- 插入默认操作系统数据
INSERT INTO operating_systems (name, version, platform) VALUES
('Windows', '11', 'desktop'),
('Windows', '10', 'desktop'),
('macOS', '14.0', 'desktop'),
('macOS', '13.0', 'desktop'),
('Linux', 'Ubuntu 22.04', 'desktop'),
('Linux', 'CentOS 8', 'desktop'),
('iOS', '17.0', 'mobile'),
('iOS', '16.0', 'mobile'),
('Android', '14.0', 'mobile'),
('Android', '13.0', 'mobile'),
('Android', '12.0', 'mobile'),
('iPadOS', '17.0', 'tablet'),
('HarmonyOS', '4.0', 'mobile');

-- 插入默认地区数据
INSERT INTO regions (country, country_code, province, province_code, city, city_code) VALUES
('中国', 'CN', '北京市', 'BJ', '北京市', 'BJ'),
('中国', 'CN', '上海市', 'SH', '上海市', 'SH'),
('中国', 'CN', '广东省', 'GD', '广州市', 'GZ'),
('中国', 'CN', '广东省', 'GD', '深圳市', 'SZ'),
('中国', 'CN', '浙江省', 'ZJ', '杭州市', 'HZ'),
('中国', 'CN', '江苏省', 'JS', '南京市', 'NJ'),
('中国', 'CN', '四川省', 'SC', '成都市', 'CD'),
('中国', 'CN', '湖北省', 'HB', '武汉市', 'WH'),
('中国', 'CN', '陕西省', 'SN', '西安市', 'XA'),
('中国', 'CN', '山东省', 'SD', '青岛市', 'QD'),
('中国', 'CN', '天津市', 'TJ', '天津市', 'TJ'),
('中国', 'CN', '重庆市', 'CQ', '重庆市', 'CQ'),
('美国', 'US', 'California', 'CA', 'San Francisco', 'SF'),
('美国', 'US', 'New York', 'NY', 'New York', 'NYC'),
('日本', 'JP', '東京都', 'TK', '東京', 'TK'),
('英国', 'GB', 'England', 'EN', 'London', 'LON');

-- 插入默认来源域名数据
INSERT INTO referer_domains (domain, domain_type, is_trusted) VALUES
('baidu.com', 'search_engine', 1),
('google.com', 'search_engine', 1),
('360.cn', 'search_engine', 1),
('sogou.com', 'search_engine', 1),
('bing.com', 'search_engine', 1),
('weibo.com', 'social_media', 1),
('qq.com', 'social_media', 1),
('wechat.com', 'social_media', 1),
('zhihu.com', 'social_media', 1),
('douyin.com', 'social_media', 1),
('bilibili.com', 'social_media', 1),
('tiktok.com', 'social_media', 1),
('facebook.com', 'social_media', 1),
('twitter.com', 'social_media', 1),
('linkedin.com', 'social_media', 1),
('github.com', 'external', 1),
('stackoverflow.com', 'external', 1);

-- ============================================================================
-- 性能优化配置
-- ============================================================================

-- 设置InnoDB缓冲池大小（建议设置为系统内存的70-80%）
-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 设置查询缓存
-- SET GLOBAL query_cache_size = 268435456; -- 256MB
-- SET GLOBAL query_cache_type = ON;

-- 设置慢查询日志
SET GLOBAL slow_query_log = ON;
SET GLOBAL long_query_time = 2;

-- ============================================================================
-- 完成提示
-- ============================================================================

SELECT 'Database optimization completed successfully!' as status;
SELECT 'Enhanced partitioning, indexing, and performance features added' as note;
SELECT 'Ready for high-volume web analytics data' as ready;
