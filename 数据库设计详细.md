# 网站统计系统数据库设计详细文档

## 数据库表结构设计

## 字典表设计（优化查询性能）

### 0.1 搜索引擎字典表 (search_engines)
```sql
CREATE TABLE search_engines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '搜索引擎名称',
    domain VARCHAR(100) COMMENT '搜索引擎域名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎字典表';

-- 初始化数据
INSERT INTO search_engines (name, domain) VALUES
('百度', 'baidu.com'),
('Google', 'google.com'),
('360搜索', '360.cn'),
('搜狗', 'sogou.com'),
('神马搜索', 'sm.cn'),
('夸克搜索', 'quark.cn'),
('头条', 'toutiao.com'),
('Bing', 'bing.com');
```

### 0.2 关键词字典表 (keywords)
```sql
CREATE TABLE keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(200) UNIQUE NOT NULL COMMENT '关键词',
    keyword_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '关键词哈希值',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_count INT DEFAULT 1 COMMENT '总出现次数',
    INDEX idx_keyword (keyword),
    INDEX idx_keyword_hash (keyword_hash),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词字典表';
```

### 0.3 浏览器字典表 (browsers)
```sql
CREATE TABLE browsers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '浏览器名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_browser_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览器字典表';
```

### 0.4 操作系统字典表 (operating_systems)
```sql
CREATE TABLE operating_systems (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '操作系统名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_os_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作系统字典表';
```

### 0.5 地区字典表 (regions)
```sql
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country VARCHAR(50) NOT NULL COMMENT '国家',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_region (country, province, city),
    INDEX idx_country (country),
    INDEX idx_province (province),
    INDEX idx_city (city)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区字典表';
```

### 0.6 页面URL字典表 (page_urls)
```sql
CREATE TABLE page_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(500) UNIQUE NOT NULL COMMENT '页面URL',
    url_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'URL哈希值',
    domain VARCHAR(100) COMMENT '域名',
    path VARCHAR(400) COMMENT '路径',
    title VARCHAR(200) COMMENT '页面标题',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    INDEX idx_url_hash (url_hash),
    INDEX idx_domain (domain),
    INDEX idx_path (path(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面URL字典表';
```

### 0.7 来源域名字典表 (referer_domains)
```sql
CREATE TABLE referer_domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(100) UNIQUE NOT NULL COMMENT '来源域名',
    domain_type ENUM('search_engine', 'social_media', 'direct', 'external') DEFAULT 'external' COMMENT '域名类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_domain_type (domain_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名字典表';
```

### 0.8 会话字典表 (sessions)
```sql
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '会话哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_hash (session_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话字典表';
```

### 0.9 IP地址字典表 (ip_addresses)
```sql
CREATE TABLE ip_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_int INT UNSIGNED COMMENT 'IPv4地址的整数表示',
    ip_string VARCHAR(45) COMMENT '原始IP字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_int (ip_int),
    INDEX idx_ip_string (ip_string)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP地址字典表';
```

### 0.10 User Agent字典表 (user_agents)
```sql
CREATE TABLE user_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ua_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'User Agent哈希值',
    user_agent TEXT COMMENT '完整User Agent字符串',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (browser_id) REFERENCES browsers(id),
    FOREIGN KEY (os_id) REFERENCES operating_systems(id),
    INDEX idx_ua_hash (ua_hash),
    INDEX idx_browser_os (browser_id, os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User Agent字典表';
```

## 数据库表结构设计

### 1. 用户管理相关表

#### 1.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 1.2 网站表 (sites)
```sql
CREATE TABLE sites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    site_name VARCHAR(100) NOT NULL COMMENT '网站名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '网站URL',
    site_type VARCHAR(50) DEFAULT 'other' COMMENT '网站类型',
    region VARCHAR(50) COMMENT '网站地区',
    description TEXT COMMENT '网站描述',
    tracking_code VARCHAR(32) UNIQUE NOT NULL COMMENT '统计代码ID',
    view_password VARCHAR(50) COMMENT '查看密码',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_tracking_code (tracking_code),
    INDEX idx_domain (domain),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';
```

### 2. 访客和会话相关表

#### 2.1 访客表 (visitors)
```sql
CREATE TABLE visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_hash VARCHAR(64) NOT NULL COMMENT '访客唯一标识',
    first_visit TIMESTAMP NOT NULL COMMENT '首次访问时间',
    last_visit TIMESTAMP NOT NULL COMMENT '最后访问时间',
    total_visits INT DEFAULT 1 COMMENT '总访问次数',
    total_page_views INT DEFAULT 1 COMMENT '总页面浏览数',
    total_duration INT DEFAULT 0 COMMENT '总访问时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_visitor (site_id, visitor_hash),
    INDEX idx_site_id (site_id),
    INDEX idx_last_visit (last_visit)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访客表';
```

#### 2.2 访问记录表 (visits) - 深度优化版
```sql
CREATE TABLE visits (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID（关联sessions表）',
    ip_id INT NOT NULL COMMENT 'IP地址ID（关联ip_addresses表）',
    user_agent_id INT COMMENT '用户代理ID（关联user_agents表）',
    referer_domain_id INT COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    landing_page_id BIGINT NOT NULL COMMENT '入口页面ID',
    exit_page_id BIGINT COMMENT '退出页面ID',

    -- 时间字段优化（关键性能提升）
    visit_time TIMESTAMP NOT NULL COMMENT '访问时间',
    visit_date DATE NOT NULL COMMENT '访问日期（冗余字段，加速查询）',
    visit_hour TINYINT NOT NULL COMMENT '访问小时（0-23）',
    visit_year SMALLINT NOT NULL COMMENT '访问年份',
    visit_month TINYINT NOT NULL COMMENT '访问月份（1-12）',
    visit_week TINYINT NOT NULL COMMENT '访问周数（1-53）',
    visit_weekday TINYINT NOT NULL COMMENT '星期几（1-7）',

    visit_duration INT DEFAULT 0 COMMENT '访问时长(秒)',
    page_views INT DEFAULT 1 COMMENT '页面浏览数',
    is_new_visitor TINYINT DEFAULT 0 COMMENT '是否新访客：1-是，0-否',
    region_id INT COMMENT '地区ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    language VARCHAR(10) COMMENT '语言',
    search_engine_id INT COMMENT '搜索引擎ID',
    search_keyword_id BIGINT COMMENT '搜索关键词ID',
    utm_source VARCHAR(100) COMMENT 'UTM来源',
    utm_medium VARCHAR(100) COMMENT 'UTM媒介',
    utm_campaign VARCHAR(100) COMMENT 'UTM活动',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 外键约束
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (ip_id) REFERENCES ip_addresses(id),
    FOREIGN KEY (user_agent_id) REFERENCES user_agents(id),
    FOREIGN KEY (referer_domain_id) REFERENCES referer_domains(id),
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id),
    FOREIGN KEY (landing_page_id) REFERENCES page_urls(id),
    FOREIGN KEY (exit_page_id) REFERENCES page_urls(id),
    FOREIGN KEY (region_id) REFERENCES regions(id),
    FOREIGN KEY (browser_id) REFERENCES browsers(id),
    FOREIGN KEY (os_id) REFERENCES operating_systems(id),
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id),
    FOREIGN KEY (search_keyword_id) REFERENCES keywords(id),

    -- 优化后的索引策略
    INDEX idx_site_date (site_id, visit_date),
    INDEX idx_site_date_hour (site_id, visit_date, visit_hour),
    INDEX idx_site_year_month (site_id, visit_year, visit_month),
    INDEX idx_site_week (site_id, visit_year, visit_week),
    INDEX idx_visitor_date (visitor_id, visit_date),
    INDEX idx_session_date (session_id, visit_date),

    -- 分析查询专用索引
    INDEX idx_analytics_basic (site_id, visit_date, device_type, is_new_visitor),
    INDEX idx_analytics_source (site_id, visit_date, search_engine_id, referer_domain_id),
    INDEX idx_analytics_region (site_id, visit_date, region_id),
    INDEX idx_analytics_page (site_id, visit_date, landing_page_id),

    -- 覆盖索引（避免回表查询）
    INDEX idx_covering_basic (site_id, visit_date, visitor_id, page_views, visit_duration, is_new_visitor),
    INDEX idx_covering_source (site_id, visit_date, search_engine_id, search_keyword_id, visitor_id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问记录表-深度优化版'
PARTITION BY RANGE (visit_year * 100 + visit_month) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 2.3 页面浏览记录表 (page_views) - 优化版
```sql
CREATE TABLE page_views (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    view_time TIMESTAMP NOT NULL COMMENT '浏览时间',
    stay_duration INT DEFAULT 0 COMMENT '停留时长(秒)',
    scroll_depth INT DEFAULT 0 COMMENT '滚动深度百分比',
    is_bounce TINYINT DEFAULT 0 COMMENT '是否跳出：1-是，0-否',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id),
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id),
    INDEX idx_site_time (site_id, view_time),
    INDEX idx_visitor_time (visitor_id, view_time),
    INDEX idx_session_time (session_id, view_time),
    INDEX idx_page_url (site_id, page_url_id),
    INDEX idx_referer_url (referer_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面浏览记录表-优化版';
```

### 3. 实时统计相关表

#### 3.1 实时在线访客表 (online_visitors)
```sql
CREATE TABLE online_visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    current_page VARCHAR(500) COMMENT '当前页面',
    last_activity TIMESTAMP NOT NULL COMMENT '最后活动时间',
    ip VARCHAR(45) NOT NULL COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_session (site_id, session_id),
    INDEX idx_site_activity (site_id, last_activity),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时在线访客表';
```

### 4. 统计汇总表

#### 4.1 日统计汇总表 (daily_stats)
```sql
CREATE TABLE daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数',
    new_visitors INT DEFAULT 0 COMMENT '新访客数',
    bounce_rate DECIMAL(5,2) DEFAULT 0 COMMENT '跳出率',
    avg_visit_duration INT DEFAULT 0 COMMENT '平均访问时长(秒)',
    avg_page_views DECIMAL(5,2) DEFAULT 0 COMMENT '平均页面浏览数',
    max_online INT DEFAULT 0 COMMENT '最高在线人数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date (site_id, date),
    INDEX idx_site_id (site_id),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日统计汇总表';
```

#### 4.2 小时统计表 (hourly_stats)
```sql
CREATE TABLE hourly_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    hour TINYINT NOT NULL COMMENT '小时(0-23)',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数',
    new_visitors INT DEFAULT 0 COMMENT '新访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date_hour (site_id, date, hour),
    INDEX idx_site_date (site_id, date),
    INDEX idx_date_hour (date, hour)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时统计表';
```

### 5. 来源分析相关表

#### 5.1 搜索引擎统计表 (search_engine_stats) - 优化版
```sql
CREATE TABLE search_engine_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    search_engine_id INT NOT NULL COMMENT '搜索引擎ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    keywords_count INT DEFAULT 0 COMMENT '关键词数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id),
    UNIQUE KEY uk_site_date_engine (site_id, date, search_engine_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_search_engine (search_engine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎统计表-优化版';
```

#### 5.2 关键词统计表 (keyword_stats) - 优化版
```sql
CREATE TABLE keyword_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    keyword_id BIGINT NOT NULL COMMENT '关键词ID',
    search_engine_id INT COMMENT '搜索引擎ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (keyword_id) REFERENCES keywords(id),
    FOREIGN KEY (search_engine_id) REFERENCES search_engines(id),
    UNIQUE KEY uk_site_date_keyword (site_id, date, keyword_id, search_engine_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_keyword (keyword_id),
    INDEX idx_search_engine (search_engine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词统计表-优化版';
```

### 6. 地区统计表

#### 6.1 地区统计表 (region_stats) - 优化版
```sql
CREATE TABLE region_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    region_id INT NOT NULL COMMENT '地区ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    ip_count INT DEFAULT 0 COMMENT 'IP数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (region_id) REFERENCES regions(id),
    UNIQUE KEY uk_site_date_region (site_id, date, region_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_region (region_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区统计表-优化版';
```

### 7. 页面和来源分析表

#### 7.1 页面统计表 (page_stats) - 优化版
```sql
CREATE TABLE page_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    page_url_id BIGINT NOT NULL COMMENT '页面URL ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    entry_count INT DEFAULT 0 COMMENT '入口次数',
    exit_count INT DEFAULT 0 COMMENT '退出次数',
    avg_stay_duration INT DEFAULT 0 COMMENT '平均停留时长(秒)',
    bounce_count INT DEFAULT 0 COMMENT '跳出次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (page_url_id) REFERENCES page_urls(id),
    UNIQUE KEY uk_site_date_page (site_id, date, page_url_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_page_url (page_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面统计表-优化版';
```

#### 7.2 来源域名统计表 (referer_stats) - 优化版
```sql
CREATE TABLE referer_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    referer_domain_id INT NOT NULL COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源URL ID',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (referer_domain_id) REFERENCES referer_domains(id),
    FOREIGN KEY (referer_url_id) REFERENCES page_urls(id),
    UNIQUE KEY uk_site_date_referer (site_id, date, referer_domain_id, referer_url_id),
    INDEX idx_site_date (site_id, date),
    INDEX idx_referer_domain (referer_domain_id),
    INDEX idx_referer_url (referer_url_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名统计表-优化版';
```

### 8. 设备和浏览器统计表

#### 8.1 设备统计表 (device_stats) - 优化版
```sql
CREATE TABLE device_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    date DATE NOT NULL COMMENT '日期',
    device_type ENUM('desktop', 'mobile', 'tablet') NOT NULL COMMENT '设备类型',
    browser_id INT NOT NULL COMMENT '浏览器ID',
    os_id INT NOT NULL COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    pv INT DEFAULT 0 COMMENT '页面浏览量',
    uv INT DEFAULT 0 COMMENT '独立访客数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (browser_id) REFERENCES browsers(id),
    FOREIGN KEY (os_id) REFERENCES operating_systems(id),
    UNIQUE KEY uk_site_date_device (site_id, date, device_type, browser_id, os_id, screen_resolution),
    INDEX idx_site_date (site_id, date),
    INDEX idx_device_type (device_type),
    INDEX idx_browser (browser_id),
    INDEX idx_os (os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备统计表-优化版';
```

### 9. 配置和管理表

#### 9.1 排除规则表 (exclude_rules)
```sql
CREATE TABLE exclude_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    rule_type ENUM('ip', 'url', 'referer', 'keyword', 'user_agent') NOT NULL COMMENT '规则类型',
    rule_value VARCHAR(500) NOT NULL COMMENT '规则值',
    match_type ENUM('exact', 'contains', 'regex', 'range') DEFAULT 'exact' COMMENT '匹配方式',
    is_case_sensitive TINYINT DEFAULT 0 COMMENT '是否区分大小写',
    description VARCHAR(200) COMMENT '规则描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    INDEX idx_site_type (site_id, rule_type),
    INDEX idx_rule_value (rule_value(100)),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排除规则表';
```

#### 9.2 站点配置表 (site_configs)
```sql
CREATE TABLE site_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_config (site_id, config_key),
    INDEX idx_site_id (site_id),
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站点配置表';
```

#### 9.3 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 10. 事件和转化跟踪表

#### 10.1 事件跟踪表 (events)
```sql
CREATE TABLE events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    event_category VARCHAR(50) NOT NULL COMMENT '事件分类',
    event_action VARCHAR(50) NOT NULL COMMENT '事件动作',
    event_label VARCHAR(100) COMMENT '事件标签',
    event_value DECIMAL(10,2) COMMENT '事件值',
    page_url VARCHAR(500) COMMENT '页面URL',
    event_time TIMESTAMP NOT NULL COMMENT '事件时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE CASCADE,
    INDEX idx_site_time (site_id, event_time),
    INDEX idx_visitor_time (visitor_id, event_time),
    INDEX idx_session_time (session_id, event_time),
    INDEX idx_category_action (event_category, event_action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事件跟踪表';
```

#### 10.2 转化目标表 (conversion_goals)
```sql
CREATE TABLE conversion_goals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    goal_name VARCHAR(100) NOT NULL COMMENT '目标名称',
    goal_type ENUM('url', 'event', 'duration', 'pages') NOT NULL COMMENT '目标类型',
    goal_value VARCHAR(200) NOT NULL COMMENT '目标值',
    goal_funnel JSON COMMENT '转化漏斗配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    INDEX idx_site_id (site_id),
    INDEX idx_goal_type (goal_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转化目标表';
```

### 11. 数据归档和清理表

#### 11.1 数据归档记录表 (archive_records)
```sql
CREATE TABLE archive_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    archive_date DATE NOT NULL COMMENT '归档日期',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    record_count INT NOT NULL COMMENT '记录数量',
    archive_file VARCHAR(255) COMMENT '归档文件路径',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_table_date (table_name, archive_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据归档记录表';
```

## 数据库优化建议

### 1. 分区策略
```sql
-- 对大表进行按月分区
ALTER TABLE visits PARTITION BY RANGE (YEAR(visit_time) * 100 + MONTH(visit_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 继续添加分区
);
```

### 2. 索引优化
- 为经常查询的字段组合创建复合索引
- 定期分析和优化慢查询
- 使用覆盖索引减少回表操作

### 3. 数据清理策略
- 定期清理超过保留期的原始数据
- 保留汇总统计数据用于历史分析
- 实现数据归档和恢复机制

## 字典表优化说明

### 1. 性能优化效果
通过使用字典表和ID关联，可以获得以下性能提升：

#### 存储空间优化
- **关键词表**: 原来每条记录存储200字符，现在只存储8字节的BIGINT ID
- **搜索引擎表**: 原来每条记录存储50字符，现在只存储4字节的INT ID
- **地区表**: 原来存储国家+省份+城市共150字符，现在只存储4字节的INT ID
- **页面URL表**: 原来每条记录存储500字符，现在只存储8字节的BIGINT ID

#### 查询性能优化
- 整数ID的比较和排序比字符串快10-100倍
- 索引大小显著减少，提高缓存命中率
- JOIN操作性能大幅提升

### 2. 数据操作示例

#### 2.1 插入访问记录的完整流程
```sql
-- 1. 获取或创建关键词ID
INSERT IGNORE INTO keywords (keyword, keyword_hash)
VALUES ('网站统计', SHA2('网站统计', 256));
SET @keyword_id = (SELECT id FROM keywords WHERE keyword = '网站统计');

-- 2. 获取或创建搜索引擎ID
SET @search_engine_id = (SELECT id FROM search_engines WHERE name = '百度');

-- 3. 获取或创建地区ID
INSERT IGNORE INTO regions (country, province, city)
VALUES ('中国', '北京市', '北京市');
SET @region_id = (SELECT id FROM regions WHERE country = '中国' AND province = '北京市' AND city = '北京市');

-- 4. 获取或创建页面URL ID
INSERT IGNORE INTO page_urls (url, url_hash, domain, path, title)
VALUES ('https://example.com/index.html', SHA2('https://example.com/index.html', 256), 'example.com', '/index.html', '首页');
SET @page_url_id = (SELECT id FROM page_urls WHERE url_hash = SHA2('https://example.com/index.html', 256));

-- 5. 获取或创建浏览器ID
INSERT IGNORE INTO browsers (name, version) VALUES ('Chrome', '120.0');
SET @browser_id = (SELECT id FROM browsers WHERE name = 'Chrome' AND version = '120.0');

-- 6. 获取或创建操作系统ID
INSERT IGNORE INTO operating_systems (name, version) VALUES ('Windows', '11');
SET @os_id = (SELECT id FROM operating_systems WHERE name = 'Windows' AND version = '11');

-- 7. 插入访问记录
INSERT INTO visits (
    site_id, visitor_id, session_id, ip, user_agent,
    landing_page_id, visit_time, region_id, device_type,
    browser_id, os_id, search_engine_id, search_keyword_id
) VALUES (
    1, 12345, 'session_abc123', '*************', 'Mozilla/5.0...',
    @page_url_id, NOW(), @region_id, 'desktop',
    @browser_id, @os_id, @search_engine_id, @keyword_id
);
```

#### 2.2 查询优化示例
```sql
-- 优化前的查询（使用字符串）
SELECT
    search_engine,
    COUNT(*) as visit_count,
    COUNT(DISTINCT visitor_id) as unique_visitors
FROM visits
WHERE site_id = 1
    AND visit_time >= '2025-01-01'
    AND search_engine = '百度'
GROUP BY search_engine;

-- 优化后的查询（使用ID）
SELECT
    se.name as search_engine,
    COUNT(*) as visit_count,
    COUNT(DISTINCT v.visitor_id) as unique_visitors
FROM visits v
JOIN search_engines se ON v.search_engine_id = se.id
WHERE v.site_id = 1
    AND v.visit_time >= '2025-01-01'
    AND v.search_engine_id = (SELECT id FROM search_engines WHERE name = '百度')
GROUP BY se.id, se.name;
```

#### 2.3 复杂查询示例
```sql
-- 获取热门关键词及其来源搜索引擎
SELECT
    k.keyword,
    se.name as search_engine,
    COUNT(*) as search_count,
    COUNT(DISTINCT v.visitor_id) as unique_visitors
FROM visits v
JOIN keywords k ON v.search_keyword_id = k.id
JOIN search_engines se ON v.search_engine_id = se.id
WHERE v.site_id = 1
    AND v.visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND v.search_keyword_id IS NOT NULL
GROUP BY k.id, k.keyword, se.id, se.name
ORDER BY search_count DESC
LIMIT 20;

-- 获取页面访问统计及地区分布
SELECT
    pu.url,
    pu.title,
    r.country,
    r.province,
    r.city,
    COUNT(*) as page_views,
    COUNT(DISTINCT v.visitor_id) as unique_visitors
FROM page_views pv
JOIN page_urls pu ON pv.page_url_id = pu.id
JOIN visits v ON pv.visitor_id = v.visitor_id AND pv.session_id = v.session_id
JOIN regions r ON v.region_id = r.id
WHERE pv.site_id = 1
    AND pv.view_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY pu.id, r.id
ORDER BY page_views DESC;
```

### 3. 数据维护策略

#### 3.1 字典表数据清理
```sql
-- 清理未使用的关键词（30天内未出现）
DELETE k FROM keywords k
LEFT JOIN visits v ON k.id = v.search_keyword_id AND v.visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
WHERE v.id IS NULL AND k.last_seen < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理未使用的页面URL（90天内未访问）
DELETE pu FROM page_urls pu
LEFT JOIN page_views pv ON pu.id = pv.page_url_id AND pv.view_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
LEFT JOIN visits v ON pu.id = v.landing_page_id AND v.visit_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
WHERE pv.id IS NULL AND v.id IS NULL AND pu.last_seen < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

#### 3.2 统计数据更新
```sql
-- 更新关键词使用统计
UPDATE keywords k
SET total_count = (
    SELECT COUNT(*) FROM visits v WHERE v.search_keyword_id = k.id
),
last_seen = (
    SELECT MAX(visit_time) FROM visits v WHERE v.search_keyword_id = k.id
)
WHERE k.id IN (
    SELECT DISTINCT search_keyword_id FROM visits
    WHERE visit_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
    AND search_keyword_id IS NOT NULL
);
```

### 4. 索引优化建议

#### 4.1 复合索引设计
```sql
-- 为常用查询组合创建复合索引
CREATE INDEX idx_visits_site_time_engine ON visits(site_id, visit_time, search_engine_id);
CREATE INDEX idx_visits_site_time_keyword ON visits(site_id, visit_time, search_keyword_id);
CREATE INDEX idx_visits_site_time_region ON visits(site_id, visit_time, region_id);
CREATE INDEX idx_pageviews_site_time_url ON page_views(site_id, view_time, page_url_id);

-- 为统计查询创建覆盖索引
CREATE INDEX idx_visits_stats_covering ON visits(site_id, visit_time, visitor_id, search_engine_id, search_keyword_id, region_id);
```

#### 4.2 分区表优化
```sql
-- 对大表进行分区优化
ALTER TABLE visits PARTITION BY RANGE (YEAR(visit_time) * 100 + MONTH(visit_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 5. 应用层优化建议

#### 5.1 缓存策略
- 将常用的字典表数据缓存到Redis中
- 使用哈希表快速查找ID映射关系
- 定期更新缓存数据

#### 5.2 批量操作
- 使用批量插入减少数据库连接开销
- 预先查询字典表ID，避免重复查询
- 使用事务保证数据一致性

#### 5.3 数据预处理
- 在应用层预处理用户代理字符串，提取浏览器和操作系统信息
- 使用IP地址库预先解析地理位置信息
- 对URL进行标准化处理，去除无关参数

## 预聚合表设计（极速查询）

### 小时级预聚合表 (stats_hourly)
```sql
CREATE TABLE stats_hourly (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    stat_hour TINYINT NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0,
    uv INT DEFAULT 0,
    ip_count INT DEFAULT 0,
    session_count INT DEFAULT 0,
    new_visitors INT DEFAULT 0,
    bounce_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,

    -- 设备分布
    desktop_pv INT DEFAULT 0,
    mobile_pv INT DEFAULT 0,
    tablet_pv INT DEFAULT 0,

    -- 来源分布
    search_pv INT DEFAULT 0,
    direct_pv INT DEFAULT 0,
    external_pv INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date_hour (site_id, stat_date, stat_hour),
    INDEX idx_site_date (site_id, stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级预聚合表';
```

### 日级预聚合表 (stats_daily)
```sql
CREATE TABLE stats_daily (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,

    -- 基础指标
    pv INT DEFAULT 0,
    uv INT DEFAULT 0,
    ip_count INT DEFAULT 0,
    session_count INT DEFAULT 0,
    new_visitors INT DEFAULT 0,
    returning_visitors INT DEFAULT 0,
    bounce_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,
    avg_duration DECIMAL(8,2) DEFAULT 0,
    avg_page_views DECIMAL(5,2) DEFAULT 0,

    -- 设备分布
    desktop_pv INT DEFAULT 0,
    desktop_uv INT DEFAULT 0,
    mobile_pv INT DEFAULT 0,
    mobile_uv INT DEFAULT 0,
    tablet_pv INT DEFAULT 0,
    tablet_uv INT DEFAULT 0,

    -- 来源分布
    search_pv INT DEFAULT 0,
    search_uv INT DEFAULT 0,
    direct_pv INT DEFAULT 0,
    direct_uv INT DEFAULT 0,
    external_pv INT DEFAULT 0,
    external_uv INT DEFAULT 0,

    -- 地区TOP5
    top_regions JSON COMMENT 'TOP5地区分布',

    -- 页面TOP10
    top_pages JSON COMMENT 'TOP10页面',

    -- 关键词TOP10
    top_keywords JSON COMMENT 'TOP10关键词',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_date (site_id, stat_date),
    INDEX idx_site_date (site_id, stat_date),
    INDEX idx_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日级预聚合表';
```

### 实时统计表 (realtime_stats)
```sql
CREATE TABLE realtime_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    time_slot TIMESTAMP NOT NULL COMMENT '时间槽（每5分钟一个槽）',

    -- 实时指标
    pv_5min INT DEFAULT 0 COMMENT '5分钟PV',
    uv_5min INT DEFAULT 0 COMMENT '5分钟UV',
    online_count INT DEFAULT 0 COMMENT '当前在线人数',

    -- 累计指标（当日）
    pv_today INT DEFAULT 0 COMMENT '今日累计PV',
    uv_today INT DEFAULT 0 COMMENT '今日累计UV',

    -- 设备分布（当前5分钟）
    desktop_pv SMALLINT DEFAULT 0,
    mobile_pv SMALLINT DEFAULT 0,
    tablet_pv SMALLINT DEFAULT 0,

    -- 来源分布（当前5分钟）
    search_pv SMALLINT DEFAULT 0,
    direct_pv SMALLINT DEFAULT 0,
    external_pv SMALLINT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_site_time (site_id, time_slot),
    INDEX idx_site_time (site_id, time_slot),
    INDEX idx_time_slot (time_slot)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时统计表';
```

### 分析专用宽表 (analytics_facts)
```sql
CREATE TABLE analytics_facts (
    site_id BIGINT NOT NULL,
    visit_date DATE NOT NULL,
    visit_hour TINYINT NOT NULL,

    -- 维度字段（全部使用ID）
    visitor_id BIGINT,
    session_id BIGINT,
    ip_id INT,
    region_id INT,
    browser_id INT,
    os_id INT,
    device_type TINYINT COMMENT '1=desktop,2=mobile,3=tablet',
    search_engine_id INT,
    search_keyword_id BIGINT,
    landing_page_id BIGINT,
    referer_domain_id INT,

    -- 度量字段
    page_views SMALLINT DEFAULT 1,
    visit_duration INT DEFAULT 0,
    is_new_visitor TINYINT DEFAULT 0,
    is_bounce TINYINT DEFAULT 0,

    -- 分区键
    partition_key INT AS (YEAR(visit_date) * 100 + MONTH(visit_date)) STORED,

    -- 专门为分析查询设计的索引
    INDEX idx_site_date_device (site_id, visit_date, device_type),
    INDEX idx_site_date_source (site_id, visit_date, search_engine_id),
    INDEX idx_site_date_region (site_id, visit_date, region_id),
    INDEX idx_site_date_page (site_id, visit_date, landing_page_id),
    INDEX idx_site_date_keyword (site_id, visit_date, search_keyword_id),

    -- 覆盖索引（包含常用度量字段）
    INDEX idx_covering_device (site_id, visit_date, device_type, visitor_id, page_views, visit_duration, is_new_visitor),
    INDEX idx_covering_source (site_id, visit_date, search_engine_id, visitor_id, page_views, is_new_visitor)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分析专用宽表'
PARTITION BY RANGE (partition_key) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```
```
