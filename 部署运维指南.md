# 网站统计系统部署运维指南

## 1. 系统环境要求

### 1.1 服务器配置
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上 SSD
- **网络**: 100Mbps以上带宽
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8

### 1.2 软件依赖
- **Go**: 1.23.9
- **Node.js**: 18.x LTS
- **MySQL**: 8.0
- **Redis**: 6.x
- **Nginx**: 1.20+

## 2. 环境搭建

### 2.1 安装Go环境
```bash
# 下载Go 1.23.9
wget https://golang.org/dl/go1.23.9.linux-amd64.tar.gz

# 解压安装
sudo tar -C /usr/local -xzf go1.23.9.linux-amd64.tar.gz

# 配置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

### 2.2 安装Node.js环境
```bash
# 使用NodeSource仓库安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2.3 安装MySQL 8.0
```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server-8.0

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
CREATE DATABASE web_stats CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'stats_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON web_stats.* TO 'stats_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2.4 安装Redis
```bash
# 安装Redis
sudo apt install redis-server

# 配置Redis
sudo vim /etc/redis/redis.conf
# 修改以下配置：
# bind 127.0.0.1
# requirepass your_redis_password
# maxmemory 2gb
# maxmemory-policy allkeys-lru

# 重启Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 2.5 安装Nginx
```bash
# 安装Nginx
sudo apt install nginx

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 3. 项目部署

### 3.1 后端部署

#### 3.1.1 编译Go应用
```bash
# 克隆项目
git clone https://github.com/your-repo/web-stats-backend.git
cd web-stats-backend

# 安装依赖
go mod download

# 编译应用
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o web-stats-api ./cmd/api

# 创建配置文件
cp config.example.yaml config.yaml
```

#### 3.1.2 配置文件 (config.yaml)
```yaml
server:
  port: 8080
  mode: production

database:
  host: localhost
  port: 3306
  username: stats_user
  password: your_password
  database: web_stats
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100

redis:
  host: localhost
  port: 6379
  password: your_redis_password
  db: 0
  pool_size: 10

jwt:
  secret: your_jwt_secret_key
  expire_hours: 24

log:
  level: info
  file: /var/log/web-stats/api.log
  max_size: 100
  max_backups: 5
  max_age: 30

tracking:
  js_domain: https://your-domain.com
  collect_endpoint: /api/track
```

#### 3.1.3 创建系统服务
```bash
# 创建服务文件
sudo vim /etc/systemd/system/web-stats-api.service
```

```ini
[Unit]
Description=Web Stats API Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/web-stats
ExecStart=/opt/web-stats/web-stats-api
Restart=always
RestartSec=5
Environment=CONFIG_PATH=/opt/web-stats/config.yaml

[Install]
WantedBy=multi-user.target
```

```bash
# 部署应用
sudo mkdir -p /opt/web-stats
sudo cp web-stats-api /opt/web-stats/
sudo cp config.yaml /opt/web-stats/
sudo chown -R www-data:www-data /opt/web-stats

# 启动服务
sudo systemctl daemon-reload
sudo systemctl start web-stats-api
sudo systemctl enable web-stats-api
```

### 3.2 前端部署

#### 3.2.1 构建前端应用
```bash
# 克隆前端项目
git clone https://github.com/your-repo/web-stats-frontend.git
cd web-stats-frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.production
```

#### 3.2.2 环境配置 (.env.production)
```env
VITE_API_BASE_URL=https://your-domain.com/api
VITE_APP_TITLE=网站统计系统
VITE_TRACKING_DOMAIN=https://your-domain.com
```

#### 3.2.3 构建和部署
```bash
# 构建生产版本
npm run build

# 部署到Nginx目录
sudo cp -r dist/* /var/www/html/
sudo chown -R www-data:www-data /var/www/html
```

### 3.3 Nginx配置

#### 3.3.1 主站点配置
```nginx
# /etc/nginx/sites-available/web-stats
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 统计JS文件
    location /js/stats.js {
        proxy_pass http://127.0.0.1:8080/js/stats.js;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 允许跨域
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        
        # 缓存配置
        expires 1h;
    }
    
    # 数据收集接口
    location /collect {
        proxy_pass http://127.0.0.1:8080/api/track/pageview;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 允许跨域
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        
        # 限流
        limit_req zone=api burst=100 nodelay;
    }
    
    # 安全配置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

#### 3.3.2 启用站点配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/web-stats /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

## 4. 数据库初始化

### 4.1 执行数据库迁移
```bash
# 进入项目目录
cd /opt/web-stats

# 执行数据库迁移
./web-stats-api migrate

# 或者手动执行SQL文件
mysql -u stats_user -p web_stats < database/migrations/001_initial_schema.sql
```

### 4.2 创建索引优化
```sql
-- 为大表创建分区（按月分区）
ALTER TABLE visits PARTITION BY RANGE (YEAR(visit_time) * 100 + MONTH(visit_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    -- 继续添加未来的分区
);

-- 创建复合索引
CREATE INDEX idx_visits_site_time_visitor ON visits(site_id, visit_time, visitor_id);
CREATE INDEX idx_page_views_site_time_url ON page_views(site_id, view_time, page_url(100));
```

## 5. 监控和日志

### 5.1 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/web-stats
sudo chown www-data:www-data /var/log/web-stats

# 配置日志轮转
sudo vim /etc/logrotate.d/web-stats
```

```
/var/log/web-stats/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload web-stats-api
    endscript
}
```

### 5.2 系统监控脚本
```bash
#!/bin/bash
# /opt/web-stats/scripts/health-check.sh

# 检查API服务状态
if ! systemctl is-active --quiet web-stats-api; then
    echo "$(date): API service is down, restarting..." >> /var/log/web-stats/health-check.log
    systemctl restart web-stats-api
fi

# 检查MySQL连接
if ! mysqladmin ping -h localhost -u stats_user -p'your_password' --silent; then
    echo "$(date): MySQL connection failed" >> /var/log/web-stats/health-check.log
fi

# 检查Redis连接
if ! redis-cli -a 'your_redis_password' ping > /dev/null 2>&1; then
    echo "$(date): Redis connection failed" >> /var/log/web-stats/health-check.log
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" >> /var/log/web-stats/health-check.log
fi
```

### 5.3 定时任务配置
```bash
# 编辑crontab
sudo crontab -e

# 添加定时任务
# 每5分钟检查系统健康状态
*/5 * * * * /opt/web-stats/scripts/health-check.sh

# 每小时清理过期的在线访客记录
0 * * * * /opt/web-stats/web-stats-api cleanup-online

# 每天凌晨2点执行数据汇总
0 2 * * * /opt/web-stats/web-stats-api aggregate-daily

# 每周日凌晨3点清理过期数据
0 3 * * 0 /opt/web-stats/web-stats-api cleanup-old-data
```

## 6. 数据备份策略

### 6.1 MySQL数据备份
```bash
#!/bin/bash
# /opt/web-stats/scripts/backup-mysql.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="web_stats"
DB_USER="stats_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
mysqldump -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    $DB_NAME > $BACKUP_DIR/web_stats_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/web_stats_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "web_stats_*.sql.gz" -mtime +7 -delete

echo "$(date): Database backup completed: web_stats_$DATE.sql.gz"
```

### 6.2 Redis数据备份
```bash
#!/bin/bash
# /opt/web-stats/scripts/backup-redis.sh

BACKUP_DIR="/backup/redis"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行Redis备份
redis-cli -a 'your_redis_password' BGSAVE

# 等待备份完成
while [ $(redis-cli -a 'your_redis_password' LASTSAVE) -eq $(redis-cli -a 'your_redis_password' LASTSAVE) ]; do
    sleep 1
done

# 复制RDB文件
cp /var/lib/redis/dump.rdb $BACKUP_DIR/dump_$DATE.rdb

# 压缩备份文件
gzip $BACKUP_DIR/dump_$DATE.rdb

# 删除7天前的备份
find $BACKUP_DIR -name "dump_*.rdb.gz" -mtime +7 -delete

echo "$(date): Redis backup completed: dump_$DATE.rdb.gz"
```

### 6.3 自动备份定时任务
```bash
# 添加到crontab
# 每天凌晨1点备份MySQL
0 1 * * * /opt/web-stats/scripts/backup-mysql.sh >> /var/log/web-stats/backup.log 2>&1

# 每天凌晨1:30备份Redis
30 1 * * * /opt/web-stats/scripts/backup-redis.sh >> /var/log/web-stats/backup.log 2>&1
```

## 7. 安全配置

### 7.1 防火墙配置
```bash
# 安装ufw
sudo apt install ufw

# 默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable

# 查看状态
sudo ufw status
```

### 7.2 SSL证书配置
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7.3 应用安全配置
```yaml
# config.yaml 安全配置
security:
  rate_limit:
    enabled: true
    requests_per_minute: 1000
    burst: 100

  cors:
    allowed_origins:
      - "https://your-domain.com"
      - "https://www.your-domain.com"
    allowed_methods:
      - "GET"
      - "POST"
      - "OPTIONS"

  csrf:
    enabled: true
    secret: "your_csrf_secret"

  ip_whitelist:
    admin_endpoints:
      - "127.0.0.1"
      - "your_admin_ip"
```

## 8. 性能优化

### 8.1 MySQL优化配置
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础配置
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2

# 连接配置
max_connections = 200
max_connect_errors = 1000
wait_timeout = 600
interactive_timeout = 600

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 临时表配置
tmp_table_size = 256M
max_heap_table_size = 256M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 8.2 Redis优化配置
```conf
# /etc/redis/redis.conf
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

### 8.3 应用性能优化
```yaml
# config.yaml 性能配置
performance:
  database:
    max_idle_conns: 20
    max_open_conns: 100
    conn_max_lifetime: 3600

  redis:
    pool_size: 20
    min_idle_conns: 5
    pool_timeout: 30

  cache:
    default_ttl: 3600
    max_entries: 10000

  batch_processing:
    batch_size: 1000
    flush_interval: 30
```

## 9. 故障排查

### 9.1 常见问题诊断
```bash
# 检查服务状态
sudo systemctl status web-stats-api
sudo systemctl status mysql
sudo systemctl status redis-server
sudo systemctl status nginx

# 查看日志
tail -f /var/log/web-stats/api.log
tail -f /var/log/mysql/error.log
tail -f /var/log/redis/redis-server.log
tail -f /var/log/nginx/error.log

# 检查端口占用
sudo netstat -tlnp | grep :8080
sudo netstat -tlnp | grep :3306
sudo netstat -tlnp | grep :6379

# 检查磁盘空间
df -h
du -sh /var/lib/mysql
du -sh /var/lib/redis

# 检查内存使用
free -h
ps aux --sort=-%mem | head
```

### 9.2 性能监控脚本
```bash
#!/bin/bash
# /opt/web-stats/scripts/performance-monitor.sh

LOG_FILE="/var/log/web-stats/performance.log"

# CPU使用率
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')

# 内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')

# 磁盘使用率
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

# MySQL连接数
MYSQL_CONNECTIONS=$(mysql -u stats_user -p'your_password' -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print $2}')

# Redis内存使用
REDIS_MEMORY=$(redis-cli -a 'your_redis_password' INFO memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')

# 记录性能数据
echo "$(date): CPU:${CPU_USAGE}% MEM:${MEM_USAGE}% DISK:${DISK_USAGE}% MySQL:${MYSQL_CONNECTIONS} Redis:${REDIS_MEMORY}" >> $LOG_FILE

# 告警阈值检查
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "$(date): HIGH CPU USAGE: ${CPU_USAGE}%" >> $LOG_FILE
fi

if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
    echo "$(date): HIGH MEMORY USAGE: ${MEM_USAGE}%" >> $LOG_FILE
fi

if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): HIGH DISK USAGE: ${DISK_USAGE}%" >> $LOG_FILE
fi
```

## 10. 升级和维护

### 10.1 应用升级流程
```bash
#!/bin/bash
# /opt/web-stats/scripts/upgrade.sh

# 备份当前版本
cp /opt/web-stats/web-stats-api /opt/web-stats/web-stats-api.backup

# 停止服务
sudo systemctl stop web-stats-api

# 部署新版本
cp /tmp/web-stats-api-new /opt/web-stats/web-stats-api
chmod +x /opt/web-stats/web-stats-api

# 执行数据库迁移
/opt/web-stats/web-stats-api migrate

# 启动服务
sudo systemctl start web-stats-api

# 检查服务状态
sleep 5
if systemctl is-active --quiet web-stats-api; then
    echo "Upgrade successful"
    rm /opt/web-stats/web-stats-api.backup
else
    echo "Upgrade failed, rolling back"
    cp /opt/web-stats/web-stats-api.backup /opt/web-stats/web-stats-api
    sudo systemctl start web-stats-api
fi
```

### 10.2 定期维护任务
```bash
# 每月第一个周日执行维护任务
0 3 1-7 * 0 /opt/web-stats/scripts/monthly-maintenance.sh

# monthly-maintenance.sh 内容：
#!/bin/bash
# 优化数据库表
mysql -u stats_user -p'your_password' web_stats -e "OPTIMIZE TABLE visits, page_views, daily_stats;"

# 清理过期会话
redis-cli -a 'your_redis_password' EVAL "return redis.call('del', unpack(redis.call('keys', 'session:*')))" 0

# 分析慢查询日志
mysqldumpslow /var/log/mysql/slow.log > /var/log/web-stats/slow-query-analysis.log

# 检查和修复表
mysql -u stats_user -p'your_password' web_stats -e "CHECK TABLE visits, page_views;"
```
```
