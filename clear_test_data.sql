-- 清理测试数据脚本
-- 删除所有测试数据，保留基础字典数据

USE web_stats;

-- 显示清理前的数据统计
SELECT 'Before cleanup:' as info;
SELECT 
    '用户数' as item, COUNT(*) as count FROM users
UNION ALL
SELECT 
    '网站数' as item, COUNT(*) as count FROM sites
UNION ALL
SELECT 
    '访客数' as item, COUNT(*) as count FROM visitors
UNION ALL
SELECT 
    '访问记录数' as item, COUNT(*) as count FROM visits
UNION ALL
SELECT 
    '页面浏览数' as item, COUNT(*) as count FROM page_views;

-- 按依赖关系顺序清理数据
DELETE FROM realtime_stats;
DELETE FROM stats_daily;
DELETE FROM page_views;
DELETE FROM visits;
DELETE FROM visitors;
DELETE FROM sites;
DELETE FROM users;
DELETE FROM user_agents;
DELETE FROM page_urls;
DELETE FROM keywords;
DELETE FROM sessions;
DELETE FROM ip_addresses;

-- 重置自增ID
ALTER TABLE users AUTO_INCREMENT = 1;
ALTER TABLE sites AUTO_INCREMENT = 1;
ALTER TABLE visitors AUTO_INCREMENT = 1;
ALTER TABLE visits AUTO_INCREMENT = 1;
ALTER TABLE page_views AUTO_INCREMENT = 1;
ALTER TABLE keywords AUTO_INCREMENT = 1;
ALTER TABLE page_urls AUTO_INCREMENT = 1;
ALTER TABLE user_agents AUTO_INCREMENT = 1;
ALTER TABLE sessions AUTO_INCREMENT = 1;
ALTER TABLE ip_addresses AUTO_INCREMENT = 1;
ALTER TABLE stats_daily AUTO_INCREMENT = 1;
ALTER TABLE realtime_stats AUTO_INCREMENT = 1;

-- 显示清理后的数据统计
SELECT 'After cleanup:' as info;
SELECT 
    '用户数' as item, COUNT(*) as count FROM users
UNION ALL
SELECT 
    '网站数' as item, COUNT(*) as count FROM sites
UNION ALL
SELECT 
    '访客数' as item, COUNT(*) as count FROM visitors
UNION ALL
SELECT 
    '访问记录数' as item, COUNT(*) as count FROM visits
UNION ALL
SELECT 
    '页面浏览数' as item, COUNT(*) as count FROM page_views;

SELECT 'Test data cleared successfully!' as status;
