# WebStats JavaScript SDK

WebStats JavaScript SDK 是一个轻量级、高性能的网站统计分析工具，支持页面浏览、用户行为、性能监控等多种数据收集功能。

## 特性

- 🚀 **轻量级**: 压缩后仅 ~15KB
- 📊 **全面统计**: 页面浏览、用户行为、性能指标
- 🔄 **异步加载**: 不影响页面加载性能
- 📱 **移动端优化**: 支持触摸事件和移动端特性
- 🛡️ **隐私保护**: 不收集敏感信息
- 🎯 **精确跟踪**: 防重复、防机器人
- 📈 **实时数据**: 支持实时统计和心跳检测

## 快速开始

### 1. 异步加载（推荐）

```html
<script>
  window._wsConfig = {
    siteId: 'YOUR_SITE_ID',
    apiUrl: 'https://your-api-domain.com/api/v1'
  };
</script>
<script async src="https://your-cdn.com/js/track.min.js"></script>
```

### 2. 直接引入

```html
<script src="https://your-cdn.com/js/webstats.min.js"></script>
<script>
  const tracker = WebStats.init({
    siteId: 'YOUR_SITE_ID',
    apiUrl: 'https://your-api-domain.com/api/v1'
  });
</script>
```

### 3. NPM 安装

```bash
npm install webstats-js-sdk
```

```javascript
import WebStats from 'webstats-js-sdk';

const tracker = WebStats.init({
  siteId: 'YOUR_SITE_ID',
  apiUrl: 'https://your-api-domain.com/api/v1'
});
```

## 配置选项

```javascript
const config = {
  // 必需配置
  siteId: 'YOUR_SITE_ID',                    // 网站ID
  
  // 可选配置
  apiUrl: 'https://api.example.com/v1',      // API地址
  debug: false,                              // 调试模式
  autoTrack: true,                           // 自动跟踪页面浏览
  trackPageView: true,                       // 跟踪页面浏览
  trackUserBehavior: true,                   // 跟踪用户行为
  trackPerformance: true,                    // 跟踪性能指标
  
  // 高级配置
  sessionTimeout: 30 * 60 * 1000,           // 会话超时时间（毫秒）
  heartbeatInterval: 15 * 1000,             // 心跳间隔（毫秒）
  batchSize: 10,                            // 批量发送大小
  flushInterval: 5 * 1000                   // 发送间隔（毫秒）
};
```

## API 文档

### 基础方法

#### 初始化

```javascript
// 创建跟踪器实例
const tracker = WebStats.create(config);

// 快速初始化（推荐）
const tracker = WebStats.init(config);

// 获取当前实例
const tracker = WebStats.getInstance();
```

#### 页面跟踪

```javascript
// 手动跟踪页面浏览
tracker.trackPageView();
```

#### 事件跟踪

```javascript
// 自定义事件
tracker.trackEvent('button_click', {
  button_name: '注册按钮',
  section: '首页'
});

// 转化目标
tracker.trackGoal('signup', 100);

// 用户属性
tracker.setUserProperty('user_type', 'premium');
```

#### 电商跟踪

```javascript
// 添加到购物车
tracker.trackEcommerce('add_to_cart', {
  product_id: 'product_001',
  product_name: 'iPhone 15',
  category: '电子产品',
  price: 5999,
  quantity: 1
});

// 购买完成
tracker.trackEcommerce('purchase', {
  order_id: 'ORDER_123456',
  total_amount: 5999,
  currency: 'CNY',
  products: [...]
});
```

### 信息获取

```javascript
// 获取访客信息
const visitorInfo = tracker.getVisitorInfo();

// 获取性能摘要
const performance = tracker.getPerformanceSummary();

// 获取行为统计
const behavior = tracker.getBehaviorStats();
```

### 控制方法

```javascript
// 手动发送事件队列
tracker.flush();

// 暂停跟踪
tracker.pause();

// 恢复跟踪
tracker.resume();

// 销毁跟踪器
tracker.destroy();
```

## 自动跟踪功能

### 页面浏览跟踪

- 页面加载时间
- 页面标题和URL
- 来源页面（referrer）
- 设备和浏览器信息
- 地理位置信息

### 用户行为跟踪

- **点击事件**: 按钮、链接、表单元素
- **滚动行为**: 滚动深度、滚动方向
- **表单交互**: 表单提交、字段焦点
- **下载跟踪**: 文件下载链接
- **外链跟踪**: 外部网站链接
- **键盘快捷键**: 常用快捷键检测

### 性能监控

- **核心Web指标**: FCP, LCP, FID, CLS
- **加载性能**: DNS解析、TCP连接、资源加载
- **运行时性能**: 长任务、内存使用
- **网络状况**: 连接类型、网速检测

## 高级用法

### 自定义配置

```javascript
// 通过URL参数配置
<script async src="track.js?id=SITE_ID&debug=true&api=https://api.example.com"></script>

// 通过data属性配置
<script async src="track.js" 
        data-site-id="SITE_ID" 
        data-debug="true"
        data-auto-track="true"></script>

// 通过全局配置对象
<script>
  window._wsConfig = {
    siteId: 'SITE_ID',
    debug: true,
    trackUserBehavior: false
  };
</script>
```

### 队列式调用

```javascript
// 在SDK加载前就可以调用
_ws('init', { siteId: 'SITE_ID' });
_ws('track');
_ws('event', 'signup', { method: 'email' });
_ws('goal', 'newsletter_signup');
```

### 条件跟踪

```javascript
// 根据条件启用不同功能
const tracker = WebStats.init({
  siteId: 'SITE_ID',
  trackUserBehavior: !window.location.href.includes('admin'),
  trackPerformance: window.location.hostname === 'production.com'
});
```

## 隐私和安全

### 数据收集原则

- ✅ 收集匿名化的统计数据
- ✅ 收集页面浏览和用户行为数据
- ✅ 收集性能和技术指标
- ❌ 不收集个人身份信息
- ❌ 不收集表单输入内容
- ❌ 不收集密码等敏感信息

### 合规性

- 支持 GDPR 合规
- 支持 CCPA 合规
- 提供数据删除接口
- 支持用户选择退出

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+（有限支持）

## 构建和开发

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run watch
```

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm test
```

## 示例项目

查看 `examples/` 目录中的示例文件：

- `basic-usage.html` - 基础使用示例
- `ecommerce.html` - 电商网站示例
- `spa.html` - 单页应用示例

## 常见问题

### Q: 如何确认SDK正常工作？

A: 开启调试模式，检查浏览器控制台的日志输出：

```javascript
WebStats.init({
  siteId: 'YOUR_SITE_ID',
  debug: true
});
```

### Q: 如何处理单页应用的路由变化？

A: 在路由变化时手动调用页面跟踪：

```javascript
// React Router 示例
useEffect(() => {
  tracker.trackPageView();
}, [location.pathname]);

// Vue Router 示例
router.afterEach(() => {
  tracker.trackPageView();
});
```

### Q: 如何减少数据传输量？

A: 调整批量发送配置：

```javascript
WebStats.init({
  siteId: 'YOUR_SITE_ID',
  batchSize: 20,           // 增加批量大小
  flushInterval: 10000,    // 增加发送间隔
  trackUserBehavior: false // 关闭行为跟踪
});
```

## 许可证

MIT License

## 支持

如有问题或建议，请提交 Issue 或联系技术支持。
