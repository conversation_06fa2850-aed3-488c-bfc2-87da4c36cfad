#!/bin/bash

# WebStats JS SDK 构建脚本

echo "=== WebStats JS SDK 构建开始 ==="

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "错误: npm 未安装"
    exit 1
fi

echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"

# 进入SDK目录
cd "$(dirname "$0")"

# 创建dist目录
mkdir -p dist

# 简单的文件合并构建（不依赖webpack）
echo "构建核心文件..."

# 合并核心文件
cat > dist/webstats.js << 'EOF'
/**
 * WebStats JavaScript SDK v1.0.0
 * 轻量级网站统计分析工具
 */
(function(window, document) {
    'use strict';

EOF

# 添加核心跟踪器
cat src/core/tracker.js >> dist/webstats.js

# 添加行为跟踪模块
echo "" >> dist/webstats.js
cat src/modules/behavior-tracker.js >> dist/webstats.js

# 添加性能监控模块
echo "" >> dist/webstats.js
cat src/modules/performance-monitor.js >> dist/webstats.js

# 添加主入口
echo "" >> dist/webstats.js
cat src/index.js >> dist/webstats.js

# 结束包装
cat >> dist/webstats.js << 'EOF'

})(window, document);
EOF

# 创建异步加载器
echo "构建异步加载器..."
cp src/loader/async-loader.js dist/track.js

# 创建压缩版本（简单压缩）
echo "创建压缩版本..."

# 简单的压缩：移除注释和多余空白
sed -e '/^[[:space:]]*\/\*/,/\*\//d' \
    -e '/^[[:space:]]*\/\//d' \
    -e '/^[[:space:]]*$/d' \
    -e 's/[[:space:]]\+/ /g' \
    dist/webstats.js > dist/webstats.min.js

sed -e '/^[[:space:]]*\/\*/,/\*\//d' \
    -e '/^[[:space:]]*\/\//d' \
    -e '/^[[:space:]]*$/d' \
    -e 's/[[:space:]]\+/ /g' \
    dist/track.js > dist/track.min.js

# 显示文件大小
echo ""
echo "=== 构建完成 ==="
echo "文件大小统计："
ls -lh dist/

echo ""
echo "文件列表："
echo "- webstats.js      (开发版本)"
echo "- webstats.min.js  (生产版本)"
echo "- track.js         (异步加载器开发版)"
echo "- track.min.js     (异步加载器生产版)"

echo ""
echo "使用方法："
echo "1. 异步加载: <script async src=\"/js/track.min.js?id=SITE_ID\"></script>"
echo "2. 直接引入: <script src=\"/js/webstats.min.js\"></script>"

echo ""
echo "=== 构建完成 ==="
