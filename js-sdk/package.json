{"name": "webstats-js-sdk", "version": "1.0.0", "description": "JavaScript SDK for WebStats analytics platform", "main": "dist/webstats.min.js", "scripts": {"build": "npm run build:dev && npm run build:prod", "build:dev": "webpack --mode development", "build:prod": "webpack --mode production", "build:async": "webpack --config webpack.async.config.js", "watch": "webpack --mode development --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "serve": "webpack serve --mode development", "analyze": "webpack-bundle-analyzer dist/webstats.min.js"}, "keywords": ["analytics", "tracking", "web-stats", "javascript", "sdk"], "author": "WebStats Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "terser-webpack-plugin": "^5.3.0", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "files": ["dist/", "src/", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/your-org/webstats-js-sdk.git"}, "bugs": {"url": "https://github.com/your-org/webstats-js-sdk/issues"}, "homepage": "https://github.com/your-org/webstats-js-sdk#readme"}