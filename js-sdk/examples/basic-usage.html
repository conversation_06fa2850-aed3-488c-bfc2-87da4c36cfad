<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebStats SDK 基础使用示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .example-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .log {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>WebStats JavaScript SDK 使用示例</h1>
    
    <div class="example-section">
        <h2>1. 异步加载方式（推荐）</h2>
        <div class="code">
&lt;script&gt;
  // 预设配置
  window._wsConfig = {
    siteId: 'TRACK001',
    apiUrl: 'http://localhost:9002/api/v1',
    debug: true
  };
&lt;/script&gt;
&lt;script async src="../dist/track.min.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <div class="example-section">
        <h2>2. 直接引入方式</h2>
        <div class="code">
&lt;script src="../dist/webstats.min.js"&gt;&lt;/script&gt;
&lt;script&gt;
  const tracker = WebStats.init({
    siteId: 'TRACK001',
    apiUrl: 'http://localhost:9002/api/v1',
    debug: true
  });
&lt;/script&gt;
        </div>
    </div>

    <div class="example-section">
        <h2>3. 事件跟踪示例</h2>
        
        <h3>基础事件</h3>
        <button onclick="trackCustomEvent()">跟踪自定义事件</button>
        <button onclick="trackGoal()">跟踪转化目标</button>
        <button onclick="setUserProperty()">设置用户属性</button>
        
        <h3>电商事件</h3>
        <button onclick="trackAddToCart()">添加到购物车</button>
        <button onclick="trackPurchase()">完成购买</button>
        
        <h3>表单事件</h3>
        <form onsubmit="trackFormSubmit(event)">
            <input type="text" placeholder="姓名" required>
            <input type="email" placeholder="邮箱" required>
            <button type="submit">提交表单</button>
        </form>
        
        <h3>下载链接</h3>
        <a href="sample.pdf" download>下载PDF文件</a>
        <a href="sample.zip" download>下载ZIP文件</a>
        
        <h3>外部链接</h3>
        <a href="https://www.google.com" target="_blank">访问Google</a>
        <a href="https://www.github.com" target="_blank">访问GitHub</a>
    </div>

    <div class="example-section">
        <h2>4. 获取统计信息</h2>
        <button onclick="getVisitorInfo()">获取访客信息</button>
        <button onclick="getPerformanceInfo()">获取性能信息</button>
        <button onclick="getBehaviorInfo()">获取行为统计</button>
        <button onclick="flushEvents()">手动发送事件</button>
    </div>

    <div class="example-section">
        <h2>5. 控制跟踪</h2>
        <button onclick="pauseTracking()">暂停跟踪</button>
        <button onclick="resumeTracking()">恢复跟踪</button>
        <button onclick="destroyTracker()">销毁跟踪器</button>
    </div>

    <div class="example-section">
        <h2>6. 调试日志</h2>
        <div id="log" class="log">
            <div>日志将显示在这里...</div>
        </div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <!-- 引入SDK -->
    <script src="../dist/webstats.min.js"></script>
    
    <script>
        // 初始化跟踪器
        const tracker = WebStats.init({
            siteId: 'TRACK001',
            apiUrl: 'http://localhost:9002/api/v1',
            debug: true,
            trackUserBehavior: true,
            trackPerformance: true
        });

        // 日志函数
        function log(message, data = null) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${time}]</strong> ${message}`;
            if (data) {
                logEntry.innerHTML += `<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 事件跟踪函数
        function trackCustomEvent() {
            tracker.trackEvent('button_click', {
                button_name: '自定义事件按钮',
                section: '示例页面'
            });
            log('自定义事件已发送');
        }

        function trackGoal() {
            tracker.trackGoal('demo_goal', 100);
            log('转化目标已发送');
        }

        function setUserProperty() {
            tracker.setUserProperty('user_type', 'demo_user');
            tracker.setUserProperty('plan', 'premium');
            log('用户属性已设置');
        }

        function trackAddToCart() {
            tracker.trackEcommerce('add_to_cart', {
                product_id: 'demo_product_001',
                product_name: '演示商品',
                category: '电子产品',
                price: 299.99,
                quantity: 1
            });
            log('添加到购物车事件已发送');
        }

        function trackPurchase() {
            tracker.trackEcommerce('purchase', {
                order_id: 'ORDER_' + Date.now(),
                total_amount: 299.99,
                currency: 'CNY',
                products: [
                    {
                        product_id: 'demo_product_001',
                        product_name: '演示商品',
                        price: 299.99,
                        quantity: 1
                    }
                ]
            });
            log('购买事件已发送');
        }

        function trackFormSubmit(event) {
            event.preventDefault();
            tracker.trackEvent('form_submit', {
                form_name: '演示表单',
                form_type: 'contact'
            });
            log('表单提交事件已发送');
        }

        function getVisitorInfo() {
            const info = tracker.getVisitorInfo();
            log('访客信息', info);
        }

        function getPerformanceInfo() {
            const info = tracker.getPerformanceSummary();
            log('性能信息', info);
        }

        function getBehaviorInfo() {
            const info = tracker.getBehaviorStats();
            log('行为统计', info);
        }

        function flushEvents() {
            tracker.flush();
            log('事件队列已手动发送');
        }

        function pauseTracking() {
            tracker.pause();
            log('跟踪已暂停');
        }

        function resumeTracking() {
            tracker.resume();
            log('跟踪已恢复');
        }

        function destroyTracker() {
            tracker.destroy();
            log('跟踪器已销毁');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>日志已清空...</div>';
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成，WebStats SDK 已初始化');
            log('访客ID: ' + tracker.visitorId);
            log('会话ID: ' + tracker.sessionId);
        });
    </script>
</body>
</html>
