/**
 * WebStats JavaScript SDK
 * 主入口文件
 */

// 导入核心模块
import WebStatsTracker from './core/tracker.js';
import BehaviorTracker from './modules/behavior-tracker.js';
import PerformanceMonitor from './modules/performance-monitor.js';

// 扩展核心跟踪器
class ExtendedWebStatsTracker extends WebStatsTracker {
    constructor(config = {}) {
        super(config);
        
        // 初始化扩展模块
        if (this.config.trackUserBehavior) {
            this.behaviorTracker = new BehaviorTracker(this);
        }
        
        if (this.config.trackPerformance) {
            this.performanceMonitor = new PerformanceMonitor(this);
        }
    }

    /**
     * 自定义事件跟踪
     */
    trackEvent(eventName, eventData = {}) {
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'custom',
            event_name: eventName,
            event_value: JSON.stringify(eventData),
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('custom_event', data);
        this.log('自定义事件已发送', { eventName, eventData });
    }

    /**
     * 电商事件跟踪
     */
    trackEcommerce(action, data = {}) {
        const ecommerceData = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'ecommerce',
            event_name: action,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('ecommerce', ecommerceData);
        this.log('电商事件已发送', { action, data });
    }

    /**
     * 转化目标跟踪
     */
    trackGoal(goalName, goalValue = null) {
        const goalData = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'goal',
            event_name: goalName,
            event_value: goalValue ? JSON.stringify({ value: goalValue }) : null,
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('goal', goalData);
        this.log('转化目标已发送', { goalName, goalValue });
    }

    /**
     * 设置用户属性
     */
    setUserProperty(key, value) {
        if (!this.userProperties) {
            this.userProperties = {};
        }
        
        this.userProperties[key] = value;
        
        // 发送用户属性更新事件
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            event_type: 'user_property',
            event_name: 'set_property',
            event_value: JSON.stringify({ [key]: value }),
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('user_property', data);
    }

    /**
     * 获取访客信息
     */
    getVisitorInfo() {
        return {
            visitorId: this.visitorId,
            sessionId: this.sessionId,
            isNewVisitor: !this.getCookie('_ws_visitor_id'),
            userProperties: this.userProperties || {},
            pageStartTime: this.pageStartTime,
            lastActivityTime: this.lastActivityTime
        };
    }

    /**
     * 获取性能摘要
     */
    getPerformanceSummary() {
        if (this.performanceMonitor) {
            return this.performanceMonitor.getPerformanceSummary();
        }
        return null;
    }

    /**
     * 获取行为统计
     */
    getBehaviorStats() {
        if (this.behaviorTracker) {
            return this.behaviorTracker.getBehaviorStats();
        }
        return null;
    }

    /**
     * 手动发送队列中的事件
     */
    flush() {
        this.sendQueuedEvents();
    }

    /**
     * 暂停跟踪
     */
    pause() {
        this.config.autoTrack = false;
        this.log('跟踪已暂停');
    }

    /**
     * 恢复跟踪
     */
    resume() {
        this.config.autoTrack = true;
        this.log('跟踪已恢复');
    }

    /**
     * 销毁跟踪器
     */
    destroy() {
        // 发送剩余事件
        this.sendQueuedEvents(true);
        
        // 清理定时器
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
        }
        
        if (this.batchTimer) {
            clearInterval(this.batchTimer);
        }
        
        // 清理模块
        if (this.performanceMonitor) {
            this.performanceMonitor.cleanup();
        }
        
        this.log('跟踪器已销毁');
    }
}

// 创建全局实例工厂
const WebStats = {
    // 版本信息
    version: '1.0.0',
    
    // 创建跟踪器实例
    create: function(config) {
        return new ExtendedWebStatsTracker(config);
    },
    
    // 快速初始化（自动检测配置）
    init: function(config = {}) {
        // 如果已经有全局实例，返回现有实例
        if (window._wsTracker) {
            return window._wsTracker;
        }
        
        // 创建新实例
        const tracker = new ExtendedWebStatsTracker(config);
        window._wsTracker = tracker;
        
        return tracker;
    },
    
    // 获取当前实例
    getInstance: function() {
        return window._wsTracker || null;
    }
};

// 自动初始化（如果有配置）
if (typeof window !== 'undefined') {
    // 检查是否有预设配置
    if (window._wsConfig && window._wsConfig.siteId) {
        WebStats.init(window._wsConfig);
    }
    
    // 处理队列中的命令
    if (window._wsQueue && Array.isArray(window._wsQueue)) {
        const tracker = WebStats.getInstance();
        if (tracker) {
            window._wsQueue.forEach(command => {
                if (Array.isArray(command) && command.length > 0) {
                    const [action, ...args] = command;
                    
                    switch (action) {
                        case 'init':
                            WebStats.init(args[0]);
                            break;
                        case 'track':
                            tracker.trackPageView();
                            break;
                        case 'event':
                            tracker.trackEvent(args[0], args[1]);
                            break;
                        case 'ecommerce':
                            tracker.trackEcommerce(args[0], args[1]);
                            break;
                        case 'goal':
                            tracker.trackGoal(args[0], args[1]);
                            break;
                        case 'set':
                            tracker.setUserProperty(args[0], args[1]);
                            break;
                    }
                }
            });
            
            // 清空队列
            window._wsQueue = [];
        }
    }
}

// 导出
export default WebStats;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebStats;
}

if (typeof window !== 'undefined') {
    window.WebStats = WebStats;
    window.WebStatsTracker = ExtendedWebStatsTracker;
}
