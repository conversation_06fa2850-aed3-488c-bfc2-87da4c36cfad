/**
 * 性能监控模块
 * 监控页面加载性能、资源加载、用户体验指标等
 */

class PerformanceMonitor {
    constructor(tracker) {
        this.tracker = tracker;
        this.config = tracker.config;
        
        // 性能数据
        this.performanceData = {
            navigation: null,
            resources: [],
            vitals: {},
            memory: null,
            connection: null
        };

        // 观察器
        this.observers = {};
        
        this.init();
    }

    /**
     * 初始化性能监控
     */
    init() {
        if (!this.config.trackPerformance) return;

        // 等待页面加载完成
        if (document.readyState === 'complete') {
            this.collectPerformanceData();
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.collectPerformanceData(), 100);
            });
        }

        // 监控核心Web指标
        this.monitorCoreWebVitals();
        
        // 监控资源加载
        this.monitorResourceLoading();
        
        // 监控内存使用
        this.monitorMemoryUsage();
        
        // 监控网络连接
        this.monitorNetworkConnection();
        
        // 监控长任务
        this.monitorLongTasks();
    }

    /**
     * 收集基础性能数据
     */
    collectPerformanceData() {
        if (!window.performance) return;

        const navigation = this.getNavigationTiming();
        const resources = this.getResourceTiming();
        
        this.performanceData.navigation = navigation;
        this.performanceData.resources = resources;

        // 发送性能数据
        this.sendPerformanceEvent('navigation', navigation);
        
        if (resources.length > 0) {
            this.sendPerformanceEvent('resources', {
                count: resources.length,
                totalSize: resources.reduce((sum, r) => sum + (r.transferSize || 0), 0),
                slowResources: resources.filter(r => r.duration > 1000)
            });
        }
    }

    /**
     * 获取导航时间数据
     */
    getNavigationTiming() {
        const timing = window.performance.timing;
        const navigation = window.performance.navigation;
        
        const navigationStart = timing.navigationStart;
        
        return {
            // 基础时间
            navigationStart: 0,
            unloadEventStart: timing.unloadEventStart - navigationStart,
            unloadEventEnd: timing.unloadEventEnd - navigationStart,
            redirectStart: timing.redirectStart - navigationStart,
            redirectEnd: timing.redirectEnd - navigationStart,
            fetchStart: timing.fetchStart - navigationStart,
            domainLookupStart: timing.domainLookupStart - navigationStart,
            domainLookupEnd: timing.domainLookupEnd - navigationStart,
            connectStart: timing.connectStart - navigationStart,
            connectEnd: timing.connectEnd - navigationStart,
            secureConnectionStart: timing.secureConnectionStart - navigationStart,
            requestStart: timing.requestStart - navigationStart,
            responseStart: timing.responseStart - navigationStart,
            responseEnd: timing.responseEnd - navigationStart,
            domLoading: timing.domLoading - navigationStart,
            domInteractive: timing.domInteractive - navigationStart,
            domContentLoadedEventStart: timing.domContentLoadedEventStart - navigationStart,
            domContentLoadedEventEnd: timing.domContentLoadedEventEnd - navigationStart,
            domComplete: timing.domComplete - navigationStart,
            loadEventStart: timing.loadEventStart - navigationStart,
            loadEventEnd: timing.loadEventEnd - navigationStart,
            
            // 计算的指标
            redirectTime: timing.redirectEnd - timing.redirectStart,
            dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
            tcpTime: timing.connectEnd - timing.connectStart,
            requestTime: timing.responseEnd - timing.requestStart,
            responseTime: timing.responseStart - timing.requestStart,
            domParseTime: timing.domComplete - timing.domLoading,
            domReadyTime: timing.domContentLoadedEventEnd - navigationStart,
            loadCompleteTime: timing.loadEventEnd - navigationStart,
            
            // 导航类型
            navigationType: navigation.type,
            redirectCount: navigation.redirectCount
        };
    }

    /**
     * 获取资源时间数据
     */
    getResourceTiming() {
        if (!window.performance.getEntriesByType) return [];

        const resources = window.performance.getEntriesByType('resource');
        
        return resources.map(resource => ({
            name: resource.name,
            type: this.getResourceType(resource),
            startTime: Math.round(resource.startTime),
            duration: Math.round(resource.duration),
            transferSize: resource.transferSize || 0,
            encodedBodySize: resource.encodedBodySize || 0,
            decodedBodySize: resource.decodedBodySize || 0,
            initiatorType: resource.initiatorType,
            nextHopProtocol: resource.nextHopProtocol,
            renderBlockingStatus: resource.renderBlockingStatus
        }));
    }

    /**
     * 获取资源类型
     */
    getResourceType(resource) {
        if (resource.initiatorType) {
            return resource.initiatorType;
        }
        
        const url = resource.name.toLowerCase();
        if (url.includes('.js')) return 'script';
        if (url.includes('.css')) return 'stylesheet';
        if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif') || url.includes('.webp')) return 'image';
        if (url.includes('.woff') || url.includes('.ttf')) return 'font';
        
        return 'other';
    }

    /**
     * 监控核心Web指标
     */
    monitorCoreWebVitals() {
        // First Contentful Paint (FCP)
        this.observePerformanceEntry('paint', (entries) => {
            entries.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                    this.performanceData.vitals.fcp = Math.round(entry.startTime);
                    this.sendPerformanceEvent('vital', {
                        name: 'FCP',
                        value: this.performanceData.vitals.fcp,
                        rating: this.rateVital('FCP', this.performanceData.vitals.fcp)
                    });
                }
            });
        });

        // Largest Contentful Paint (LCP)
        this.observePerformanceEntry('largest-contentful-paint', (entries) => {
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
                this.performanceData.vitals.lcp = Math.round(lastEntry.startTime);
                this.sendPerformanceEvent('vital', {
                    name: 'LCP',
                    value: this.performanceData.vitals.lcp,
                    rating: this.rateVital('LCP', this.performanceData.vitals.lcp)
                });
            }
        });

        // First Input Delay (FID)
        this.observePerformanceEntry('first-input', (entries) => {
            entries.forEach(entry => {
                this.performanceData.vitals.fid = Math.round(entry.processingStart - entry.startTime);
                this.sendPerformanceEvent('vital', {
                    name: 'FID',
                    value: this.performanceData.vitals.fid,
                    rating: this.rateVital('FID', this.performanceData.vitals.fid)
                });
            });
        });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        this.observePerformanceEntry('layout-shift', (entries) => {
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            
            this.performanceData.vitals.cls = Math.round(clsValue * 1000) / 1000;
        });

        // 页面卸载时发送CLS
        window.addEventListener('beforeunload', () => {
            if (this.performanceData.vitals.cls !== undefined) {
                this.sendPerformanceEvent('vital', {
                    name: 'CLS',
                    value: this.performanceData.vitals.cls,
                    rating: this.rateVital('CLS', this.performanceData.vitals.cls)
                }, true);
            }
        });
    }

    /**
     * 监控资源加载
     */
    monitorResourceLoading() {
        this.observePerformanceEntry('resource', (entries) => {
            entries.forEach(entry => {
                // 监控慢资源
                if (entry.duration > 1000) {
                    this.sendPerformanceEvent('slow_resource', {
                        name: entry.name,
                        type: this.getResourceType(entry),
                        duration: Math.round(entry.duration),
                        size: entry.transferSize || 0
                    });
                }
                
                // 监控大资源
                if (entry.transferSize > 1024 * 1024) { // 1MB
                    this.sendPerformanceEvent('large_resource', {
                        name: entry.name,
                        type: this.getResourceType(entry),
                        size: entry.transferSize
                    });
                }
            });
        });
    }

    /**
     * 监控内存使用
     */
    monitorMemoryUsage() {
        if (!window.performance.memory) return;

        const collectMemoryData = () => {
            const memory = window.performance.memory;
            this.performanceData.memory = {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                usageRatio: memory.usedJSHeapSize / memory.jsHeapSizeLimit
            };

            // 内存使用率过高时发送警告
            if (this.performanceData.memory.usageRatio > 0.9) {
                this.sendPerformanceEvent('high_memory_usage', this.performanceData.memory);
            }
        };

        // 定期收集内存数据
        setInterval(collectMemoryData, 30000); // 30秒
        collectMemoryData(); // 立即执行一次
    }

    /**
     * 监控网络连接
     */
    monitorNetworkConnection() {
        if (!navigator.connection) return;

        const connection = navigator.connection;
        
        const collectConnectionData = () => {
            this.performanceData.connection = {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData
            };
        };

        collectConnectionData();
        
        // 监听连接变化
        connection.addEventListener('change', () => {
            collectConnectionData();
            this.sendPerformanceEvent('connection_change', this.performanceData.connection);
        });
    }

    /**
     * 监控长任务
     */
    monitorLongTasks() {
        this.observePerformanceEntry('longtask', (entries) => {
            entries.forEach(entry => {
                this.sendPerformanceEvent('long_task', {
                    duration: Math.round(entry.duration),
                    startTime: Math.round(entry.startTime),
                    attribution: entry.attribution ? entry.attribution.map(attr => ({
                        name: attr.name,
                        entryType: attr.entryType,
                        startTime: Math.round(attr.startTime),
                        duration: Math.round(attr.duration)
                    })) : []
                });
            });
        });
    }

    /**
     * 观察性能条目
     */
    observePerformanceEntry(type, callback) {
        if (!window.PerformanceObserver) return;

        try {
            const observer = new PerformanceObserver((list) => {
                callback(list.getEntries());
            });
            
            observer.observe({ entryTypes: [type] });
            this.observers[type] = observer;
        } catch (error) {
            console.warn(`[WebStats] 无法观察性能条目类型: ${type}`, error);
        }
    }

    /**
     * 评估Web指标
     */
    rateVital(name, value) {
        const thresholds = {
            FCP: { good: 1800, poor: 3000 },
            LCP: { good: 2500, poor: 4000 },
            FID: { good: 100, poor: 300 },
            CLS: { good: 0.1, poor: 0.25 }
        };

        const threshold = thresholds[name];
        if (!threshold) return 'unknown';

        if (value <= threshold.good) return 'good';
        if (value <= threshold.poor) return 'needs-improvement';
        return 'poor';
    }

    /**
     * 发送性能事件
     */
    sendPerformanceEvent(type, data, sync = false) {
        const eventData = {
            site_id: this.config.siteId,
            visitor_id: this.tracker.visitorId,
            session_id: this.tracker.sessionId,
            page_url: window.location.href,
            event_type: 'performance',
            event_name: type,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.tracker.sendEvent('performance', eventData, sync);
    }

    /**
     * 获取性能摘要
     */
    getPerformanceSummary() {
        return {
            navigation: this.performanceData.navigation,
            vitals: this.performanceData.vitals,
            memory: this.performanceData.memory,
            connection: this.performanceData.connection,
            resourceCount: this.performanceData.resources.length,
            totalResourceSize: this.performanceData.resources.reduce((sum, r) => sum + (r.transferSize || 0), 0)
        };
    }

    /**
     * 清理观察器
     */
    cleanup() {
        Object.values(this.observers).forEach(observer => {
            observer.disconnect();
        });
        this.observers = {};
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
} else if (typeof window !== 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
}
