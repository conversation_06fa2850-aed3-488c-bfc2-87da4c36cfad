/**
 * 用户行为跟踪模块
 * 跟踪用户在页面上的各种交互行为
 */

class BehaviorTracker {
    constructor(tracker) {
        this.tracker = tracker;
        this.config = tracker.config;
        
        // 行为数据
        this.behaviors = {
            clicks: [],
            scrolls: [],
            hovers: [],
            forms: [],
            downloads: [],
            outboundLinks: []
        };

        // 节流和防抖定时器
        this.timers = {};
        
        // 初始化
        this.init();
    }

    /**
     * 初始化行为跟踪
     */
    init() {
        this.bindClickTracking();
        this.bindScrollTracking();
        this.bindHoverTracking();
        this.bindFormTracking();
        this.bindDownloadTracking();
        this.bindOutboundLinkTracking();
        this.bindKeyboardTracking();
        this.bindTouchTracking();
    }

    /**
     * 点击跟踪
     */
    bindClickTracking() {
        document.addEventListener('click', (e) => {
            const element = e.target;
            const clickData = this.getElementData(element);
            
            const behaviorData = {
                type: 'click',
                element: clickData,
                coordinates: {
                    x: e.clientX,
                    y: e.clientY,
                    pageX: e.pageX,
                    pageY: e.pageY
                },
                timestamp: Date.now(),
                modifiers: {
                    ctrl: e.ctrlKey,
                    alt: e.altKey,
                    shift: e.shiftKey,
                    meta: e.metaKey
                }
            };

            this.behaviors.clicks.push(behaviorData);
            this.sendBehaviorEvent('click', behaviorData);

            // 特殊元素处理
            this.handleSpecialClicks(element, e);
        }, true);
    }

    /**
     * 滚动跟踪
     */
    bindScrollTracking() {
        let scrollTimer;
        let lastScrollTop = 0;
        let scrollDirection = 'down';

        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up';
            lastScrollTop = scrollTop;

            scrollTimer = setTimeout(() => {
                const scrollData = this.getScrollData(scrollDirection);
                this.behaviors.scrolls.push(scrollData);
                
                // 只在重要的滚动点发送事件
                if (this.isImportantScrollPoint(scrollData)) {
                    this.sendBehaviorEvent('scroll', scrollData);
                }
            }, 150);
        });
    }

    /**
     * 悬停跟踪
     */
    bindHoverTracking() {
        let hoverTimer;
        
        document.addEventListener('mouseover', (e) => {
            const element = e.target;
            
            // 只跟踪重要元素的悬停
            if (this.isImportantElement(element)) {
                hoverTimer = setTimeout(() => {
                    const hoverData = {
                        type: 'hover',
                        element: this.getElementData(element),
                        duration: 1000, // 悬停1秒后触发
                        timestamp: Date.now()
                    };
                    
                    this.behaviors.hovers.push(hoverData);
                    this.sendBehaviorEvent('hover', hoverData);
                }, 1000);
            }
        });

        document.addEventListener('mouseout', () => {
            clearTimeout(hoverTimer);
        });
    }

    /**
     * 表单跟踪
     */
    bindFormTracking() {
        // 表单提交
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const formData = this.getFormData(form);
            
            this.behaviors.forms.push(formData);
            this.sendBehaviorEvent('form_submit', formData);
        });

        // 表单字段焦点
        document.addEventListener('focus', (e) => {
            if (this.isFormField(e.target)) {
                const fieldData = {
                    type: 'form_focus',
                    element: this.getElementData(e.target),
                    timestamp: Date.now()
                };
                
                this.sendBehaviorEvent('form_focus', fieldData);
            }
        }, true);

        // 表单字段失焦
        document.addEventListener('blur', (e) => {
            if (this.isFormField(e.target)) {
                const fieldData = {
                    type: 'form_blur',
                    element: this.getElementData(e.target),
                    timestamp: Date.now()
                };
                
                this.sendBehaviorEvent('form_blur', fieldData);
            }
        }, true);
    }

    /**
     * 下载跟踪
     */
    bindDownloadTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isDownloadLink(link)) {
                const downloadData = {
                    type: 'download',
                    url: link.href,
                    filename: this.getFilenameFromUrl(link.href),
                    fileType: this.getFileTypeFromUrl(link.href),
                    element: this.getElementData(link),
                    timestamp: Date.now()
                };
                
                this.behaviors.downloads.push(downloadData);
                this.sendBehaviorEvent('download', downloadData);
            }
        });
    }

    /**
     * 外链跟踪
     */
    bindOutboundLinkTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isOutboundLink(link)) {
                const outboundData = {
                    type: 'outbound_link',
                    url: link.href,
                    domain: this.getDomainFromUrl(link.href),
                    element: this.getElementData(link),
                    timestamp: Date.now()
                };
                
                this.behaviors.outboundLinks.push(outboundData);
                this.sendBehaviorEvent('outbound_link', outboundData);
            }
        });
    }

    /**
     * 键盘跟踪
     */
    bindKeyboardTracking() {
        let keySequence = [];
        
        document.addEventListener('keydown', (e) => {
            // 记录按键序列（用于检测快捷键等）
            keySequence.push({
                key: e.key,
                code: e.code,
                timestamp: Date.now()
            });
            
            // 只保留最近10个按键
            if (keySequence.length > 10) {
                keySequence.shift();
            }
            
            // 检测特殊按键组合
            this.detectKeyboardShortcuts(e, keySequence);
        });
    }

    /**
     * 触摸跟踪（移动端）
     */
    bindTouchTracking() {
        if (!('ontouchstart' in window)) return;

        let touchStartTime;
        let touchStartPos;

        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartPos = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        });

        document.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const duration = touchEndTime - touchStartTime;
            
            const touchData = {
                type: 'touch',
                duration: duration,
                startPos: touchStartPos,
                element: this.getElementData(e.target),
                timestamp: touchEndTime
            };
            
            // 长按检测
            if (duration > 500) {
                touchData.type = 'long_press';
                this.sendBehaviorEvent('long_press', touchData);
            }
        });

        // 滑动检测
        document.addEventListener('touchmove', this.throttle((e) => {
            // 滑动逻辑
        }, 100));
    }

    /**
     * 获取元素数据
     */
    getElementData(element) {
        return {
            tag: element.tagName.toLowerCase(),
            id: element.id,
            className: element.className,
            text: this.getElementText(element),
            attributes: this.getImportantAttributes(element),
            selector: this.getElementSelector(element),
            position: this.getElementPosition(element)
        };
    }

    /**
     * 获取元素文本
     */
    getElementText(element) {
        let text = '';
        
        if (element.tagName === 'INPUT') {
            text = element.value || element.placeholder || '';
        } else if (element.tagName === 'IMG') {
            text = element.alt || element.title || '';
        } else {
            text = element.textContent || element.innerText || '';
        }
        
        return text.trim().substring(0, 100);
    }

    /**
     * 获取重要属性
     */
    getImportantAttributes(element) {
        const importantAttrs = ['href', 'src', 'type', 'name', 'value', 'title', 'alt'];
        const attrs = {};
        
        importantAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
                attrs[attr] = element.getAttribute(attr);
            }
        });
        
        return attrs;
    }

    /**
     * 获取元素选择器
     */
    getElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        let selector = element.tagName.toLowerCase();
        
        if (element.className) {
            selector += '.' + element.className.split(' ').join('.');
        }
        
        return selector;
    }

    /**
     * 获取元素位置
     */
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };
    }

    /**
     * 获取滚动数据
     */
    getScrollData(direction) {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const scrollPercent = Math.round((scrollTop + windowHeight) / documentHeight * 100);
        
        return {
            type: 'scroll',
            scrollTop: scrollTop,
            scrollPercent: Math.min(scrollPercent, 100),
            direction: direction,
            timestamp: Date.now()
        };
    }

    /**
     * 获取表单数据
     */
    getFormData(form) {
        const formData = new FormData(form);
        const fields = {};
        
        for (let [key, value] of formData.entries()) {
            // 不记录敏感信息
            if (this.isSensitiveField(key)) {
                fields[key] = '[HIDDEN]';
            } else {
                fields[key] = value.toString().substring(0, 100);
            }
        }
        
        return {
            type: 'form_submit',
            action: form.action,
            method: form.method,
            fields: fields,
            element: this.getElementData(form),
            timestamp: Date.now()
        };
    }

    /**
     * 检查是否为重要元素
     */
    isImportantElement(element) {
        const importantTags = ['a', 'button', 'input', 'select', 'textarea'];
        const importantClasses = ['btn', 'button', 'link', 'nav', 'menu'];
        
        if (importantTags.includes(element.tagName.toLowerCase())) {
            return true;
        }
        
        return importantClasses.some(cls => 
            element.className.toLowerCase().includes(cls)
        );
    }

    /**
     * 检查是否为重要滚动点
     */
    isImportantScrollPoint(scrollData) {
        const importantPoints = [25, 50, 75, 90, 100];
        return importantPoints.includes(scrollData.scrollPercent);
    }

    /**
     * 检查是否为表单字段
     */
    isFormField(element) {
        const formTags = ['input', 'select', 'textarea'];
        return formTags.includes(element.tagName.toLowerCase());
    }

    /**
     * 检查是否为敏感字段
     */
    isSensitiveField(fieldName) {
        const sensitiveFields = ['password', 'pass', 'pwd', 'secret', 'token', 'ssn', 'credit', 'card'];
        return sensitiveFields.some(field => 
            fieldName.toLowerCase().includes(field)
        );
    }

    /**
     * 检查是否为下载链接
     */
    isDownloadLink(link) {
        const downloadExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
        return downloadExtensions.some(ext => 
            link.href.toLowerCase().includes(ext)
        );
    }

    /**
     * 检查是否为外链
     */
    isOutboundLink(link) {
        const currentDomain = window.location.hostname;
        const linkDomain = this.getDomainFromUrl(link.href);
        return linkDomain && linkDomain !== currentDomain;
    }

    /**
     * 从URL获取域名
     */
    getDomainFromUrl(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return null;
        }
    }

    /**
     * 从URL获取文件名
     */
    getFilenameFromUrl(url) {
        return url.split('/').pop().split('?')[0];
    }

    /**
     * 从URL获取文件类型
     */
    getFileTypeFromUrl(url) {
        const filename = this.getFilenameFromUrl(url);
        const extension = filename.split('.').pop();
        return extension ? extension.toLowerCase() : 'unknown';
    }

    /**
     * 处理特殊点击
     */
    handleSpecialClicks(element, event) {
        // 处理按钮点击
        if (element.tagName === 'BUTTON' || element.type === 'button') {
            this.sendBehaviorEvent('button_click', {
                buttonText: this.getElementText(element),
                buttonType: element.type,
                element: this.getElementData(element)
            });
        }
        
        // 处理链接点击
        if (element.tagName === 'A') {
            this.sendBehaviorEvent('link_click', {
                url: element.href,
                linkText: this.getElementText(element),
                element: this.getElementData(element)
            });
        }
    }

    /**
     * 检测键盘快捷键
     */
    detectKeyboardShortcuts(event, keySequence) {
        // 检测常见快捷键
        if (event.ctrlKey || event.metaKey) {
            const shortcuts = {
                'KeyC': 'copy',
                'KeyV': 'paste',
                'KeyX': 'cut',
                'KeyZ': 'undo',
                'KeyY': 'redo',
                'KeyS': 'save',
                'KeyF': 'find'
            };
            
            if (shortcuts[event.code]) {
                this.sendBehaviorEvent('keyboard_shortcut', {
                    shortcut: shortcuts[event.code],
                    key: event.key,
                    timestamp: Date.now()
                });
            }
        }
    }

    /**
     * 发送行为事件
     */
    sendBehaviorEvent(type, data) {
        if (!this.config.trackUserBehavior) return;
        
        const eventData = {
            site_id: this.config.siteId,
            visitor_id: this.tracker.visitorId,
            session_id: this.tracker.sessionId,
            page_url: window.location.href,
            event_type: 'behavior',
            event_name: type,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };
        
        this.tracker.sendEvent('behavior', eventData);
    }

    /**
     * 节流函数
     */
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        
        return function(...args) {
            const currentTime = Date.now();
            
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }

    /**
     * 获取行为统计
     */
    getBehaviorStats() {
        return {
            clicks: this.behaviors.clicks.length,
            scrolls: this.behaviors.scrolls.length,
            hovers: this.behaviors.hovers.length,
            forms: this.behaviors.forms.length,
            downloads: this.behaviors.downloads.length,
            outboundLinks: this.behaviors.outboundLinks.length,
            maxScrollDepth: Math.max(...this.behaviors.scrolls.map(s => s.scrollPercent), 0)
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BehaviorTracker;
} else if (typeof window !== 'undefined') {
    window.BehaviorTracker = BehaviorTracker;
}
