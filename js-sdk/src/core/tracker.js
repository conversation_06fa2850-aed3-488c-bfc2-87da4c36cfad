/**
 * 网站统计系统 JavaScript SDK
 * 核心跟踪器类
 */

class WebStatsTracker {
    constructor(config = {}) {
        this.config = {
            apiUrl: config.apiUrl || 'http://localhost:9002/api/v1',
            siteId: config.siteId || '',
            debug: config.debug || false,
            autoTrack: config.autoTrack !== false, // 默认开启自动跟踪
            trackPageView: config.trackPageView !== false,
            trackUserBehavior: config.trackUserBehavior !== false,
            trackPerformance: config.trackPerformance !== false,
            sessionTimeout: config.sessionTimeout || 30 * 60 * 1000, // 30分钟
            heartbeatInterval: config.heartbeatInterval || 15 * 1000, // 15秒
            batchSize: config.batchSize || 10,
            flushInterval: config.flushInterval || 5 * 1000, // 5秒
            ...config
        };

        this.visitorId = this.getOrCreateVisitorId();
        this.sessionId = this.getOrCreateSessionId();
        this.pageStartTime = Date.now();
        this.lastActivityTime = Date.now();
        this.eventQueue = [];
        this.isPageVisible = true;
        this.scrollDepth = 0;
        this.maxScrollDepth = 0;
        this.clickCount = 0;
        this.pageViewSent = false;

        this.init();
    }

    /**
     * 初始化跟踪器
     */
    init() {
        if (!this.config.siteId) {
            this.log('错误: 缺少 siteId 配置');
            return;
        }

        this.log('初始化 WebStats 跟踪器', {
            siteId: this.config.siteId,
            visitorId: this.visitorId,
            sessionId: this.sessionId
        });

        // 绑定事件监听器
        this.bindEvents();

        // 自动跟踪页面浏览
        if (this.config.autoTrack && this.config.trackPageView) {
            this.trackPageView();
        }

        // 启动心跳检测
        this.startHeartbeat();

        // 启动批量发送
        this.startBatchSender();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.isPageVisible = !document.hidden;
            if (this.isPageVisible) {
                this.updateActivity();
            } else {
                this.sendQueuedEvents();
            }
        });

        // 页面卸载
        window.addEventListener('beforeunload', () => {
            this.trackPageLeave();
            this.sendQueuedEvents(true); // 同步发送
        });

        // 页面隐藏（移动端）
        window.addEventListener('pagehide', () => {
            this.trackPageLeave();
            this.sendQueuedEvents(true);
        });

        if (this.config.trackUserBehavior) {
            this.bindUserBehaviorEvents();
        }

        if (this.config.trackPerformance) {
            this.bindPerformanceEvents();
        }
    }

    /**
     * 绑定用户行为事件
     */
    bindUserBehaviorEvents() {
        // 点击事件
        document.addEventListener('click', (e) => {
            this.clickCount++;
            this.updateActivity();
            this.trackClick(e);
        });

        // 滚动事件
        let scrollTimer;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(() => {
                this.updateScrollDepth();
                this.updateActivity();
            }, 100);
        });

        // 鼠标移动（节流）
        let mouseMoveTimer;
        document.addEventListener('mousemove', () => {
            clearTimeout(mouseMoveTimer);
            mouseMoveTimer = setTimeout(() => {
                this.updateActivity();
            }, 1000);
        });

        // 键盘事件
        document.addEventListener('keydown', () => {
            this.updateActivity();
        });
    }

    /**
     * 绑定性能监控事件
     */
    bindPerformanceEvents() {
        // 页面加载完成后收集性能数据
        if (document.readyState === 'complete') {
            setTimeout(() => this.collectPerformanceData(), 100);
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.collectPerformanceData(), 100);
            });
        }
    }

    /**
     * 获取或创建访客ID
     */
    getOrCreateVisitorId() {
        const key = '_ws_visitor_id';
        let visitorId = this.getCookie(key) || localStorage.getItem(key);
        
        if (!visitorId) {
            visitorId = this.generateId();
            // 设置1年过期的cookie
            this.setCookie(key, visitorId, 365 * 24 * 60 * 60 * 1000);
            localStorage.setItem(key, visitorId);
        }
        
        return visitorId;
    }

    /**
     * 获取或创建会话ID
     */
    getOrCreateSessionId() {
        const key = '_ws_session_id';
        const timeKey = '_ws_session_time';
        
        let sessionId = sessionStorage.getItem(key);
        let sessionTime = sessionStorage.getItem(timeKey);
        
        const now = Date.now();
        
        // 检查会话是否过期
        if (!sessionId || !sessionTime || (now - parseInt(sessionTime)) > this.config.sessionTimeout) {
            sessionId = this.generateId();
            sessionStorage.setItem(key, sessionId);
        }
        
        sessionStorage.setItem(timeKey, now.toString());
        return sessionId;
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'ws_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }

    /**
     * 更新活动时间
     */
    updateActivity() {
        this.lastActivityTime = Date.now();
        // 更新会话时间
        sessionStorage.setItem('_ws_session_time', this.lastActivityTime.toString());
    }

    /**
     * 更新滚动深度
     */
    updateScrollDepth() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        
        const scrollPercent = Math.round((scrollTop + windowHeight) / documentHeight * 100);
        this.scrollDepth = Math.min(scrollPercent, 100);
        this.maxScrollDepth = Math.max(this.maxScrollDepth, this.scrollDepth);
    }

    /**
     * 跟踪页面浏览
     */
    trackPageView() {
        if (this.pageViewSent) return;
        
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            page_title: document.title,
            referrer: document.referrer,
            user_agent: navigator.userAgent,
            screen_width: screen.width,
            screen_height: screen.height,
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight,
            color_depth: screen.colorDepth,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            is_new_visitor: !this.getCookie('_ws_visitor_id'),
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('track', data);
        this.pageViewSent = true;
        this.log('页面浏览事件已发送', data);
    }

    /**
     * 跟踪页面离开
     */
    trackPageLeave() {
        const stayDuration = Math.floor((Date.now() - this.pageStartTime) / 1000);
        
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            stay_duration: stayDuration,
            scroll_depth: this.maxScrollDepth,
            click_count: this.clickCount,
            is_bounce: this.clickCount === 0 && stayDuration < 10,
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('page_leave', data, true); // 同步发送
        this.log('页面离开事件已发送', data);
    }

    /**
     * 跟踪点击事件
     */
    trackClick(event) {
        const element = event.target;
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            element_tag: element.tagName.toLowerCase(),
            element_id: element.id,
            element_class: element.className,
            element_text: element.textContent ? element.textContent.substring(0, 100) : '',
            click_x: event.clientX,
            click_y: event.clientY,
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('click', data);
    }

    /**
     * 收集性能数据
     */
    collectPerformanceData() {
        if (!window.performance || !window.performance.timing) {
            return;
        }

        const timing = window.performance.timing;
        const navigation = window.performance.navigation;

        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            load_time: timing.loadEventEnd - timing.navigationStart,
            dom_ready_time: timing.domContentLoadedEventEnd - timing.navigationStart,
            first_paint_time: timing.responseStart - timing.navigationStart,
            dns_time: timing.domainLookupEnd - timing.domainLookupStart,
            tcp_time: timing.connectEnd - timing.connectStart,
            request_time: timing.responseEnd - timing.requestStart,
            navigation_type: navigation.type,
            redirect_count: navigation.redirectCount,
            timestamp: Math.floor(Date.now() / 1000)
        };

        this.sendEvent('performance', data);
        this.log('性能数据已收集', data);
    }

    /**
     * 发送事件
     */
    sendEvent(type, data, sync = false) {
        const event = {
            type: type,
            data: data,
            timestamp: Date.now()
        };

        if (sync) {
            this.sendEventSync(event);
        } else {
            this.eventQueue.push(event);
            
            // 如果队列满了，立即发送
            if (this.eventQueue.length >= this.config.batchSize) {
                this.sendQueuedEvents();
            }
        }
    }

    /**
     * 同步发送事件
     */
    sendEventSync(event) {
        try {
            const url = `${this.config.apiUrl}/track`;
            
            if (navigator.sendBeacon) {
                // 使用 sendBeacon API（推荐）
                const blob = new Blob([JSON.stringify(event.data)], {
                    type: 'application/json'
                });
                navigator.sendBeacon(url, blob);
            } else {
                // 降级到同步 XMLHttpRequest
                const xhr = new XMLHttpRequest();
                xhr.open('POST', url, false);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(event.data));
            }
        } catch (error) {
            this.log('同步发送事件失败', error);
        }
    }

    /**
     * 发送队列中的事件
     */
    sendQueuedEvents(sync = false) {
        if (this.eventQueue.length === 0) return;

        const events = [...this.eventQueue];
        this.eventQueue = [];

        events.forEach(event => {
            if (sync) {
                this.sendEventSync(event);
            } else {
                this.sendEventAsync(event);
            }
        });
    }

    /**
     * 异步发送事件
     */
    sendEventAsync(event) {
        const url = `${this.config.apiUrl}/track`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(event.data),
            keepalive: true
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            this.log('事件发送成功', data);
        })
        .catch(error => {
            this.log('事件发送失败', error);
            // 重新加入队列重试
            this.eventQueue.unshift(event);
        });
    }

    /**
     * 启动心跳检测
     */
    startHeartbeat() {
        setInterval(() => {
            if (this.isPageVisible && (Date.now() - this.lastActivityTime) < this.config.sessionTimeout) {
                // 发送心跳事件
                this.sendEvent('heartbeat', {
                    site_id: this.config.siteId,
                    visitor_id: this.visitorId,
                    session_id: this.sessionId,
                    page_url: window.location.href,
                    timestamp: Math.floor(Date.now() / 1000)
                });
            }
        }, this.config.heartbeatInterval);
    }

    /**
     * 启动批量发送器
     */
    startBatchSender() {
        setInterval(() => {
            this.sendQueuedEvents();
        }, this.config.flushInterval);
    }

    /**
     * Cookie 操作
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    setCookie(name, value, maxAge) {
        document.cookie = `${name}=${value}; max-age=${maxAge}; path=/; SameSite=Lax`;
    }

    /**
     * 日志输出
     */
    log(message, data = null) {
        if (this.config.debug) {
            console.log(`[WebStats] ${message}`, data);
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebStatsTracker;
} else if (typeof window !== 'undefined') {
    window.WebStatsTracker = WebStatsTracker;
}
