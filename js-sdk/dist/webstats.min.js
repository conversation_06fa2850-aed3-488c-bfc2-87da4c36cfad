(function(window, document) {
    'use strict';
class WebStatsTracker {
    constructor(config = {}) {
        this.config = {
            apiUrl: config.apiUrl || 'http://localhost:9002/api/v1',
            siteId: config.siteId || '',
            debug: config.debug || false,
            autoTrack: config.autoTrack !== false, // 默认开启自动跟踪
            trackPageView: config.trackPageView !== false,
            trackUserBehavior: config.trackUserBehavior !== false,
            trackPerformance: config.trackPerformance !== false,
            sessionTimeout: config.sessionTimeout || 30 * 60 * 1000, // 30分钟
            heartbeatInterval: config.heartbeatInterval || 15 * 1000, // 15秒
            batchSize: config.batchSize || 10,
            flushInterval: config.flushInterval || 5 * 1000, // 5秒
            ...config
        };
        this.visitorId = this.getOrCreateVisitorId();
        this.sessionId = this.getOrCreateSessionId();
        this.pageStartTime = Date.now();
        this.lastActivityTime = Date.now();
        this.eventQueue = [];
        this.isPageVisible = true;
        this.scrollDepth = 0;
        this.maxScrollDepth = 0;
        this.clickCount = 0;
        this.pageViewSent = false;
        this.init();
    }
    init() {
        if (!this.config.siteId) {
            this.log('错误: 缺少 siteId 配置');
            return;
        }
        this.log('初始化 WebStats 跟踪器', {
            siteId: this.config.siteId,
            visitorId: this.visitorId,
            sessionId: this.sessionId
        });
        this.bindEvents();
        if (this.config.autoTrack && this.config.trackPageView) {
            this.trackPageView();
        }
        this.startHeartbeat();
        this.startBatchSender();
    }
    bindEvents() {
        document.addEventListener('visibilitychange', () => {
            this.isPageVisible = !document.hidden;
            if (this.isPageVisible) {
                this.updateActivity();
            } else {
                this.sendQueuedEvents();
            }
        });
        window.addEventListener('beforeunload', () => {
            this.trackPageLeave();
            this.sendQueuedEvents(true); // 同步发送
        });
        window.addEventListener('pagehide', () => {
            this.trackPageLeave();
            this.sendQueuedEvents(true);
        });
        if (this.config.trackUserBehavior) {
            this.bindUserBehaviorEvents();
        }
        if (this.config.trackPerformance) {
            this.bindPerformanceEvents();
        }
    }
    bindUserBehaviorEvents() {
        document.addEventListener('click', (e) => {
            this.clickCount++;
            this.updateActivity();
            this.trackClick(e);
        });
        let scrollTimer;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(() => {
                this.updateScrollDepth();
                this.updateActivity();
            }, 100);
        });
        let mouseMoveTimer;
        document.addEventListener('mousemove', () => {
            clearTimeout(mouseMoveTimer);
            mouseMoveTimer = setTimeout(() => {
                this.updateActivity();
            }, 1000);
        });
        document.addEventListener('keydown', () => {
            this.updateActivity();
        });
    }
    bindPerformanceEvents() {
        if (document.readyState === 'complete') {
            setTimeout(() => this.collectPerformanceData(), 100);
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.collectPerformanceData(), 100);
            });
        }
    }
    getOrCreateVisitorId() {
        const key = '_ws_visitor_id';
        let visitorId = this.getCookie(key) || localStorage.getItem(key);
        if (!visitorId) {
            visitorId = this.generateId();
            this.setCookie(key, visitorId, 365 * 24 * 60 * 60 * 1000);
            localStorage.setItem(key, visitorId);
        }
        return visitorId;
    }
    getOrCreateSessionId() {
        const key = '_ws_session_id';
        const timeKey = '_ws_session_time';
        let sessionId = sessionStorage.getItem(key);
        let sessionTime = sessionStorage.getItem(timeKey);
        const now = Date.now();
        if (!sessionId || !sessionTime || (now - parseInt(sessionTime)) > this.config.sessionTimeout) {
            sessionId = this.generateId();
            sessionStorage.setItem(key, sessionId);
        }
        sessionStorage.setItem(timeKey, now.toString());
        return sessionId;
    }
    generateId() {
        return 'ws_'  Math.random().toString(36).substr(2, 9)  '_'  Date.now().toString(36);
    }
    updateActivity() {
        this.lastActivityTime = Date.now();
        sessionStorage.setItem('_ws_session_time', this.lastActivityTime.toString());
    }
    updateScrollDepth() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const scrollPercent = Math.round((scrollTop  windowHeight) / documentHeight * 100);
        this.scrollDepth = Math.min(scrollPercent, 100);
        this.maxScrollDepth = Math.max(this.maxScrollDepth, this.scrollDepth);
    }
    trackPageView() {
        if (this.pageViewSent) return;
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            page_title: document.title,
            referrer: document.referrer,
            user_agent: navigator.userAgent,
            screen_width: screen.width,
            screen_height: screen.height,
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight,
            color_depth: screen.colorDepth,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            is_new_visitor: !this.getCookie('_ws_visitor_id'),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('track', data);
        this.pageViewSent = true;
        this.log('页面浏览事件已发送', data);
    }
    trackPageLeave() {
        const stayDuration = Math.floor((Date.now() - this.pageStartTime) / 1000);
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            stay_duration: stayDuration,
            scroll_depth: this.maxScrollDepth,
            click_count: this.clickCount,
            is_bounce: this.clickCount === 0 && stayDuration < 10,
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('page_leave', data, true); // 同步发送
        this.log('页面离开事件已发送', data);
    }
    trackClick(event) {
        const element = event.target;
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            element_tag: element.tagName.toLowerCase(),
            element_id: element.id,
            element_class: element.className,
            element_text: element.textContent ? element.textContent.substring(0, 100) : '',
            click_x: event.clientX,
            click_y: event.clientY,
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('click', data);
    }
    collectPerformanceData() {
        if (!window.performance || !window.performance.timing) {
            return;
        }
        const timing = window.performance.timing;
        const navigation = window.performance.navigation;
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            load_time: timing.loadEventEnd - timing.navigationStart,
            dom_ready_time: timing.domContentLoadedEventEnd - timing.navigationStart,
            first_paint_time: timing.responseStart - timing.navigationStart,
            dns_time: timing.domainLookupEnd - timing.domainLookupStart,
            tcp_time: timing.connectEnd - timing.connectStart,
            request_time: timing.responseEnd - timing.requestStart,
            navigation_type: navigation.type,
            redirect_count: navigation.redirectCount,
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('performance', data);
        this.log('性能数据已收集', data);
    }
    sendEvent(type, data, sync = false) {
        const event = {
            type: type,
            data: data,
            timestamp: Date.now()
        };
        if (sync) {
            this.sendEventSync(event);
        } else {
            this.eventQueue.push(event);
            if (this.eventQueue.length >= this.config.batchSize) {
                this.sendQueuedEvents();
            }
        }
    }
    sendEventSync(event) {
        try {
            const url = `${this.config.apiUrl}/track`;
            if (navigator.sendBeacon) {
                const blob = new Blob([JSON.stringify(event.data)], {
                    type: 'application/json'
                });
                navigator.sendBeacon(url, blob);
            } else {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', url, false);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(event.data));
            }
        } catch (error) {
            this.log('同步发送事件失败', error);
        }
    }
    sendQueuedEvents(sync = false) {
        if (this.eventQueue.length === 0) return;
        const events = [...this.eventQueue];
        this.eventQueue = [];
        events.forEach(event => {
            if (sync) {
                this.sendEventSync(event);
            } else {
                this.sendEventAsync(event);
            }
        });
    }
    sendEventAsync(event) {
        const url = `${this.config.apiUrl}/track`;
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(event.data),
            keepalive: true
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            this.log('事件发送成功', data);
        })
        .catch(error => {
            this.log('事件发送失败', error);
            this.eventQueue.unshift(event);
        });
    }
    startHeartbeat() {
        setInterval(() => {
            if (this.isPageVisible && (Date.now() - this.lastActivityTime) < this.config.sessionTimeout) {
                this.sendEvent('heartbeat', {
                    site_id: this.config.siteId,
                    visitor_id: this.visitorId,
                    session_id: this.sessionId,
                    page_url: window.location.href,
                    timestamp: Math.floor(Date.now() / 1000)
                });
            }
        }, this.config.heartbeatInterval);
    }
    startBatchSender() {
        setInterval(() => {
            this.sendQueuedEvents();
        }, this.config.flushInterval);
    }
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }
    setCookie(name, value, maxAge) {
        document.cookie = `${name}=${value}; max-age=${maxAge}; path=/; SameSite=Lax`;
    }
    log(message, data = null) {
        if (this.config.debug) {
            console.log(`[WebStats] ${message}`, data);
        }
    }
}
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebStatsTracker;
} else if (typeof window !== 'undefined') {
    window.WebStatsTracker = WebStatsTracker;
}
class BehaviorTracker {
    constructor(tracker) {
        this.tracker = tracker;
        this.config = tracker.config;
        this.behaviors = {
            clicks: [],
            scrolls: [],
            hovers: [],
            forms: [],
            downloads: [],
            outboundLinks: []
        };
        this.timers = {};
        this.init();
    }
    init() {
        this.bindClickTracking();
        this.bindScrollTracking();
        this.bindHoverTracking();
        this.bindFormTracking();
        this.bindDownloadTracking();
        this.bindOutboundLinkTracking();
        this.bindKeyboardTracking();
        this.bindTouchTracking();
    }
    bindClickTracking() {
        document.addEventListener('click', (e) => {
            const element = e.target;
            const clickData = this.getElementData(element);
            const behaviorData = {
                type: 'click',
                element: clickData,
                coordinates: {
                    x: e.clientX,
                    y: e.clientY,
                    pageX: e.pageX,
                    pageY: e.pageY
                },
                timestamp: Date.now(),
                modifiers: {
                    ctrl: e.ctrlKey,
                    alt: e.altKey,
                    shift: e.shiftKey,
                    meta: e.metaKey
                }
            };
            this.behaviors.clicks.push(behaviorData);
            this.sendBehaviorEvent('click', behaviorData);
            this.handleSpecialClicks(element, e);
        }, true);
    }
    bindScrollTracking() {
        let scrollTimer;
        let lastScrollTop = 0;
        let scrollDirection = 'down';
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up';
            lastScrollTop = scrollTop;
            scrollTimer = setTimeout(() => {
                const scrollData = this.getScrollData(scrollDirection);
                this.behaviors.scrolls.push(scrollData);
                if (this.isImportantScrollPoint(scrollData)) {
                    this.sendBehaviorEvent('scroll', scrollData);
                }
            }, 150);
        });
    }
    bindHoverTracking() {
        let hoverTimer;
        document.addEventListener('mouseover', (e) => {
            const element = e.target;
            if (this.isImportantElement(element)) {
                hoverTimer = setTimeout(() => {
                    const hoverData = {
                        type: 'hover',
                        element: this.getElementData(element),
                        duration: 1000, // 悬停1秒后触发
                        timestamp: Date.now()
                    };
                    this.behaviors.hovers.push(hoverData);
                    this.sendBehaviorEvent('hover', hoverData);
                }, 1000);
            }
        });
        document.addEventListener('mouseout', () => {
            clearTimeout(hoverTimer);
        });
    }
    bindFormTracking() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const formData = this.getFormData(form);
            this.behaviors.forms.push(formData);
            this.sendBehaviorEvent('form_submit', formData);
        });
        document.addEventListener('focus', (e) => {
            if (this.isFormField(e.target)) {
                const fieldData = {
                    type: 'form_focus',
                    element: this.getElementData(e.target),
                    timestamp: Date.now()
                };
                this.sendBehaviorEvent('form_focus', fieldData);
            }
        }, true);
        document.addEventListener('blur', (e) => {
            if (this.isFormField(e.target)) {
                const fieldData = {
                    type: 'form_blur',
                    element: this.getElementData(e.target),
                    timestamp: Date.now()
                };
                this.sendBehaviorEvent('form_blur', fieldData);
            }
        }, true);
    }
    bindDownloadTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isDownloadLink(link)) {
                const downloadData = {
                    type: 'download',
                    url: link.href,
                    filename: this.getFilenameFromUrl(link.href),
                    fileType: this.getFileTypeFromUrl(link.href),
                    element: this.getElementData(link),
                    timestamp: Date.now()
                };
                this.behaviors.downloads.push(downloadData);
                this.sendBehaviorEvent('download', downloadData);
            }
        });
    }
    bindOutboundLinkTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isOutboundLink(link)) {
                const outboundData = {
                    type: 'outbound_link',
                    url: link.href,
                    domain: this.getDomainFromUrl(link.href),
                    element: this.getElementData(link),
                    timestamp: Date.now()
                };
                this.behaviors.outboundLinks.push(outboundData);
                this.sendBehaviorEvent('outbound_link', outboundData);
            }
        });
    }
    bindKeyboardTracking() {
        let keySequence = [];
        document.addEventListener('keydown', (e) => {
            keySequence.push({
                key: e.key,
                code: e.code,
                timestamp: Date.now()
            });
            if (keySequence.length > 10) {
                keySequence.shift();
            }
            this.detectKeyboardShortcuts(e, keySequence);
        });
    }
    bindTouchTracking() {
        if (!('ontouchstart' in window)) return;
        let touchStartTime;
        let touchStartPos;
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartPos = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        });
        document.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const duration = touchEndTime - touchStartTime;
            const touchData = {
                type: 'touch',
                duration: duration,
                startPos: touchStartPos,
                element: this.getElementData(e.target),
                timestamp: touchEndTime
            };
            if (duration > 500) {
                touchData.type = 'long_press';
                this.sendBehaviorEvent('long_press', touchData);
            }
        });
        document.addEventListener('touchmove', this.throttle((e) => {
        }, 100));
    }
    getElementData(element) {
        return {
            tag: element.tagName.toLowerCase(),
            id: element.id,
            className: element.className,
            text: this.getElementText(element),
            attributes: this.getImportantAttributes(element),
            selector: this.getElementSelector(element),
            position: this.getElementPosition(element)
        };
    }
    getElementText(element) {
        let text = '';
        if (element.tagName === 'INPUT') {
            text = element.value || element.placeholder || '';
        } else if (element.tagName === 'IMG') {
            text = element.alt || element.title || '';
        } else {
            text = element.textContent || element.innerText || '';
        }
        return text.trim().substring(0, 100);
    }
    getImportantAttributes(element) {
        const importantAttrs = ['href', 'src', 'type', 'name', 'value', 'title', 'alt'];
        const attrs = {};
        importantAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
                attrs[attr] = element.getAttribute(attr);
            }
        });
        return attrs;
    }
    getElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        let selector = element.tagName.toLowerCase();
        if (element.className) {
            selector = '.'  element.className.split(' ').join('.');
        }
        return selector;
    }
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };
    }
    getScrollData(direction) {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const scrollPercent = Math.round((scrollTop  windowHeight) / documentHeight * 100);
        return {
            type: 'scroll',
            scrollTop: scrollTop,
            scrollPercent: Math.min(scrollPercent, 100),
            direction: direction,
            timestamp: Date.now()
        };
    }
    getFormData(form) {
        const formData = new FormData(form);
        const fields = {};
        for (let [key, value] of formData.entries()) {
            if (this.isSensitiveField(key)) {
                fields[key] = '[HIDDEN]';
            } else {
                fields[key] = value.toString().substring(0, 100);
            }
        }
        return {
            type: 'form_submit',
            action: form.action,
            method: form.method,
            fields: fields,
            element: this.getElementData(form),
            timestamp: Date.now()
        };
    }
    isImportantElement(element) {
        const importantTags = ['a', 'button', 'input', 'select', 'textarea'];
        const importantClasses = ['btn', 'button', 'link', 'nav', 'menu'];
        if (importantTags.includes(element.tagName.toLowerCase())) {
            return true;
        }
        return importantClasses.some(cls => 
            element.className.toLowerCase().includes(cls)
        );
    }
    isImportantScrollPoint(scrollData) {
        const importantPoints = [25, 50, 75, 90, 100];
        return importantPoints.includes(scrollData.scrollPercent);
    }
    isFormField(element) {
        const formTags = ['input', 'select', 'textarea'];
        return formTags.includes(element.tagName.toLowerCase());
    }
    isSensitiveField(fieldName) {
        const sensitiveFields = ['password', 'pass', 'pwd', 'secret', 'token', 'ssn', 'credit', 'card'];
        return sensitiveFields.some(field => 
            fieldName.toLowerCase().includes(field)
        );
    }
    isDownloadLink(link) {
        const downloadExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
        return downloadExtensions.some(ext => 
            link.href.toLowerCase().includes(ext)
        );
    }
    isOutboundLink(link) {
        const currentDomain = window.location.hostname;
        const linkDomain = this.getDomainFromUrl(link.href);
        return linkDomain && linkDomain !== currentDomain;
    }
    getDomainFromUrl(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return null;
        }
    }
    getFilenameFromUrl(url) {
        return url.split('/').pop().split('?')[0];
    }
    getFileTypeFromUrl(url) {
        const filename = this.getFilenameFromUrl(url);
        const extension = filename.split('.').pop();
        return extension ? extension.toLowerCase() : 'unknown';
    }
    handleSpecialClicks(element, event) {
        if (element.tagName === 'BUTTON' || element.type === 'button') {
            this.sendBehaviorEvent('button_click', {
                buttonText: this.getElementText(element),
                buttonType: element.type,
                element: this.getElementData(element)
            });
        }
        if (element.tagName === 'A') {
            this.sendBehaviorEvent('link_click', {
                url: element.href,
                linkText: this.getElementText(element),
                element: this.getElementData(element)
            });
        }
    }
    detectKeyboardShortcuts(event, keySequence) {
        if (event.ctrlKey || event.metaKey) {
            const shortcuts = {
                'KeyC': 'copy',
                'KeyV': 'paste',
                'KeyX': 'cut',
                'KeyZ': 'undo',
                'KeyY': 'redo',
                'KeyS': 'save',
                'KeyF': 'find'
            };
            if (shortcuts[event.code]) {
                this.sendBehaviorEvent('keyboard_shortcut', {
                    shortcut: shortcuts[event.code],
                    key: event.key,
                    timestamp: Date.now()
                });
            }
        }
    }
    sendBehaviorEvent(type, data) {
        if (!this.config.trackUserBehavior) return;
        const eventData = {
            site_id: this.config.siteId,
            visitor_id: this.tracker.visitorId,
            session_id: this.tracker.sessionId,
            page_url: window.location.href,
            event_type: 'behavior',
            event_name: type,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.tracker.sendEvent('behavior', eventData);
    }
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        return function(...args) {
            const currentTime = Date.now();
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }
    getBehaviorStats() {
        return {
            clicks: this.behaviors.clicks.length,
            scrolls: this.behaviors.scrolls.length,
            hovers: this.behaviors.hovers.length,
            forms: this.behaviors.forms.length,
            downloads: this.behaviors.downloads.length,
            outboundLinks: this.behaviors.outboundLinks.length,
            maxScrollDepth: Math.max(...this.behaviors.scrolls.map(s => s.scrollPercent), 0)
        };
    }
}
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BehaviorTracker;
} else if (typeof window !== 'undefined') {
    window.BehaviorTracker = BehaviorTracker;
}
class PerformanceMonitor {
    constructor(tracker) {
        this.tracker = tracker;
        this.config = tracker.config;
        this.performanceData = {
            navigation: null,
            resources: [],
            vitals: {},
            memory: null,
            connection: null
        };
        this.observers = {};
        this.init();
    }
    init() {
        if (!this.config.trackPerformance) return;
        if (document.readyState === 'complete') {
            this.collectPerformanceData();
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.collectPerformanceData(), 100);
            });
        }
        this.monitorCoreWebVitals();
        this.monitorResourceLoading();
        this.monitorMemoryUsage();
        this.monitorNetworkConnection();
        this.monitorLongTasks();
    }
    collectPerformanceData() {
        if (!window.performance) return;
        const navigation = this.getNavigationTiming();
        const resources = this.getResourceTiming();
        this.performanceData.navigation = navigation;
        this.performanceData.resources = resources;
        this.sendPerformanceEvent('navigation', navigation);
        if (resources.length > 0) {
            this.sendPerformanceEvent('resources', {
                count: resources.length,
                totalSize: resources.reduce((sum, r) => sum  (r.transferSize || 0), 0),
                slowResources: resources.filter(r => r.duration > 1000)
            });
        }
    }
    getNavigationTiming() {
        const timing = window.performance.timing;
        const navigation = window.performance.navigation;
        const navigationStart = timing.navigationStart;
        return {
            navigationStart: 0,
            unloadEventStart: timing.unloadEventStart - navigationStart,
            unloadEventEnd: timing.unloadEventEnd - navigationStart,
            redirectStart: timing.redirectStart - navigationStart,
            redirectEnd: timing.redirectEnd - navigationStart,
            fetchStart: timing.fetchStart - navigationStart,
            domainLookupStart: timing.domainLookupStart - navigationStart,
            domainLookupEnd: timing.domainLookupEnd - navigationStart,
            connectStart: timing.connectStart - navigationStart,
            connectEnd: timing.connectEnd - navigationStart,
            secureConnectionStart: timing.secureConnectionStart - navigationStart,
            requestStart: timing.requestStart - navigationStart,
            responseStart: timing.responseStart - navigationStart,
            responseEnd: timing.responseEnd - navigationStart,
            domLoading: timing.domLoading - navigationStart,
            domInteractive: timing.domInteractive - navigationStart,
            domContentLoadedEventStart: timing.domContentLoadedEventStart - navigationStart,
            domContentLoadedEventEnd: timing.domContentLoadedEventEnd - navigationStart,
            domComplete: timing.domComplete - navigationStart,
            loadEventStart: timing.loadEventStart - navigationStart,
            loadEventEnd: timing.loadEventEnd - navigationStart,
            redirectTime: timing.redirectEnd - timing.redirectStart,
            dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
            tcpTime: timing.connectEnd - timing.connectStart,
            requestTime: timing.responseEnd - timing.requestStart,
            responseTime: timing.responseStart - timing.requestStart,
            domParseTime: timing.domComplete - timing.domLoading,
            domReadyTime: timing.domContentLoadedEventEnd - navigationStart,
            loadCompleteTime: timing.loadEventEnd - navigationStart,
            navigationType: navigation.type,
            redirectCount: navigation.redirectCount
        };
    }
    getResourceTiming() {
        if (!window.performance.getEntriesByType) return [];
        const resources = window.performance.getEntriesByType('resource');
        return resources.map(resource => ({
            name: resource.name,
            type: this.getResourceType(resource),
            startTime: Math.round(resource.startTime),
            duration: Math.round(resource.duration),
            transferSize: resource.transferSize || 0,
            encodedBodySize: resource.encodedBodySize || 0,
            decodedBodySize: resource.decodedBodySize || 0,
            initiatorType: resource.initiatorType,
            nextHopProtocol: resource.nextHopProtocol,
            renderBlockingStatus: resource.renderBlockingStatus
        }));
    }
    getResourceType(resource) {
        if (resource.initiatorType) {
            return resource.initiatorType;
        }
        const url = resource.name.toLowerCase();
        if (url.includes('.js')) return 'script';
        if (url.includes('.css')) return 'stylesheet';
        if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif') || url.includes('.webp')) return 'image';
        if (url.includes('.woff') || url.includes('.ttf')) return 'font';
        return 'other';
    }
    monitorCoreWebVitals() {
        this.observePerformanceEntry('paint', (entries) => {
            entries.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                    this.performanceData.vitals.fcp = Math.round(entry.startTime);
                    this.sendPerformanceEvent('vital', {
                        name: 'FCP',
                        value: this.performanceData.vitals.fcp,
                        rating: this.rateVital('FCP', this.performanceData.vitals.fcp)
                    });
                }
            });
        });
        this.observePerformanceEntry('largest-contentful-paint', (entries) => {
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
                this.performanceData.vitals.lcp = Math.round(lastEntry.startTime);
                this.sendPerformanceEvent('vital', {
                    name: 'LCP',
                    value: this.performanceData.vitals.lcp,
                    rating: this.rateVital('LCP', this.performanceData.vitals.lcp)
                });
            }
        });
        this.observePerformanceEntry('first-input', (entries) => {
            entries.forEach(entry => {
                this.performanceData.vitals.fid = Math.round(entry.processingStart - entry.startTime);
                this.sendPerformanceEvent('vital', {
                    name: 'FID',
                    value: this.performanceData.vitals.fid,
                    rating: this.rateVital('FID', this.performanceData.vitals.fid)
                });
            });
        });
        let clsValue = 0;
        this.observePerformanceEntry('layout-shift', (entries) => {
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue = entry.value;
                }
            });
            this.performanceData.vitals.cls = Math.round(clsValue * 1000) / 1000;
        });
        window.addEventListener('beforeunload', () => {
            if (this.performanceData.vitals.cls !== undefined) {
                this.sendPerformanceEvent('vital', {
                    name: 'CLS',
                    value: this.performanceData.vitals.cls,
                    rating: this.rateVital('CLS', this.performanceData.vitals.cls)
                }, true);
            }
        });
    }
    monitorResourceLoading() {
        this.observePerformanceEntry('resource', (entries) => {
            entries.forEach(entry => {
                if (entry.duration > 1000) {
                    this.sendPerformanceEvent('slow_resource', {
                        name: entry.name,
                        type: this.getResourceType(entry),
                        duration: Math.round(entry.duration),
                        size: entry.transferSize || 0
                    });
                }
                if (entry.transferSize > 1024 * 1024) { // 1MB
                    this.sendPerformanceEvent('large_resource', {
                        name: entry.name,
                        type: this.getResourceType(entry),
                        size: entry.transferSize
                    });
                }
            });
        });
    }
    monitorMemoryUsage() {
        if (!window.performance.memory) return;
        const collectMemoryData = () => {
            const memory = window.performance.memory;
            this.performanceData.memory = {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                usageRatio: memory.usedJSHeapSize / memory.jsHeapSizeLimit
            };
            if (this.performanceData.memory.usageRatio > 0.9) {
                this.sendPerformanceEvent('high_memory_usage', this.performanceData.memory);
            }
        };
        setInterval(collectMemoryData, 30000); // 30秒
        collectMemoryData(); // 立即执行一次
    }
    monitorNetworkConnection() {
        if (!navigator.connection) return;
        const connection = navigator.connection;
        const collectConnectionData = () => {
            this.performanceData.connection = {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData
            };
        };
        collectConnectionData();
        connection.addEventListener('change', () => {
            collectConnectionData();
            this.sendPerformanceEvent('connection_change', this.performanceData.connection);
        });
    }
    monitorLongTasks() {
        this.observePerformanceEntry('longtask', (entries) => {
            entries.forEach(entry => {
                this.sendPerformanceEvent('long_task', {
                    duration: Math.round(entry.duration),
                    startTime: Math.round(entry.startTime),
                    attribution: entry.attribution ? entry.attribution.map(attr => ({
                        name: attr.name,
                        entryType: attr.entryType,
                        startTime: Math.round(attr.startTime),
                        duration: Math.round(attr.duration)
                    })) : []
                });
            });
        });
    }
    observePerformanceEntry(type, callback) {
        if (!window.PerformanceObserver) return;
        try {
            const observer = new PerformanceObserver((list) => {
                callback(list.getEntries());
            });
            observer.observe({ entryTypes: [type] });
            this.observers[type] = observer;
        } catch (error) {
            console.warn(`[WebStats] 无法观察性能条目类型: ${type}`, error);
        }
    }
    rateVital(name, value) {
        const thresholds = {
            FCP: { good: 1800, poor: 3000 },
            LCP: { good: 2500, poor: 4000 },
            FID: { good: 100, poor: 300 },
            CLS: { good: 0.1, poor: 0.25 }
        };
        const threshold = thresholds[name];
        if (!threshold) return 'unknown';
        if (value <= threshold.good) return 'good';
        if (value <= threshold.poor) return 'needs-improvement';
        return 'poor';
    }
    sendPerformanceEvent(type, data, sync = false) {
        const eventData = {
            site_id: this.config.siteId,
            visitor_id: this.tracker.visitorId,
            session_id: this.tracker.sessionId,
            page_url: window.location.href,
            event_type: 'performance',
            event_name: type,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.tracker.sendEvent('performance', eventData, sync);
    }
    getPerformanceSummary() {
        return {
            navigation: this.performanceData.navigation,
            vitals: this.performanceData.vitals,
            memory: this.performanceData.memory,
            connection: this.performanceData.connection,
            resourceCount: this.performanceData.resources.length,
            totalResourceSize: this.performanceData.resources.reduce((sum, r) => sum  (r.transferSize || 0), 0)
        };
    }
    cleanup() {
        Object.values(this.observers).forEach(observer => {
            observer.disconnect();
        });
        this.observers = {};
    }
}
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
} else if (typeof window !== 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
}
import WebStatsTracker from './core/tracker.js';
import BehaviorTracker from './modules/behavior-tracker.js';
import PerformanceMonitor from './modules/performance-monitor.js';
class ExtendedWebStatsTracker extends WebStatsTracker {
    constructor(config = {}) {
        super(config);
        if (this.config.trackUserBehavior) {
            this.behaviorTracker = new BehaviorTracker(this);
        }
        if (this.config.trackPerformance) {
            this.performanceMonitor = new PerformanceMonitor(this);
        }
    }
    trackEvent(eventName, eventData = {}) {
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'custom',
            event_name: eventName,
            event_value: JSON.stringify(eventData),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('custom_event', data);
        this.log('自定义事件已发送', { eventName, eventData });
    }
    trackEcommerce(action, data = {}) {
        const ecommerceData = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'ecommerce',
            event_name: action,
            event_value: JSON.stringify(data),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('ecommerce', ecommerceData);
        this.log('电商事件已发送', { action, data });
    }
    trackGoal(goalName, goalValue = null) {
        const goalData = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            page_url: window.location.href,
            event_type: 'goal',
            event_name: goalName,
            event_value: goalValue ? JSON.stringify({ value: goalValue }) : null,
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('goal', goalData);
        this.log('转化目标已发送', { goalName, goalValue });
    }
    setUserProperty(key, value) {
        if (!this.userProperties) {
            this.userProperties = {};
        }
        this.userProperties[key] = value;
        const data = {
            site_id: this.config.siteId,
            visitor_id: this.visitorId,
            session_id: this.sessionId,
            event_type: 'user_property',
            event_name: 'set_property',
            event_value: JSON.stringify({ [key]: value }),
            timestamp: Math.floor(Date.now() / 1000)
        };
        this.sendEvent('user_property', data);
    }
    getVisitorInfo() {
        return {
            visitorId: this.visitorId,
            sessionId: this.sessionId,
            isNewVisitor: !this.getCookie('_ws_visitor_id'),
            userProperties: this.userProperties || {},
            pageStartTime: this.pageStartTime,
            lastActivityTime: this.lastActivityTime
        };
    }
    getPerformanceSummary() {
        if (this.performanceMonitor) {
            return this.performanceMonitor.getPerformanceSummary();
        }
        return null;
    }
    getBehaviorStats() {
        if (this.behaviorTracker) {
            return this.behaviorTracker.getBehaviorStats();
        }
        return null;
    }
    flush() {
        this.sendQueuedEvents();
    }
    pause() {
        this.config.autoTrack = false;
        this.log('跟踪已暂停');
    }
    resume() {
        this.config.autoTrack = true;
        this.log('跟踪已恢复');
    }
    destroy() {
        this.sendQueuedEvents(true);
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
        }
        if (this.batchTimer) {
            clearInterval(this.batchTimer);
        }
        if (this.performanceMonitor) {
            this.performanceMonitor.cleanup();
        }
        this.log('跟踪器已销毁');
    }
}
const WebStats = {
    version: '1.0.0',
    create: function(config) {
        return new ExtendedWebStatsTracker(config);
    },
    init: function(config = {}) {
        if (window._wsTracker) {
            return window._wsTracker;
        }
        const tracker = new ExtendedWebStatsTracker(config);
        window._wsTracker = tracker;
        return tracker;
    },
    getInstance: function() {
        return window._wsTracker || null;
    }
};
if (typeof window !== 'undefined') {
    if (window._wsConfig && window._wsConfig.siteId) {
        WebStats.init(window._wsConfig);
    }
    if (window._wsQueue && Array.isArray(window._wsQueue)) {
        const tracker = WebStats.getInstance();
        if (tracker) {
            window._wsQueue.forEach(command => {
                if (Array.isArray(command) && command.length > 0) {
                    const [action, ...args] = command;
                    switch (action) {
                        case 'init':
                            WebStats.init(args[0]);
                            break;
                        case 'track':
                            tracker.trackPageView();
                            break;
                        case 'event':
                            tracker.trackEvent(args[0], args[1]);
                            break;
                        case 'ecommerce':
                            tracker.trackEcommerce(args[0], args[1]);
                            break;
                        case 'goal':
                            tracker.trackGoal(args[0], args[1]);
                            break;
                        case 'set':
                            tracker.setUserProperty(args[0], args[1]);
                            break;
                    }
                }
            });
            window._wsQueue = [];
        }
    }
}
export default WebStats;
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebStats;
}
if (typeof window !== 'undefined') {
    window.WebStats = WebStats;
    window.WebStatsTracker = ExtendedWebStatsTracker;
}
})(window, document);
