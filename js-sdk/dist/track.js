/**
 * 异步加载器
 * 支持多种加载方式和配置
 */

(function() {
    'use strict';

    // 全局配置对象
    window._wsConfig = window._wsConfig || {};
    window._wsQueue = window._wsQueue || [];

    /**
     * 异步加载器类
     */
    class AsyncLoader {
        constructor() {
            this.loaded = false;
            this.tracker = null;
            this.config = this.parseConfig();
            
            this.init();
        }

        /**
         * 解析配置
         */
        parseConfig() {
            // 从脚本标签获取配置
            const scripts = document.getElementsByTagName('script');
            let config = {};

            for (let script of scripts) {
                const src = script.src;
                if (src && src.includes('track.js')) {
                    // 从URL参数解析配置
                    const url = new URL(src);
                    config.siteId = url.searchParams.get('id') || url.searchParams.get('site_id');
                    config.apiUrl = url.searchParams.get('api') || url.searchParams.get('api_url');
                    config.debug = url.searchParams.get('debug') === 'true';
                    config.autoTrack = url.searchParams.get('auto') !== 'false';
                    
                    // 从data属性获取配置
                    Object.keys(script.dataset).forEach(key => {
                        const value = script.dataset[key];
                        if (value === 'true') config[key] = true;
                        else if (value === 'false') config[key] = false;
                        else if (!isNaN(value)) config[key] = Number(value);
                        else config[key] = value;
                    });
                    
                    break;
                }
            }

            // 合并全局配置
            return Object.assign({}, window._wsConfig, config);
        }

        /**
         * 初始化
         */
        init() {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.loadTracker());
            } else {
                this.loadTracker();
            }
        }

        /**
         * 加载跟踪器
         */
        loadTracker() {
            if (this.loaded) return;

            try {
                // 检查是否已经加载了跟踪器类
                if (typeof WebStatsTracker !== 'undefined') {
                    this.initTracker();
                } else {
                    // 动态加载跟踪器脚本
                    this.loadTrackerScript();
                }
            } catch (error) {
                console.error('[WebStats] 加载跟踪器失败:', error);
            }
        }

        /**
         * 动态加载跟踪器脚本
         */
        loadTrackerScript() {
            const script = document.createElement('script');
            script.async = true;
            script.src = this.getTrackerScriptUrl();
            
            script.onload = () => {
                this.initTracker();
            };
            
            script.onerror = () => {
                console.error('[WebStats] 无法加载跟踪器脚本');
            };

            document.head.appendChild(script);
        }

        /**
         * 获取跟踪器脚本URL
         */
        getTrackerScriptUrl() {
            const baseUrl = this.config.apiUrl || 'http://localhost:9002';
            return `${baseUrl}/js/tracker.min.js`;
        }

        /**
         * 初始化跟踪器
         */
        initTracker() {
            if (this.loaded || typeof WebStatsTracker === 'undefined') return;

            try {
                this.tracker = new WebStatsTracker(this.config);
                this.loaded = true;

                // 处理队列中的命令
                this.processQueue();

                // 设置全局接口
                this.setupGlobalInterface();

                console.log('[WebStats] 跟踪器初始化成功');
            } catch (error) {
                console.error('[WebStats] 跟踪器初始化失败:', error);
            }
        }

        /**
         * 处理队列中的命令
         */
        processQueue() {
            while (window._wsQueue.length > 0) {
                const command = window._wsQueue.shift();
                this.executeCommand(command);
            }
        }

        /**
         * 执行命令
         */
        executeCommand(command) {
            if (!Array.isArray(command) || command.length === 0) return;

            const [action, ...args] = command;

            try {
                switch (action) {
                    case 'config':
                        this.updateConfig(args[0]);
                        break;
                    case 'track':
                        this.tracker && this.tracker.trackPageView();
                        break;
                    case 'event':
                        this.tracker && this.tracker.trackEvent(args[0], args[1]);
                        break;
                    case 'set':
                        this.tracker && this.tracker.set(args[0], args[1]);
                        break;
                    default:
                        console.warn('[WebStats] 未知命令:', action);
                }
            } catch (error) {
                console.error('[WebStats] 执行命令失败:', error);
            }
        }

        /**
         * 更新配置
         */
        updateConfig(newConfig) {
            Object.assign(this.config, newConfig);
            if (this.tracker) {
                Object.assign(this.tracker.config, newConfig);
            }
        }

        /**
         * 设置全局接口
         */
        setupGlobalInterface() {
            // 重写全局函数，直接执行命令
            window._ws = (...args) => {
                if (this.loaded && this.tracker) {
                    this.executeCommand(args);
                } else {
                    window._wsQueue.push(args);
                }
            };

            // 提供直接访问跟踪器的接口
            window._wsTracker = this.tracker;
        }
    }

    /**
     * 检测环境和加载方式
     */
    function detectLoadingMethod() {
        // 检查是否通过异步脚本加载
        const currentScript = document.currentScript;
        if (currentScript && currentScript.async) {
            return 'async';
        }

        // 检查是否通过动态插入加载
        if (document.readyState === 'loading') {
            return 'dynamic';
        }

        return 'sync';
    }

    /**
     * 初始化全局函数（在跟踪器加载前）
     */
    function initGlobalFunction() {
        window._ws = window._ws || function() {
            window._wsQueue.push(Array.prototype.slice.call(arguments));
        };
    }

    /**
     * 主入口
     */
    function main() {
        // 避免重复初始化
        if (window._wsLoader) return;

        initGlobalFunction();
        
        const loader = new AsyncLoader();
        window._wsLoader = loader;

        // 兼容旧版本API
        window.webstats = window._ws;
    }

    // 立即执行
    main();

})();

/**
 * 使用示例：
 * 
 * 1. 基础用法：
 * <script async src="https://stats.yourdomain.com/js/track.js?id=SITE_ID"></script>
 * 
 * 2. 带配置的用法：
 * <script async src="https://stats.yourdomain.com/js/track.js?id=SITE_ID&debug=true&api=https://api.yourdomain.com"></script>
 * 
 * 3. 通过data属性配置：
 * <script async src="https://stats.yourdomain.com/js/track.js" 
 *         data-site-id="SITE_ID" 
 *         data-debug="true" 
 *         data-auto-track="true"></script>
 * 
 * 4. 通过全局配置：
 * <script>
 *   window._wsConfig = {
 *     siteId: 'SITE_ID',
 *     apiUrl: 'https://api.yourdomain.com',
 *     debug: true
 *   };
 * </script>
 * <script async src="https://stats.yourdomain.com/js/track.js"></script>
 * 
 * 5. 手动调用：
 * <script>
 *   _ws('config', { siteId: 'SITE_ID' });
 *   _ws('track');
 *   _ws('event', 'click', { button: 'signup' });
 * </script>
 */
