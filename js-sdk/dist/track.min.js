(function() {
    'use strict';
    window._wsConfig = window._wsConfig || {};
    window._wsQueue = window._wsQueue || [];
    class AsyncLoader {
        constructor() {
            this.loaded = false;
            this.tracker = null;
            this.config = this.parseConfig();
            this.init();
        }
        parseConfig() {
            const scripts = document.getElementsByTagName('script');
            let config = {};
            for (let script of scripts) {
                const src = script.src;
                if (src && src.includes('track.js')) {
                    const url = new URL(src);
                    config.siteId = url.searchParams.get('id') || url.searchParams.get('site_id');
                    config.apiUrl = url.searchParams.get('api') || url.searchParams.get('api_url');
                    config.debug = url.searchParams.get('debug') === 'true';
                    config.autoTrack = url.searchParams.get('auto') !== 'false';
                    Object.keys(script.dataset).forEach(key => {
                        const value = script.dataset[key];
                        if (value === 'true') config[key] = true;
                        else if (value === 'false') config[key] = false;
                        else if (!isNaN(value)) config[key] = Number(value);
                        else config[key] = value;
                    });
                    break;
                }
            }
            return Object.assign({}, window._wsConfig, config);
        }
        init() {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.loadTracker());
            } else {
                this.loadTracker();
            }
        }
        loadTracker() {
            if (this.loaded) return;
            try {
                if (typeof WebStatsTracker !== 'undefined') {
                    this.initTracker();
                } else {
                    this.loadTrackerScript();
                }
            } catch (error) {
                console.error('[WebStats] 加载跟踪器失败:', error);
            }
        }
        loadTrackerScript() {
            const script = document.createElement('script');
            script.async = true;
            script.src = this.getTrackerScriptUrl();
            script.onload = () => {
                this.initTracker();
            };
            script.onerror = () => {
                console.error('[WebStats] 无法加载跟踪器脚本');
            };
            document.head.appendChild(script);
        }
        getTrackerScriptUrl() {
            const baseUrl = this.config.apiUrl || 'http://localhost:9002';
            return `${baseUrl}/js/tracker.min.js`;
        }
        initTracker() {
            if (this.loaded || typeof WebStatsTracker === 'undefined') return;
            try {
                this.tracker = new WebStatsTracker(this.config);
                this.loaded = true;
                this.processQueue();
                this.setupGlobalInterface();
                console.log('[WebStats] 跟踪器初始化成功');
            } catch (error) {
                console.error('[WebStats] 跟踪器初始化失败:', error);
            }
        }
        processQueue() {
            while (window._wsQueue.length > 0) {
                const command = window._wsQueue.shift();
                this.executeCommand(command);
            }
        }
        executeCommand(command) {
            if (!Array.isArray(command) || command.length === 0) return;
            const [action, ...args] = command;
            try {
                switch (action) {
                    case 'config':
                        this.updateConfig(args[0]);
                        break;
                    case 'track':
                        this.tracker && this.tracker.trackPageView();
                        break;
                    case 'event':
                        this.tracker && this.tracker.trackEvent(args[0], args[1]);
                        break;
                    case 'set':
                        this.tracker && this.tracker.set(args[0], args[1]);
                        break;
                    default:
                        console.warn('[WebStats] 未知命令:', action);
                }
            } catch (error) {
                console.error('[WebStats] 执行命令失败:', error);
            }
        }
        updateConfig(newConfig) {
            Object.assign(this.config, newConfig);
            if (this.tracker) {
                Object.assign(this.tracker.config, newConfig);
            }
        }
        setupGlobalInterface() {
            window._ws = (...args) => {
                if (this.loaded && this.tracker) {
                    this.executeCommand(args);
                } else {
                    window._wsQueue.push(args);
                }
            };
            window._wsTracker = this.tracker;
        }
    }
    function detectLoadingMethod() {
        const currentScript = document.currentScript;
        if (currentScript && currentScript.async) {
            return 'async';
        }
        if (document.readyState === 'loading') {
            return 'dynamic';
        }
        return 'sync';
    }
    function initGlobalFunction() {
        window._ws = window._ws || function() {
            window._wsQueue.push(Array.prototype.slice.call(arguments));
        };
    }
    function main() {
        if (window._wsLoader) return;
        initGlobalFunction();
        const loader = new AsyncLoader();
        window._wsLoader = loader;
        window.webstats = window._ws;
    }
    main();
})();
