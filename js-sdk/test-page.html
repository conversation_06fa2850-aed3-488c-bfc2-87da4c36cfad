<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebStats SDK 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .log {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>WebStats JavaScript SDK 测试页面</h1>
    
    <div class="status" id="status">
        <strong>状态:</strong> 正在初始化...
    </div>

    <div class="test-section">
        <h2>1. SDK 状态检查</h2>
        <button onclick="checkSDKStatus()">检查SDK状态</button>
        <button onclick="getVisitorInfo()">获取访客信息</button>
        <button onclick="getPerformanceInfo()">获取性能信息</button>
    </div>

    <div class="test-section">
        <h2>2. 事件跟踪测试</h2>
        <button onclick="trackCustomEvent()">发送自定义事件</button>
        <button onclick="trackGoal()">跟踪转化目标</button>
        <button onclick="setUserProperty()">设置用户属性</button>
    </div>

    <div class="test-section">
        <h2>3. 电商事件测试</h2>
        <button onclick="trackAddToCart()">添加到购物车</button>
        <button onclick="trackPurchase()">完成购买</button>
    </div>

    <div class="test-section">
        <h2>4. 用户交互测试</h2>
        <p>以下元素会自动跟踪用户交互：</p>
        <button onclick="testClick()">测试按钮点击</button>
        <a href="#test" onclick="testLinkClick(event)">测试链接点击</a>
        <a href="https://www.google.com" target="_blank">外部链接测试</a>
        
        <form onsubmit="testFormSubmit(event)" style="margin-top: 15px;">
            <input type="text" placeholder="测试输入框" style="padding: 5px; margin: 5px;">
            <button type="submit">提交表单</button>
        </form>
    </div>

    <div class="test-section">
        <h2>5. 滚动测试</h2>
        <p>向下滚动页面测试滚动深度跟踪...</p>
        <div style="height: 500px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); padding: 20px;">
            <p>这是一个长内容区域，用于测试滚动跟踪功能。</p>
            <p>继续滚动以测试不同的滚动深度...</p>
            <p style="margin-top: 200px;">中间内容</p>
            <p style="margin-top: 200px;">底部内容</p>
        </div>
    </div>

    <div class="test-section">
        <h2>6. 控制功能测试</h2>
        <button onclick="flushEvents()">手动发送事件</button>
        <button onclick="pauseTracking()">暂停跟踪</button>
        <button onclick="resumeTracking()">恢复跟踪</button>
    </div>

    <div class="test-section">
        <h2>7. 调试日志</h2>
        <div id="log" class="log">
            <div class="info">日志将显示在这里...</div>
        </div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <!-- 使用异步加载方式引入SDK -->
    <script>
        // 预设配置
        window._wsConfig = {
            siteId: 'TRACK001',
            apiUrl: 'http://localhost:9002/api/v1',
            debug: true,
            trackUserBehavior: true,
            trackPerformance: true
        };
        
        // 队列式调用（在SDK加载前就可以调用）
        window._wsQueue = window._wsQueue || [];
        window._ws = window._ws || function() {
            window._wsQueue.push(Array.prototype.slice.call(arguments));
        };
        
        // 预先发送一些事件
        _ws('track'); // 页面浏览
    </script>
    
    <!-- 异步加载SDK -->
    <script async src="http://localhost:9002/js/track.min.js"></script>
    
    <script>
        let tracker = null;
        
        // 等待SDK加载完成
        function waitForSDK() {
            if (window._wsTracker) {
                tracker = window._wsTracker;
                updateStatus('SDK 已加载并初始化', 'success');
                log('SDK 初始化完成', {
                    visitorId: tracker.visitorId,
                    sessionId: tracker.sessionId,
                    config: tracker.config
                });
            } else {
                setTimeout(waitForSDK, 100);
            }
        }
        
        // 页面加载完成后开始等待SDK
        window.addEventListener('load', function() {
            waitForSDK();
        });

        // 日志函数
        function log(message, data = null, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${time}]</strong> ${message}`;
            if (data) {
                logEntry.innerHTML += `<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 状态更新函数
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<strong>状态:</strong> <span class="${type}">${message}</span>`;
        }

        // 测试函数
        function checkSDKStatus() {
            if (tracker) {
                const info = {
                    loaded: true,
                    visitorId: tracker.visitorId,
                    sessionId: tracker.sessionId,
                    config: tracker.config,
                    queueLength: tracker.eventQueue ? tracker.eventQueue.length : 0
                };
                log('SDK 状态检查', info, 'success');
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function getVisitorInfo() {
            if (tracker) {
                const info = tracker.getVisitorInfo();
                log('访客信息', info);
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function getPerformanceInfo() {
            if (tracker) {
                const info = tracker.getPerformanceSummary();
                log('性能信息', info);
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function trackCustomEvent() {
            if (tracker) {
                tracker.trackEvent('test_button_click', {
                    button_name: '自定义事件测试',
                    test_type: 'manual',
                    timestamp: Date.now()
                });
                log('自定义事件已发送', null, 'success');
            } else {
                _ws('event', 'test_button_click', { queued: true });
                log('自定义事件已加入队列', null, 'info');
            }
        }

        function trackGoal() {
            if (tracker) {
                tracker.trackGoal('test_goal', 100);
                log('转化目标已发送', null, 'success');
            } else {
                _ws('goal', 'test_goal', 100);
                log('转化目标已加入队列', null, 'info');
            }
        }

        function setUserProperty() {
            if (tracker) {
                tracker.setUserProperty('test_user_type', 'sdk_tester');
                tracker.setUserProperty('test_session', Date.now());
                log('用户属性已设置', null, 'success');
            } else {
                _ws('set', 'test_user_type', 'sdk_tester');
                log('用户属性已加入队列', null, 'info');
            }
        }

        function trackAddToCart() {
            if (tracker) {
                tracker.trackEcommerce('add_to_cart', {
                    product_id: 'test_product_001',
                    product_name: '测试商品',
                    category: '测试分类',
                    price: 99.99,
                    quantity: 1
                });
                log('添加到购物车事件已发送', null, 'success');
            } else {
                _ws('ecommerce', 'add_to_cart', { product_id: 'test_product_001' });
                log('电商事件已加入队列', null, 'info');
            }
        }

        function trackPurchase() {
            if (tracker) {
                tracker.trackEcommerce('purchase', {
                    order_id: 'TEST_ORDER_' + Date.now(),
                    total_amount: 99.99,
                    currency: 'CNY',
                    products: [{
                        product_id: 'test_product_001',
                        product_name: '测试商品',
                        price: 99.99,
                        quantity: 1
                    }]
                });
                log('购买事件已发送', null, 'success');
            } else {
                log('SDK 尚未加载，无法发送购买事件', null, 'error');
            }
        }

        function testClick() {
            log('按钮点击测试 - 这个点击会被自动跟踪');
        }

        function testLinkClick(event) {
            event.preventDefault();
            log('链接点击测试 - 这个点击会被自动跟踪');
        }

        function testFormSubmit(event) {
            event.preventDefault();
            log('表单提交测试 - 这个提交会被自动跟踪');
        }

        function flushEvents() {
            if (tracker) {
                tracker.flush();
                log('事件队列已手动发送', null, 'success');
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function pauseTracking() {
            if (tracker) {
                tracker.pause();
                log('跟踪已暂停', null, 'info');
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function resumeTracking() {
            if (tracker) {
                tracker.resume();
                log('跟踪已恢复', null, 'success');
            } else {
                log('SDK 尚未加载', null, 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">日志已清空...</div>';
        }

        // 监听滚动事件
        let scrollTimer;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(function() {
                const scrollPercent = Math.round((window.pageYOffset + window.innerHeight) / document.documentElement.scrollHeight * 100);
                log(`滚动深度: ${scrollPercent}%`);
            }, 500);
        });
    </script>
</body>
</html>
