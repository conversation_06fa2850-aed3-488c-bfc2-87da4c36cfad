# 前端组件设计文档

## 技术栈
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5
- **语言**: TypeScript
- **UI组件库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **图表库**: ECharts
- **代码规范**: ESLint + Prettier

## 项目结构
```
src/
├── components/          # 公共组件
│   ├── charts/         # 图表组件
│   ├── common/         # 通用组件
│   └── layout/         # 布局组件
├── views/              # 页面组件
│   ├── auth/           # 认证页面
│   ├── dashboard/      # 仪表板
│   ├── sites/          # 站点管理
│   ├── stats/          # 统计分析
│   └── settings/       # 设置页面
├── stores/             # Pinia状态管理
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── api/                # API接口
└── router/             # 路由配置
```

## 1. 布局组件

### 1.1 主布局 (MainLayout.vue)
```vue
<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <TopNavbar />
    </el-header>
    
    <el-container>
      <!-- 侧边栏 -->
      <el-aside class="sidebar" :width="sidebarWidth">
        <SideNavbar />
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>
```

### 1.2 顶部导航栏 (TopNavbar.vue)
```vue
<template>
  <div class="top-navbar">
    <!-- Logo -->
    <div class="logo">
      <img src="/logo.png" alt="Logo" />
      <span>网站统计</span>
    </div>
    
    <!-- 导航菜单 -->
    <el-menu mode="horizontal" :default-active="activeMenu">
      <el-menu-item index="dashboard">首页</el-menu-item>
      <el-menu-item index="console">控制台</el-menu-item>
      <el-menu-item index="help">帮助文档</el-menu-item>
    </el-menu>
    
    <!-- 用户信息 -->
    <div class="user-info">
      <el-dropdown>
        <span class="user-name">
          {{ userStore.userInfo.nickname }}
          <el-icon><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleProfile">个人设置</el-dropdown-item>
            <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
```

### 1.3 侧边导航栏 (SideNavbar.vue)
```vue
<template>
  <el-menu
    :default-active="activeMenu"
    class="side-menu"
    @select="handleMenuSelect"
  >
    <!-- 网站概况 -->
    <el-menu-item index="overview">
      <el-icon><data-analysis /></el-icon>
      <span>网站概况</span>
    </el-menu-item>
    
    <!-- 流量分析 -->
    <el-sub-menu index="traffic">
      <template #title>
        <el-icon><trend-charts /></el-icon>
        <span>流量分析</span>
      </template>
      <el-menu-item index="trend">趋势分析</el-menu-item>
      <el-menu-item index="compare">对比分析</el-menu-item>
      <el-menu-item index="realtime">当前在线</el-menu-item>
      <el-menu-item index="details">访问明细</el-menu-item>
    </el-sub-menu>
    
    <!-- 来源分析 -->
    <el-sub-menu index="source">
      <template #title>
        <el-icon><link /></el-icon>
        <span>来源分析</span>
      </template>
      <el-menu-item index="all-sources">全部来源</el-menu-item>
      <el-menu-item index="search-engines">搜索分析</el-menu-item>
      <el-menu-item index="keywords">搜索词</el-menu-item>
      <el-menu-item index="recent-search">最近搜索</el-menu-item>
      <el-menu-item index="referer-domains">来路域名</el-menu-item>
      <el-menu-item index="referer-pages">来路页面</el-menu-item>
      <el-menu-item index="source-ranking">来源升降榜</el-menu-item>
    </el-sub-menu>
    
    <!-- 受访分析 -->
    <el-sub-menu index="pages">
      <template #title>
        <el-icon><document /></el-icon>
        <span>受访分析</span>
      </template>
      <el-menu-item index="visited-domains">受访域名</el-menu-item>
      <el-menu-item index="visited-pages">受访页面</el-menu-item>
      <el-menu-item index="page-ranking">受访升降榜</el-menu-item>
    </el-sub-menu>
    
    <!-- 访客分析 -->
    <el-sub-menu index="visitors">
      <template #title>
        <el-icon><user /></el-icon>
        <span>访客分析</span>
      </template>
      <el-menu-item index="regions">地区分布</el-menu-item>
      <el-menu-item index="environment">系统环境</el-menu-item>
      <el-menu-item index="new-returning">新老访客</el-menu-item>
      <el-menu-item index="loyalty">忠诚度</el-menu-item>
      <el-menu-item index="activity">活跃度</el-menu-item>
    </el-sub-menu>
    
    <!-- 设置 -->
    <el-sub-menu index="settings">
      <template #title>
        <el-icon><setting /></el-icon>
        <span>设置</span>
      </template>
      <el-menu-item index="site-info">站点资料</el-menu-item>
      <el-menu-item index="tracking-code">获取代码</el-menu-item>
      <el-menu-item index="exclude-pages">排除受访</el-menu-item>
      <el-menu-item index="exclude-referers">排除来路</el-menu-item>
      <el-menu-item index="exclude-ips">排除访客IP</el-menu-item>
      <el-menu-item index="exclude-keywords">排除搜索词</el-menu-item>
      <el-menu-item index="exclude-bots">排除爬虫</el-menu-item>
      <el-menu-item index="view-password">查看密码</el-menu-item>
      <el-menu-item index="close-stats">关闭统计</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>
```

## 2. 图表组件

### 2.1 趋势线图 (TrendChart.vue)
```vue
<template>
  <div class="trend-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button label="today">今天</el-radio-button>
          <el-radio-button label="yesterday">昨天</el-radio-button>
          <el-radio-button label="7days">最近7天</el-radio-button>
          <el-radio-button label="30days">最近30天</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <div ref="chartRef" class="chart-container"></div>
    
    <div class="chart-legend">
      <div class="legend-item" v-for="item in legendData" :key="item.name">
        <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
        <span class="legend-name">{{ item.name }}</span>
        <span class="legend-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

interface Props {
  title: string
  data: Array<{
    date: string
    pv: number
    uv: number
    ip_count: number
  }>
  metrics?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  metrics: () => ['pv', 'uv']
})

const chartRef = ref<HTMLElement>()
const timeRange = ref('today')
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: props.metrics.map(metric => getMetricName(metric))
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: props.metrics.map(metric => ({
      name: getMetricName(metric),
      type: 'line',
      data: props.data.map(item => item[metric as keyof typeof item]),
      smooth: true,
      itemStyle: {
        color: getMetricColor(metric)
      }
    }))
  }
  
  chartInstance.setOption(option)
}

const getMetricName = (metric: string) => {
  const names: Record<string, string> = {
    pv: '浏览量(PV)',
    uv: '访客数(UV)',
    ip_count: 'IP数'
  }
  return names[metric] || metric
}

const getMetricColor = (metric: string) => {
  const colors: Record<string, string> = {
    pv: '#409EFF',
    uv: '#67C23A',
    ip_count: '#E6A23C'
  }
  return colors[metric] || '#909399'
}

onMounted(() => {
  initChart()
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })
</script>
```

### 2.2 饼图组件 (PieChart.vue)
```vue
<template>
  <div class="pie-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
    </div>
    
    <div class="chart-content">
      <div ref="chartRef" class="chart-container"></div>
      
      <div class="chart-table">
        <el-table :data="tableData" size="small">
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="value" label="数值" />
          <el-table-column prop="percentage" label="占比">
            <template #default="{ row }">
              <span :class="{ 'text-red': row.trend === 'up', 'text-green': row.trend === 'down' }">
                {{ row.percentage }}%
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
```

### 2.3 地图热力图 (MapChart.vue)
```vue
<template>
  <div class="map-chart">
    <div class="chart-header">
      <h3>地区分布</h3>
    </div>
    
    <div ref="chartRef" class="chart-container"></div>
    
    <div class="map-legend">
      <span>访问量少</span>
      <div class="legend-gradient"></div>
      <span>访问量多</span>
    </div>
  </div>
</template>
```

## 3. 数据表格组件

### 3.1 统计数据表格 (StatsTable.vue)
```vue
<template>
  <div class="stats-table">
    <div class="table-header">
      <h3>{{ title }}</h3>
      <div class="table-controls">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索..."
          size="small"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        
        <el-button @click="handleExport" size="small">
          <el-icon><download /></el-icon>
          导出
        </el-button>
      </div>
    </div>
    
    <el-table
      :data="filteredData"
      v-loading="loading"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :sortable="column.sortable"
        :width="column.width"
      >
        <template #default="{ row }" v-if="column.type === 'trend'">
          <span :class="getTrendClass(row[column.prop])">
            {{ formatValue(row[column.prop], column.format) }}
          </span>
        </template>
        
        <template #default="{ row }" v-else>
          {{ formatValue(row[column.prop], column.format) }}
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      v-if="showPagination"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @current-change="handlePageChange"
      layout="total, prev, pager, next, jumper"
    />
  </div>
</template>
```

## 4. 时间选择器组件

### 4.1 时间范围选择器 (DateRangePicker.vue)
```vue
<template>
  <div class="date-range-picker">
    <div class="quick-options">
      <el-button
        v-for="option in quickOptions"
        :key="option.value"
        :type="selectedQuick === option.value ? 'primary' : 'default'"
        size="small"
        @click="handleQuickSelect(option)"
      >
        {{ option.label }}
      </el-button>
    </div>
    
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      @change="handleDateChange"
    />
    
    <div class="compare-options" v-if="showCompare">
      <el-checkbox v-model="enableCompare">对比</el-checkbox>
      <el-select v-model="compareType" v-if="enableCompare" size="small">
        <el-option label="前一时期" value="previous" />
        <el-option label="上周同期" value="same_week" />
        <el-option label="去年同期" value="same_year" />
      </el-select>
    </div>
  </div>
</template>
```

## 5. 页面组件

### 5.1 用户首页 (Dashboard.vue)
```vue
<template>
  <div class="dashboard">
    <!-- 汇总统计卡片 -->
    <div class="summary-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <SummaryCard
            title="累计浏览次数(PV)"
            :value="summaryData.total_pv"
            :today="summaryData.today_pv"
            :yesterday="summaryData.yesterday_pv"
          />
        </el-col>
        <el-col :span="6">
          <SummaryCard
            title="累计独立访客(UV)"
            :value="summaryData.total_uv"
            :today="summaryData.today_uv"
            :yesterday="summaryData.yesterday_uv"
          />
        </el-col>
        <el-col :span="6">
          <SummaryCard
            title="累计IP"
            :value="summaryData.total_ip"
            :today="summaryData.today_ip"
            :yesterday="summaryData.yesterday_ip"
          />
        </el-col>
        <el-col :span="6">
          <SummaryCard
            title="在线访客"
            :value="summaryData.online_visitors"
            icon="user"
            color="success"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 站点列表 -->
    <div class="sites-section">
      <div class="section-header">
        <h2>我的站点</h2>
        <el-button type="primary" @click="handleAddSite">
          <el-icon><plus /></el-icon>
          添加站点
        </el-button>
      </div>

      <SitesTable
        :data="sitesData"
        :loading="sitesLoading"
        @view-report="handleViewReport"
        @get-code="handleGetCode"
        @edit-site="handleEditSite"
        @delete-site="handleDeleteSite"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSitesStore } from '@/stores/sites'
import SummaryCard from '@/components/common/SummaryCard.vue'
import SitesTable from '@/components/sites/SitesTable.vue'

const router = useRouter()
const sitesStore = useSitesStore()

const summaryData = ref({
  total_pv: 0,
  total_uv: 0,
  total_ip: 0,
  today_pv: 0,
  today_uv: 0,
  today_ip: 0,
  yesterday_pv: 0,
  yesterday_uv: 0,
  yesterday_ip: 0,
  online_visitors: 0
})

const sitesData = ref([])
const sitesLoading = ref(false)

const loadData = async () => {
  sitesLoading.value = true
  try {
    await sitesStore.fetchSites()
    sitesData.value = sitesStore.sites
    // 加载汇总数据
    await loadSummaryData()
  } finally {
    sitesLoading.value = false
  }
}

const handleViewReport = (site: any) => {
  router.push(`/stats/overview/${site.id}`)
}

onMounted(() => {
  loadData()
})
</script>
```

### 5.2 网站概况页 (Overview.vue)
```vue
<template>
  <div class="overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="site-info">
        <h1>{{ siteInfo.site_name }}</h1>
        <span class="site-url">{{ siteInfo.site_url }}</span>
      </div>

      <div class="header-controls">
        <DateRangePicker
          v-model="dateRange"
          :show-compare="true"
          @change="handleDateChange"
        />
      </div>
    </div>

    <!-- 基础统计 -->
    <div class="basic-stats">
      <el-row :gutter="20">
        <el-col :span="4" v-for="stat in basicStats" :key="stat.key">
          <StatCard
            :title="stat.title"
            :value="stat.value"
            :change="stat.change"
            :trend="stat.trend"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图表 -->
    <div class="trend-section">
      <TrendChart
        title="流量趋势"
        :data="trendData"
        :metrics="['pv', 'uv', 'ip_count']"
      />
    </div>

    <!-- Top数据 -->
    <el-row :gutter="20" class="top-data-section">
      <el-col :span="8">
        <TopList
          title="Top10搜索词"
          :data="topKeywords"
          :columns="[
            { prop: 'keyword', label: '搜索词' },
            { prop: 'pv', label: '浏览量(PV)' },
            { prop: 'percentage', label: '占比', type: 'percentage' }
          ]"
        />
      </el-col>

      <el-col :span="8">
        <TopList
          title="Top10来源网站"
          :data="topSources"
          :columns="[
            { prop: 'source', label: '来源网站' },
            { prop: 'pv', label: '浏览量(PV)' },
            { prop: 'percentage', label: '占比', type: 'percentage' }
          ]"
        />
      </el-col>

      <el-col :span="8">
        <TopList
          title="Top10受访页面"
          :data="topPages"
          :columns="[
            { prop: 'page_url', label: '受访页面' },
            { prop: 'pv', label: '浏览量(PV)' },
            { prop: 'percentage', label: '占比', type: 'percentage' }
          ]"
        />
      </el-col>
    </el-row>

    <!-- 新老访客分析 -->
    <div class="visitor-analysis">
      <el-row :gutter="20">
        <el-col :span="12">
          <PieChart
            title="新老访客"
            :data="newReturningData"
          />
        </el-col>

        <el-col :span="12">
          <MapChart
            title="地域分布"
            :data="regionData"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>
```
