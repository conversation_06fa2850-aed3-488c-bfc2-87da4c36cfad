# 数据库创建步骤指南

## 问题说明
遇到权限错误 `#1044 - Access denied for user 'root'@'%' to database 'information_schema'` 是因为您的MySQL用户权限受限，无法访问系统表。

## 解决方案

### 方法1：使用简化版SQL脚本（推荐）

我已经创建了 `database_simple.sql` 文件，避免了权限问题。

#### 步骤1：先创建数据库
```sql
CREATE DATABASE web_stats CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 步骤2：执行简化版脚本
```bash
mysql -u root -p web_stats < database_simple.sql
```

或者在MySQL客户端中：
```sql
USE web_stats;
SOURCE database_simple.sql;
```

### 方法2：手动分步执行（如果方法1仍有问题）

#### 步骤1：创建数据库
```sql
CREATE DATABASE web_stats CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE web_stats;
```

#### 步骤2：创建字典表
```sql
-- 搜索引擎字典表
CREATE TABLE search_engines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '搜索引擎名称',
    domain VARCHAR(100) COMMENT '搜索引擎域名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎字典表';

-- 关键词字典表
CREATE TABLE keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(200) UNIQUE NOT NULL COMMENT '关键词',
    keyword_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '关键词哈希值',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    total_count INT DEFAULT 1 COMMENT '总出现次数',
    INDEX idx_keyword (keyword),
    INDEX idx_keyword_hash (keyword_hash),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词字典表';

-- 浏览器字典表
CREATE TABLE browsers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '浏览器名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_browser_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览器字典表';

-- 操作系统字典表
CREATE TABLE operating_systems (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '操作系统名称',
    version VARCHAR(20) COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_os_version (name, version),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作系统字典表';

-- 地区字典表
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country VARCHAR(50) NOT NULL COMMENT '国家',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_region (country, province, city),
    INDEX idx_country (country),
    INDEX idx_province (province),
    INDEX idx_city (city)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区字典表';

-- 页面URL字典表
CREATE TABLE page_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(500) UNIQUE NOT NULL COMMENT '页面URL',
    url_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'URL哈希值',
    domain VARCHAR(100) COMMENT '域名',
    path VARCHAR(400) COMMENT '路径',
    title VARCHAR(200) COMMENT '页面标题',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
    INDEX idx_url_hash (url_hash),
    INDEX idx_domain (domain),
    INDEX idx_path (path(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面URL字典表';

-- 来源域名字典表
CREATE TABLE referer_domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(100) UNIQUE NOT NULL COMMENT '来源域名',
    domain_type ENUM('search_engine', 'social_media', 'direct', 'external') DEFAULT 'external' COMMENT '域名类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_domain_type (domain_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来源域名字典表';

-- 会话字典表
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '会话哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_hash (session_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话字典表';

-- IP地址字典表
CREATE TABLE ip_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_int INT UNSIGNED COMMENT 'IPv4地址的整数表示',
    ip_string VARCHAR(45) COMMENT '原始IP字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_int (ip_int),
    INDEX idx_ip_string (ip_string)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP地址字典表';

-- User Agent字典表
CREATE TABLE user_agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ua_hash VARCHAR(64) UNIQUE NOT NULL COMMENT 'User Agent哈希值',
    user_agent TEXT COMMENT '完整User Agent字符串',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ua_hash (ua_hash),
    INDEX idx_browser_os (browser_id, os_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User Agent字典表';
```

#### 步骤3：创建用户管理表
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 网站表
CREATE TABLE sites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    site_name VARCHAR(100) NOT NULL COMMENT '网站名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '网站URL',
    site_type VARCHAR(50) DEFAULT 'other' COMMENT '网站类型',
    region VARCHAR(50) COMMENT '网站地区',
    description TEXT COMMENT '网站描述',
    tracking_code VARCHAR(32) UNIQUE NOT NULL COMMENT '统计代码ID',
    view_password VARCHAR(50) COMMENT '查看密码',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_tracking_code (tracking_code),
    INDEX idx_domain (domain),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';

-- 访客表
CREATE TABLE visitors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_hash VARCHAR(64) NOT NULL COMMENT '访客唯一标识',
    first_visit TIMESTAMP NOT NULL COMMENT '首次访问时间',
    last_visit TIMESTAMP NOT NULL COMMENT '最后访问时间',
    total_visits INT DEFAULT 1 COMMENT '总访问次数',
    total_page_views INT DEFAULT 1 COMMENT '总页面浏览数',
    total_duration INT DEFAULT 0 COMMENT '总访问时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_site_visitor (site_id, visitor_hash),
    INDEX idx_site_id (site_id),
    INDEX idx_last_visit (last_visit)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访客表';
```

#### 步骤4：创建核心访问记录表（分区表）
```sql
-- 访问记录表（分区表，复合主键）
CREATE TABLE visits (
    id BIGINT AUTO_INCREMENT,
    site_id BIGINT NOT NULL COMMENT '网站ID',
    visitor_id BIGINT NOT NULL COMMENT '访客ID',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    ip_id INT NOT NULL COMMENT 'IP地址ID',
    user_agent_id INT COMMENT '用户代理ID',
    referer_domain_id INT COMMENT '来源域名ID',
    referer_url_id BIGINT COMMENT '来源页面URL ID',
    landing_page_id BIGINT NOT NULL COMMENT '入口页面ID',
    exit_page_id BIGINT COMMENT '退出页面ID',
    
    -- 时间字段优化
    visit_time TIMESTAMP NOT NULL COMMENT '访问时间',
    visit_date DATE NOT NULL COMMENT '访问日期',
    visit_hour TINYINT NOT NULL COMMENT '访问小时（0-23）',
    visit_year SMALLINT NOT NULL COMMENT '访问年份',
    visit_month TINYINT NOT NULL COMMENT '访问月份（1-12）',
    visit_week TINYINT NOT NULL COMMENT '访问周数（1-53）',
    visit_weekday TINYINT NOT NULL COMMENT '星期几（1-7）',
    
    visit_duration INT DEFAULT 0 COMMENT '访问时长(秒)',
    page_views INT DEFAULT 1 COMMENT '页面浏览数',
    is_new_visitor TINYINT DEFAULT 0 COMMENT '是否新访客',
    region_id INT COMMENT '地区ID',
    device_type ENUM('desktop', 'mobile', 'tablet') COMMENT '设备类型',
    browser_id INT COMMENT '浏览器ID',
    os_id INT COMMENT '操作系统ID',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    language VARCHAR(10) COMMENT '语言',
    search_engine_id INT COMMENT '搜索引擎ID',
    search_keyword_id BIGINT COMMENT '搜索关键词ID',
    utm_source VARCHAR(100) COMMENT 'UTM来源',
    utm_medium VARCHAR(100) COMMENT 'UTM媒介',
    utm_campaign VARCHAR(100) COMMENT 'UTM活动',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 复合主键（包含分区键）
    PRIMARY KEY (id, visit_year, visit_month),
    
    -- 优化索引
    INDEX idx_site_date (site_id, visit_date),
    INDEX idx_site_date_hour (site_id, visit_date, visit_hour),
    INDEX idx_visitor_date (visitor_id, visit_date),
    INDEX idx_session_date (session_id, visit_date),
    INDEX idx_analytics_basic (site_id, visit_date, device_type, is_new_visitor),
    INDEX idx_analytics_source (site_id, visit_date, search_engine_id),
    INDEX idx_covering_basic (site_id, visit_date, visitor_id, page_views, visit_duration)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问记录表'
PARTITION BY RANGE (visit_year * 100 + visit_month) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 验证创建结果

执行完成后，运行以下SQL验证：

```sql
-- 查看所有表
SHOW TABLES;

-- 查看visits表结构
SHOW CREATE TABLE visits;

-- 查看分区信息
SELECT 
    PARTITION_NAME,
    PARTITION_DESCRIPTION,
    TABLE_ROWS
FROM information_schema.PARTITIONS 
WHERE TABLE_SCHEMA = 'web_stats' 
    AND TABLE_NAME = 'visits' 
    AND PARTITION_NAME IS NOT NULL;
```

## 重要说明

1. **分区表无外键**: visits表使用分区，无法使用数据库级外键约束
2. **应用层维护**: 数据完整性需要在应用层维护
3. **复合主键**: 主键包含分区键 (id, visit_year, visit_month)
4. **性能优化**: 所有索引和分区策略保持不变

## 下一步

数据库创建完成后，就可以开始后端API开发了！
